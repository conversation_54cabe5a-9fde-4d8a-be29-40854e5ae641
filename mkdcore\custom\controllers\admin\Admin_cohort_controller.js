'use strict';

const app = require('express').Router();
const Sequelize = require('sequelize');
const logger = require('../../services/LoggingService');
let pagination = require('../../services/PaginationService');
let SessionService = require('../../services/SessionService');
let JwtService = require('../../services/JwtService');
const ValidationService = require('../../services/ValidationService');
const PermissionService = require('../../services/PermissionService');
const UploadService = require('../../services/UploadService');
const AuthService = require('../../services/AuthService');
const db = require('../../models');
const helpers = require('../../core/helpers');

const role = 1;

app.get('/admin/cohorts/:num', SessionService.verifySessionMiddleware(role, 'admin'), async function (req, res, next) {
  try {
    let session = req.session;
    let paginateListViewModel = require('../../view_models/cohort_admin_list_paginate_view_model');

    var viewModel = new paginateListViewModel(db.cohort, 'Co-Horts', session.success, session.error, '/admin/cohorts');

    const format = req.query.format ? req.query.format : 'view';
    const direction = req.query.direction ? req.query.direction : 'ASC';
    const per_page = req.query.per_page ? req.query.per_page : 10;
    let order_by = req.query.order_by ? req.query.order_by : viewModel.get_field_column()[0];
    let orderAssociations = [];
    viewModel.set_order_by(order_by);
    let joins = order_by.includes('.') ? order_by.split('.') : [];
    order_by = order_by.includes('.') ? joins[joins.length - 1] : order_by;
    if (joins.length > 0) {
      for (let i = joins.length - 1; i > 0; i--) {
        orderAssociations.push(`${joins[i - 1]}`);
      }
    }
    // Check for flash messages
    const flashMessageSuccess = req.flash('success');
    if (flashMessageSuccess && flashMessageSuccess.length > 0) {
      viewModel.success = flashMessageSuccess[0];
    }
    const flashMessageError = req.flash('error');
    if (flashMessageError && flashMessageError.length > 0) {
      viewModel.error = flashMessageError[0];
    }

    viewModel.set_id(req.query.id ? req.query.id : '');
    viewModel.set_name(req.query.name ? req.query.name : '');
    viewModel.set_parent_1(req.query.parent_1 ? req.query.parent_1 : '');
    viewModel.set_parent_2(req.query.parent_2 ? req.query.parent_2 : '');
    viewModel.set_shared_email(req.query.shared_email ? req.query.shared_email : '');
    viewModel.set_shared_phone(req.query.shared_phone ? req.query.shared_phone : '');
    viewModel.set_status(req.query.status ? req.query.status : '');

    let where = helpers.filterEmptyFields({
      id: viewModel.get_id(),
      name: { [Sequelize.Op.like]: `${viewModel.get_name()}%` },
      parent_1: viewModel.get_parent_1(),
      parent_2: viewModel.get_parent_2(),
      shared_email: { [Sequelize.Op.like]: `${viewModel.get_shared_email()}%` },
      shared_phone: viewModel.get_shared_phone(),
      status: viewModel.get_status(),
    });

    const count = await db.cohort._count(where, []);

    let sort_url = '';

    if (req.originalUrl.includes('?')) {
      if (req.originalUrl.includes('order_by')) {
        let url_query = req.originalUrl.split('?')[1];
        sort_url = `${url_query.split('order_by')[0]}`;
      } else {
        sort_url = `${req.originalUrl.split('?')[1]}`;
      }
    }
    viewModel.set_total_rows(count);
    viewModel.set_per_page(+per_page);
    viewModel.set_page(+req.params.num);
    viewModel.set_query(req.query);
    viewModel.set_sort_base_url(`/admin/cohorts/${+req.params.num}?${sort_url}`);
    viewModel.set_sort(direction);

    const list = await db.cohort.getPaginated(viewModel.get_page() - 1 < 0 ? 0 : viewModel.get_page(), viewModel.get_per_page(), where, order_by, direction, orderAssociations);

    viewModel.set_list(list);

    if (format == 'csv') {
      const csv = viewModel.to_csv();
      return res
        .set({
          'Content-Type': 'text/csv',
          'Content-Disposition': 'attachment; filename="export.csv"',
        })
        .send(csv);
    }

    // if (format != 'view') {
    //   res.json(viewModel.to_json());
    // } else {
    // }

    return res.render('admin/Cohort', viewModel);
  } catch (error) {
    console.error(error);
    viewModel.error = error.message || 'Something went wrong';
    return res.render('admin/Cohort', viewModel);
  }
});

app.get('/admin/cohorts-add', SessionService.verifySessionMiddleware(role, 'admin'), async function (req, res, next) {
  if (req.session.csrf === undefined) {
    req.session.csrf = SessionService.randomString(100);
  }

  const cohortAdminAddViewModel = require('../../view_models/cohort_admin_add_view_model');

  const viewModel = new cohortAdminAddViewModel(db.cohort, 'Add Co-Hort', '', '', '/admin/cohorts');
  viewModel.heading = 'Add Co-Hort';

  res.render('admin/Add_Cohort', viewModel);
});

app.post(
  '/admin/cohorts-add',
  SessionService.verifySessionMiddleware(role, 'admin'),
  ValidationService.validateInput(
    { name: 'required', parent_1: 'required', shared_email: 'required|email', shared_phone: 'required', status: 'required' },
    {
      'name.required': 'Name is required',
      'parent_1.required': 'Parent1 is required',
      'shared_email.required': 'SharedEmail is required',
      'shared_email.email': 'Invalid email',
      'shared_phone.required': 'SharedPhone is required',
      'status.required': 'Status is required',
    },
  ),
  async function (req, res, next) {
    if (req.session.csrf === undefined) {
      req.session.csrf = SessionService.randomString(100);
    }
    const cohortAdminAddViewModel = require('../../view_models/cohort_admin_add_view_model');

    const viewModel = new cohortAdminAddViewModel(db.cohort, 'Add Co-Hort', '', '', '/admin/cohorts');

    // TODO use separate controller for image upload
    //  {{{upload_field_setter}}}

    const { name, parent_1, parent_2, shared_email, shared_phone, status } = req.body;

    viewModel.form_fields = {
      ...viewModel.form_fields,
      name,
      parent_1,
      parent_2,
      shared_email,
      shared_phone,
      status,
    };

    try {
      if (req.validationError) {
        viewModel.error = req.validationError;
        return res.render('admin/Add_Cohort', viewModel);
      }

      viewModel.session = req.session;
      console.log(parent_2);
      if (parent_1 < 0 || parent_2 < 0) {
        viewModel.error = 'Invalid parent ID';
        return res.render('admin/Add_Cohort', viewModel);
      }

      const validPhone = /^[\+]?[(]?[0-9]{3}[)]?[-\s\.]?[0-9]{3}[-\s\.]?[0-9]{4,6}$/im;

      if (!validPhone.test(shared_phone)) {
        viewModel.error = 'Invalid phone number';
        return res.render('admin/Add_Cohort', viewModel);
      }
      const data = await db.cohort.insert({ name, parent_1, parent_2: parent_2 ? parent_2 : null, shared_email, shared_phone, status });

      if (!data) {
        viewModel.error = 'Something went wrong';
        return res.render('admin/Add_Cohort', viewModel);
      }

      await db.activity_log.insert({
        action: 'ADD',
        name: 'Admin_cohort_controller.js',
        portal: 'admin',
        data: JSON.stringify({ name, parent_1, parent_2, shared_email, shared_phone, status }),
      });

      req.flash('success', 'Cohort created successfully');
      return res.redirect('/admin/cohorts/0');
    } catch (error) {
      console.error(error);
      viewModel.error = error.message || 'Something went wrong';
      return res.render('admin/Add_Cohort', viewModel);
    }
  },
);

app.get('/admin/cohorts-edit/:id', SessionService.verifySessionMiddleware(role, 'admin'), async function (req, res, next) {
  let id = req.params.id;
  if (req.session.csrf === undefined) {
    req.session.csrf = SessionService.randomString(100);
  }
  const cohortAdminEditViewModel = require('../../view_models/cohort_admin_edit_view_model');

  const viewModel = new cohortAdminEditViewModel(db.cohort, 'Edit Co-Hort', '', '', '/admin/cohorts');
  viewModel.heading = 'Edit Co-Hort';

  try {
    const exists = await db.cohort.getByPK(id);

    if (!exists) {
      req.flash('error', 'Cohort not found');
      return res.redirect('/admin/cohorts/0');
    }
    const values = exists;
    Object.keys(viewModel.form_fields).forEach((field) => {
      viewModel.form_fields[field] = values[field] || '';
    });

    return res.render('admin/Edit_Cohort', viewModel);
  } catch (error) {
    console.error(error);
    viewModel.error = error.message || 'Something went wrong';
    return res.render('admin/Edit_Cohort', viewModel);
  }
});

app.post(
  '/admin/cohorts-edit/:id',
  SessionService.verifySessionMiddleware(role, 'admin'),
  ValidationService.validateInput(
    { name: 'required', parent_1: 'required', status: 'required' },
    { 'name.required': 'Name is required', 'parent_1.required': 'Parent1 is required', 'status.required': 'Status is required' },
  ),
  async function (req, res, next) {
    let id = req.params.id;
    if (req.session.csrf === undefined) {
      req.session.csrf = SessionService.randomString(100);
    }

    const cohortAdminEditViewModel = require('../../view_models/cohort_admin_edit_view_model');

    const viewModel = new cohortAdminEditViewModel(db.cohort, 'Edit cohort', '', '', '/admin/cohorts');

    const { name, parent_1, parent_2, shared_email, shared_phone, status } = req.body;

    viewModel.form_fields = {
      ...viewModel.form_fields,
      name,
      parent_1,
      parent_2,
      shared_email,
      shared_phone,
      status,
    };

    delete viewModel.form_fields.id;

    try {
      if (req.validationError) {
        viewModel.error = req.validationError;
        return res.render('admin/Edit_Cohort', viewModel);
      }

      const resourceExists = await db.cohort.getByPK(id);
      if (!resourceExists) {
        req.flash('error', 'Cohort not found');
        return res.redirect('/admin/cohorts/0');
      }

      viewModel.session = req.session;

      let data = await db.cohort.edit({ name, parent_1, parent_2: parent_2 ? parent_2 : null, shared_email, shared_phone, status }, id);
      if (!data) {
        viewModel.error = 'Something went wrong';
        return res.render('admin/Edit_Cohort', viewModel);
      }

      await db.activity_log.insert({
        action: 'EDIT',
        name: 'Admin_cohort_controller.js',
        portal: 'admin',
        data: JSON.stringify({ name, parent_1, parent_2, shared_email, shared_phone, status }),
      });

      req.flash('success', 'Cohort edited successfully');

      return res.redirect('/admin/cohorts/0');
    } catch (error) {
      console.error(error);
      viewModel.error = error.message || 'Something went wrong';
      return res.render('admin/Edit_Cohort', viewModel);
    }
  },
);

app.get(
  '/admin/cohorts-view/:id',
  SessionService.verifySessionMiddleware(role, 'admin'),

  async function (req, res, next) {
    try {
      let id = req.params.id;

      const cohortAdminDetailViewModel = require('../../view_models/cohort_admin_detail_view_model');

      var viewModel = new cohortAdminDetailViewModel(db.cohort, 'Co-Hort details', '', '', '/admin/cohorts');
      viewModel.heading = 'Co-Hort details';

      const data = await db.cohort.getByPK(id);
      data.status = db.cohort.status_mapping()[data.status];

      if (!data) {
        viewModel.error = 'Cohort not found';
        viewModel.detail_fields = { ...viewModel.detail_fields, name: 'N/A', parent_1: 'N/A', parent_2: 'N/A', shared_email: 'N/A', shared_phone: 'N/A', status: 'N/A' };
      } else {
        viewModel.detail_fields = {
          ...viewModel.detail_fields,
          name: data['name'] || 'N/A',
          parent_1: data['parent_1'] || 'N/A',
          parent_2: data['parent_2'] || 'N/A',
          shared_email: data['shared_email'] || 'N/A',
          shared_phone: data['shared_phone'] || 'N/A',
          status: data['status'] || 'N/A',
        };
      }

      res.render('admin/View_Cohort', viewModel);
    } catch (error) {
      console.error(error);
      viewModel.error = error.message || 'Something went wrong';
      viewModel.detail_fields = { ...viewModel.detail_fields, name: 'N/A', parent_1: 'N/A', parent_2: 'N/A', shared_email: 'N/A', shared_phone: 'N/A', status: 'N/A' };
      res.render('admin/View_Cohort', viewModel);
    }
  },
);

app.get('/admin/cohorts-delete/:id', SessionService.verifySessionMiddleware(role, 'admin'), async function (req, res, next) {
  let id = req.params.id;

  const cohortAdminDeleteViewModel = require('../../view_models/cohort_admin_delete_view_model');

  const viewModel = new cohortAdminDeleteViewModel(db.cohort);

  try {
    const exists = await db.cohort.getByPK(id);

    if (!exists) {
      req.flash('error', 'Cohort not found');
      return res.redirect('/admin/cohorts/0');
    }

    viewModel.session = req.session;

    await db.cohort.realDelete(id);

    await db.activity_log.insert({
      action: 'DELETE',
      name: 'Admin_cohort_controller.js',
      portal: 'admin',
      data: JSON.stringify(exists),
    });

    req.flash('success', 'Cohort deleted successfully');

    return res.redirect('/admin/cohorts/0');
  } catch (error) {
    console.error(error);
    req.flash('error', error.message || 'Something went wrong');
    return res.redirect('/admin/cohorts/0');
  }
});

// APIS

app.get('/admin/api/cohorts', JwtService.verifyTokenMiddleware(role), async function (req, res, next) {
  try {
    const user_id = req.user_id;
    const session = req.session;
    let listViewModel = require('../../view_models/cohort_admin_list_paginate_view_model');
    let viewModel = new listViewModel(db.cohort, 'Co-Horts', session.success, session.error, '/admin/cohorts');
    const direction = req.query.direction ? req.query.direction : 'ASC';
    const page = req.query.page ? parseInt(req.query.page) : 1;
    const limit = req.query.limit ? parseInt(req.query.limit) : 10;
    const offset = (page - 1) * limit;
    let order_by = req.query.order_by ? req.query.order_by : viewModel.get_field_column()[0];
    let orderAssociations = [];
    viewModel.set_order_by(order_by);
    let joins = order_by.includes('.') ? order_by.split('.') : [];
    order_by = order_by.includes('.') ? joins[joins.length - 1] : order_by;
    if (joins.length > 0) {
      for (let i = joins.length - 1; i > 0; i--) {
        orderAssociations.push(`${joins[i - 1]}`);
      }
    }

    viewModel.set_id(req.query.id ? req.query.id : '');
    viewModel.set_name(req.query.name ? req.query.name : '');
    viewModel.set_parent_1(req.query.parent_1 ? req.query.parent_1 : '');
    viewModel.set_parent_2(req.query.parent_2 ? req.query.parent_2 : '');
    viewModel.set_shared_email(req.query.shared_email ? req.query.shared_email : '');
    viewModel.set_shared_phone(req.query.shared_phone ? req.query.shared_phone : '');
    viewModel.set_status(req.query.status ? req.query.status : '');

    let where = helpers.filterEmptyFields({
      id: viewModel.get_id(),
      name: viewModel.get_name(),
      parent_1: viewModel.get_parent_1(),
      parent_2: viewModel.get_parent_2(),
      shared_email: viewModel.get_shared_email(),
      shared_phone: viewModel.get_shared_phone(),
      status: viewModel.get_status(),
    });

    let include = [];

    const { rows: allItems, count } = await db.cohort.findAndCountAll({
      where: where,
      limit: limit == 0 ? null : limit,
      offset: offset,
      include: include,
      distinct: true,
    });

    const response = {
      items: allItems,
      page,
      nextPage: count > offset + limit ? page + 1 : false,
      retrievedCount: allItems.length,
      fullCount: count,
    };

    return res.status(201).json({ success: true, data: response });
  } catch (error) {
    console.error(error);
    return res.status(500).json({ success: false, message: error.message || 'Something went wrong' });
  }
});

app.post(
  '/admin/api/cohorts-add',
  JwtService.verifyTokenMiddleware(role),
  ValidationService.validateInput(
    { name: 'required', parent_1: 'required', shared_email: 'required|email', shared_phone: 'required', status: 'required' },
    {
      'name.required': 'Name is required',
      'parent_1.required': 'Parent1 is required',
      'shared_email.required': 'SharedEmail is required',
      'shared_email.email': 'Invalid email',
      'shared_phone.required': 'SharedPhone is required',
      'status.required': 'Status is required',
    },
  ),
  async function (req, res, next) {
    const cohortAdminAddViewModel = require('../../view_models/cohort_admin_add_view_model');

    const viewModel = new cohortAdminAddViewModel(db.cohort);

    const { name, parent_1, parent_2, shared_email, shared_phone, status } = req.body;
    try {
      if (req.validationError) {
        return res.status(500).json({ success: false, message: req.validationError });
      }

      const data = await db.cohort.insert({ name, parent_1, parent_2, shared_email, shared_phone, status });

      if (!data) {
        return res.status(500).json({ success: false, message: 'Something went wrong' });
      }

      await db.activity_log.insert({
        action: 'ADD',
        name: 'Admin_cohort_controller.js',
        portal: 'admin',
        data: JSON.stringify({ name, parent_1, parent_2, shared_email, shared_phone, status }),
      });

      return res.status(201).json({ success: true, message: 'Cohort created successfully' });
    } catch (error) {
      return res.status(500).json({ success: false, message: 'Something went wrong' });
    }
  },
);

app.put(
  '/admin/api/cohorts-edit/:id',
  JwtService.verifyTokenMiddleware(role),
  ValidationService.validateInput(
    { name: 'required', parent_1: 'required', status: 'required' },
    { 'name.required': 'Name is required', 'parent_1.required': 'Parent1 is required', 'status.required': 'Status is required' },
  ),
  async function (req, res, next) {
    let id = req.params.id;

    const cohortAdminEditViewModel = require('../../view_models/cohort_admin_edit_view_model');

    const viewModel = new cohortAdminEditViewModel(db.cohort);

    const { name, parent_1, parent_2, shared_email, shared_phone, status } = req.body;

    try {
      if (req.validationError) {
        return res.status(500).json({ success: false, message: req.validationError });
      }

      const resourceExists = await db.cohort.getByPK(id);
      if (!resourceExists) {
        return res.status(404).json({ success: false, message: 'Cohort not found' });
      }

      const data = await db.cohort.edit({ name, parent_1, parent_2, shared_email, shared_phone, status }, id);

      if (!data) {
        return res.status(500).json({ success: false, message: 'Something went wrong' });
      }

      await db.activity_log.insert({
        action: 'EDIT',
        name: 'Admin_cohort_controller.js',
        portal: 'admin',
        data: JSON.stringify({ name, parent_1, parent_2, shared_email, shared_phone, status }),
      });

      return res.json({ success: true, message: 'Cohort edited successfully' });
    } catch (error) {
      return res.status(500).json({ success: false, message: 'Something went wrong' });
    }
  },
);

app.get('/admin/api/cohorts-view/:id', JwtService.verifyTokenMiddleware(role), async function (req, res, next) {
  let id = req.params.id;

  const cohortAdminDetailViewModel = require('../../view_models/cohort_admin_detail_view_model');

  const viewModel = new cohortAdminDetailViewModel(db.cohort);

  try {
    const data = await db.cohort.getByPK(id);

    if (!data) {
      return res.status(404).json({ message: 'Cohort not found', data: null });
    } else {
      const fields = {
        ...viewModel.detail_fields,
        name: data['name'] || '',
        parent_1: data['parent_1'] || '',
        parent_2: data['parent_2'] || '',
        shared_email: data['shared_email'] || '',
        shared_phone: data['shared_phone'] || '',
        status: data['status'] || '',
      };
      return res.status(200).json({ data: fields });
    }
  } catch (error) {
    return res.status(404).json({ message: 'Something went wrong', data: null });
  }
});

app.delete('/admin/api/cohorts-delete/:id', JwtService.verifyTokenMiddleware(role), async function (req, res, next) {
  let id = req.params.id;

  const cohortAdminDeleteViewModel = require('../../view_models/cohort_admin_delete_view_model');

  const viewModel = new cohortAdminDeleteViewModel(db.cohort);

  try {
    const exists = await db.cohort.getByPK(id);

    if (!exists) {
      return res.status(404).json({ success: false, message: 'Cohort not found' });
    }

    await db.cohort.realDelete(id);

    await db.activity_log.insert({
      action: 'DELETE',
      name: 'Admin_cohort_controller.js',
      portal: 'admin',
      data: JSON.stringify(exists),
    });

    return res.status(200).json({ success: true, message: 'Cohort deleted successfully' });
  } catch (error) {
    return res.status(500).json({ success: false, message: 'Something went wrong' });
  }
});

module.exports = app;
