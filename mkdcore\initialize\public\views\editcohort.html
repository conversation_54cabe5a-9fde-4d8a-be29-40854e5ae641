<!DOCTYPE html>
<html>

<head>
  <!-- Required meta tags-->
  <meta charset="utf-8" />
  <meta name="viewport"
    content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no, viewport-fit=cover" />
  <meta name="apple-mobile-web-app-capable" content="yes" />
  <!-- Color theme for statusbar (Android only) -->
  <meta name="theme-color" content="#2196f3" />
  <!-- Your app title -->
  <title>Co-Parent</title>
</head>

<body>
  <!-- App root element -->
  <div id="app">
    <div class="page">
      <!-- Top Navbar -->
      <div class="navbar">
        <div class="navbar-bg"></div>
        <div class="navbar-inner">
          <div class="left">
            <a class="link back">
              <i class="icon icon-back color-black"></i>
              <span class="if-not-md">Back</span>
            </a>
          </div>
          <div class="title">Add Co-Hort</div>
          <div class="right">
            <a href="" onclick="addCohort()"><i class="f7-icons color-black">checkmark_alt</i></a>
          </div>
        </div>
      </div>

      <!-- Scrollable page content -->
      <div class="page-content no-swipe-panel">
        <div class="list no-hairlines">
          <ul>
            <li class="item-content item-input item-input-outline">
              <div class="item-inner">
                <div class="item-title item-label">Parent 1</div>
                <div class="item-input-wrap">
                  <input type="text" name="parent-1" id="edit-cohort-parent1" />
                  <span class="input-clear-button"></span>
                </div>
              </div>
            </li>

            <li class="item-content item-input item-input-outline">
              <div class="item-inner">
                <div class="item-title item-label">Parent 2</div>
                <div class="item-input-wrap">
                  <input type="text" name="parent-2" id="edit-cohort-parent2" />
                  <span class="input-clear-button"></span>
                </div>
              </div>
            </li>

            <li class="item-content item-input item-input-outline">
              <div class="item-inner">
                <div class="item-title item-label">Email</div>
                <div class="item-input-wrap">
                  <input type="email" name="email" id="edit-cohort-email" />
                  <span class="input-clear-button"></span>
                </div>
              </div>
            </li>

            <li class="item-content item-input item-input-outline">
              <div class="item-inner">
                <div class="item-title item-label">Phone</div>
                <div class="item-input-wrap">
                  <input type="tel" name="phone" id="edit-cohort-phone" />
                  <span class="input-clear-button"></span>
                </div>
              </div>
            </li>

            <li class="item-content item-input item-input-outline">
              <div class="item-inner">
                <div class="item-title item-label">Parent 1 Default Split</div>
                <div class="item-input-wrap">
                  <input type="number" name="parent-1-split" id="edit-cohort-parent1split" />
                  <span class="input-clear-button"></span>
                </div>
              </div>
            </li>

            <li class="item-content item-input item-input-outline">
              <div class="item-inner">
                <div class="item-title item-label">Parent 2 Default Split</div>
                <div class="item-input-wrap">
                  <input type="number" name="parent-1-split" id="edit-cohort-parent2split" />
                  <span class="input-clear-button"></span>
                </div>
              </div>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</body>

</html>