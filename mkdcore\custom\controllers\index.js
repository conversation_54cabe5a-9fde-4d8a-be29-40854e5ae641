const AdminUserController = require('./admin/Admin_user_controller');
const AdminCohortController = require('./admin/Admin_cohort_controller');
const AdminSwapController = require('./admin/Admin_swap_controller');
const AdminExpenseController = require('./admin/Admin_expense_controller');
const AdminChildrenController = require('./admin/Admin_children_controller');
const AdminEmailsController = require('./admin/Admin_emails_controller');
const AdminCallForwardController = require('./admin/Admin_call_forward_controller');
const AdminSmsForwardController = require('./admin/Admin_sms_forward_controller');
const AdminEmailForwardController = require('./admin/Admin_email_forward_controller');
const AdminSettleAmountController = require('./admin/Admin_settle_amount_controller');
const AdminNotificationLogController = require('./admin/Admin_notification_log_controller');
const AdminFilesController = require('./admin/Admin_files_controller');
const AdminDashboard = require('./admin/Dashboard');
const AdminInvoices = require('./admin/Admin_stripe_invoice_controller.js');
const MemberDashboard = require('./member/Dashboard');
const MemberAuth = require('./member/Member_auth_controller.js');
const MemberFiles = require('./member/Member_files_controller.js');
const MemberInvite = require('./member/Member_invite_controller.js');
const MemberSwaps = require('./member/Member_swaps_controller.js');
const MemberExpense = require('./member/Member_expense');
const MemberCohort = require('./member/Member_cohort');
const MemberPhones = require('./member/Member_phone_controller.js');
const MemberEmail = require('./member/Member_email_controller.js');
const MemberChild = require('./member/Member_child_controller.js');
const MemberSettle = require('./member/Member_settlement.js');

module.exports = [
  AdminUserController,
  AdminCohortController,
  AdminSwapController,
  AdminExpenseController,
  AdminChildrenController,
  AdminEmailsController,
  AdminCallForwardController,
  AdminSmsForwardController,
  AdminEmailForwardController,
  AdminSettleAmountController,
  AdminNotificationLogController,
  AdminFilesController,
  AdminDashboard,
  MemberDashboard,
  MemberAuth,
  MemberFiles,
  MemberInvite,
  MemberSwaps,
  MemberExpense,
  MemberCohort,
  MemberPhones,
  MemberEmail,
  MemberChild,
  MemberSettle,
  AdminInvoices,
];
