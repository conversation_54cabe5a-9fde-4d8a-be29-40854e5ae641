<!DOCTYPE html>
<html>
  <head>
    <!-- Required meta tags-->
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no, viewport-fit=cover" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <!-- Color theme for statusbar (Android only) -->
    <meta name="theme-color" content="#2196f3" />

    <title>Co-Parent</title>

    <link rel="stylesheet" href="../css/framework7-bundle.min.css" />
  </head>

  <body>
    <!-- App root element -->
    <div id="app">
      <div class="page" data-name="sharedEmail">
        <!-- Top Navbar -->
        <div class="navbar">
          <div class="navbar-bg"></div>
          <div class="navbar-inner">
            <div class="left"></div>
            <div class="title text-align-center">Select Co-Email</div>
            <div class="right"></div>
          </div>
        </div>

        <!-- Scrollable page content -->
        <div class="page-content no-swipe-panel">
          <div class="list no-hairlines">
            <ul>
              <li>
                <div class="item-content item-input item-input-outline">
                  <div class="item-inner">
                    <div class="item-title item-label">Choose Co-Email Alias</div>
                    <div class="item-input-wrap">
                      <input type="text" name="sharedEmail" id="shared-email" placeholder="Enter Alias" />
                      <span class="input-clear-button"></span>
                      <span class="domainurl">@cphsend.com</span>
                    </div>
                  </div>
                </div>
              </li>

              <li>
                <button class="email-selection-button button button-raised button-fill margin-vertical" id="email-selection-button" onclick="choseEmail()">Choose Co-Email</button>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>
