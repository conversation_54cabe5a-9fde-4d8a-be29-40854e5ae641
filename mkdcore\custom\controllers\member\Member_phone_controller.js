const app = require('express').Router();
const sequelize = require('sequelize');
const { Op } = require('sequelize');
const db = require('../../models');
const { protect } = require('../../middlewares/auth_middleware.js');
const { getAvailableNumbers, orderNumber, getRecordings } = require('../../services/TelnyxService');
const Twilio = require('twilio');
const client = require('twilio')('**********************************', '75a36786da8db0a4866f118deeebb0ba');
const sgMail = require('@sendgrid/mail');
const MailService = require('../../services/MailService');

app.get('/api/user/available_phones', protect, async function (req, res, next) {
  try {
    const { AreaCode } = req.query;
    const phones = await getAvailableNumbers(AreaCode);
    res.status(200).json({
      status: 'success',
      phones: phones.data.available_phone_numbers,
    });
  } catch (error) {
    return res.status(400).json({
      error: true,
      message: error.message,
    });
  }
});

app.post('/api/user/order_phone/:cohort_id', protect, async function (req, res, next) {
  try {
    const user = req.user;
    const { phone_number } = req.body;
    const { cohort_id } = req.params;
    const belongToCohort = await db.cohort.findOne({
      where: {
        [Op.or]: {
          parent_1: user.id,
          parent_2: user.id,
        },
        id: cohort_id,
      },
    });
    if (!belongToCohort) {
      return res.status(404).json({
        status: 'fail',
        message: 'Cohort not found',
      });
    }
    if (belongToCohort.shared_phone) {
      return res.status(401).json({
        status: 'fail',
        message: 'This cohort has already a shared phone',
      });
    }
    await orderNumber(phone_number, req);
    await db.cohort.edit({ shared_phone: phone_number }, cohort_id);
    res.status(200).json({
      status: 'success',
      shared_phone: phone_number,
    });
  } catch (error) {
    console.log(error);
    return res.status(400).json({
      error: true,
      message: error.message,
    });
  }
});

app.post('/api/user/calling', async function (req, res, next) {
  try {
    console.log(req.body);
    const cohort = await db.cohort.findOne({
      where: { shared_phone: req.body.Called },
      include: [
        {
          model: db.user,
          required: false,
          as: 'parent1',
        },
        {
          model: db.user,
          required: false,
          as: 'parent2',
        },
      ],
    });
    const { From } = req.body;
    if (From == cohort?.parent1?.phone || From == cohort?.parent2?.phone) {
      return;
    }

    const limitMonth = await db.calls_list.findOne({
      where: { [Op.and]: [sequelize.where(sequelize.fn('month', sequelize.col('created_at')), new Date().getMonth() + 1), { cohort_id: cohort.id }] },
      attributes: ['cohort_id', 'created_at', [sequelize.fn('sum', sequelize.col('duration')), 'sumDuration']],
      group: ['cohort_id', sequelize.fn('month', sequelize.col('created_at'))],
    });
    console.log(limitMonth);
    let response = '';
    if (limitMonth?.dataValues && limitMonth?.dataValues?.sumDuration * 1 >= 12000) {
      response = `<?xml version="1.0" encoding="UTF-8"?><Response><Say voice="woman">This call cannot be completed right now, try again later</Say></Response>`;
    } else {
      response = `<?xml version="1.0" encoding="UTF-8"?><Response><Say voice="woman">The parents you're trying to reach are using Co-Parent Hub. Please hold while we try to connect you to them. This call will be recorded.</Say><Dial record="record-from-ringing-dual" recordingStatusCallback="https://myapp.com/recording-events" recordingStatusCallbackEvent="in-progress completed absent" ><Number>${
        cohort.parent1.phone
      }</Number>${cohort.parent2 ? `<Number>${cohort.parent2.phone}</Number>` : ''}</Dial></Response>`;
    }

    res.type('text/xml');
    res.send(response.toString());
  } catch (error) {
    console.log(error);
  }
});
app.post('/api/user/message', async function (req, res, next) {
  try {
    const { Body, To, From } = req.body;
    const cohort = await db.cohort.findOne({
      where: { shared_phone: To },
      include: [
        {
          model: db.user,
          required: false,
          as: 'parent1',
        },
        {
          model: db.user,
          required: false,
          as: 'parent2',
        },
      ],
    });

    const limitMonth = await db.calls_list.findOne({
      where: {
        [Op.and]: [
          sequelize.where(sequelize.fn('month', sequelize.col('created_at')), new Date().getMonth() + 1),
          sequelize.where(sequelize.fn('year', sequelize.col('created_at')), new Date().getFullYear()),
          { cohort_id: cohort.id },
        ],
      },
      attributes: ['cohort_id', 'created_at', [sequelize.fn('sum', sequelize.col('duration')), 'sumDuration']],
      group: ['cohort_id', sequelize.fn('month', sequelize.col('created_at'))],
    });
    const extraMinutes = await db.extra_minutes.findOne({
      where: {
        [Op.and]: [
          sequelize.where(sequelize.fn('month', sequelize.col('created_at')), new Date().getMonth() + 1),
          sequelize.where(sequelize.fn('year', sequelize.col('created_at')), new Date().getFullYear()),
          { cohort_id: cohort.id },
        ],
      },
      attributes: [[sequelize.fn('sum', sequelize.col('amount')), 'amount']],
    });
    const extraAmount = extraMinutes?.dataValues?.amount * 60 || 0;
    if (limitMonth && limitMonth.dataValues && limitMonth.dataValues.sumDuration * 1 >= 12000 + extraAmount) {
      return;
    }
    await db.calls_list.insert({ cohort_id: cohort.id, duration: 120 });

    let response = `<?xml version="1.0" encoding="UTF-8"?><Response><Message to='${cohort?.parent1?.phone}'> Message From : ${From} \n ${Body}</Message>${
      cohort?.parent2?.phone ? `<Message to='${cohort?.parent2?.phone}'> Message From ${From} : \n ${Body}</Message>` : ''
    }</Response>`;

    await db.sms_forward.insert({ cohort_id: cohort.id, from: From, to: To, status: 0, date: new Date() });
    await db.notification_log.insert({ to: To, name: 'Forwarded SMS', description: `Forwarded SMS From ${From} To ${To} in Cohort ${cohort.id}`, status: 0, date: new Date() });

    res.type('text/xml');
    res.send(response.toString());
  } catch (error) {
    console.log(error);
  }
});

app.post('/api/user/calling/status', async function (req, res, next) {
  try {
    const { CallStatus, To, CallSid, From, CallDuration } = req.body;
    if (CallStatus === 'completed') {
      const recordings = await getRecordings(CallSid);
      let recordingLinks = '';
      recordings.forEach(({ uri }) => {
        recordingLinks += `<a href="https://api.twilio.com/${uri.replace('.json', '.mp3')}" download>https://api.twilio.com/${uri.replace('.json', '.mp3')}</a> `;
      });
      const cohort = await db.cohort.findOne({
        where: { shared_phone: To },
        include: [
          {
            model: db.user,
            required: false,
            as: 'parent1',
            include: [{ model: db.credential, as: 'credential' }],
          },
          {
            model: db.user,
            required: false,
            as: 'parent2',
            include: [{ model: db.credential, as: 'credential' }],
          },
        ],
      });
      console.log(cohort.id);
      await db.calls_list.insert({ cohort_id: cohort.id, duration: CallDuration });
      if (cohort.parent1.credential.email) {
        sgMail.setApiKey('*********************************************************************');
        const mailTemplate = await MailService.template('call-record');
        console.log(cohort.shared_email);
        MailService.initialize({
          from: '<EMAIL>',
          to: cohort.parent1.credential.email,
        });

        const injectedMailTemplate = MailService.inject(
          {
            body: mailTemplate.html,
            subject: mailTemplate.subject,
          },
          {
            from: From,
            to: To,
            link: recordingLinks,
          },
        );
        await sgMail.send(injectedMailTemplate);
      }

      if (cohort.parent2) {
        sgMail.setApiKey('*********************************************************************');
        const mailTemplate = await MailService.template('call-record');
        console.log(cohort.shared_email);
        MailService.initialize({
          from: '<EMAIL>',
          to: cohort.parent2.credential.email,
        });

        const injectedMailTemplate = MailService.inject(
          {
            body: mailTemplate.html,
            subject: mailTemplate.subject,
          },
          {
            from: From,
            to: To,
            link: recordingLinks,
          },
        );
        await sgMail.send(injectedMailTemplate);
      }

      await db.call_forward.insert({ cohort_id: cohort.id, from: From, to: To, status: 0, date: new Date() });
      await db.notification_log.insert({ to: To, name: 'Forwarded Call', description: `Forwarded Call From ${From} To ${To} in Cohort ${cohort.id}`, status: 0, date: new Date() });
      await db.file.insert({
        upload_by: cohort.parent_1,
        cohort_id: cohort.id,
        notes: `Forwarded Call From ${From} To ${To}`,
        file_url: 'https://api.twilio.com/' + recordings[0]?.uri?.replace('.json', '.mp3'),
        date: new Date(),
      });
    }
  } catch (error) {
    console.log(error);
  }
});

module.exports = app;
