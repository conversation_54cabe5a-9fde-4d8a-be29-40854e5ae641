<!DOCTYPE html>
<html>

<head>
    <!-- Required meta tags-->
    <meta charset="utf-8">
    <meta name="viewport"
        content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no, viewport-fit=cover">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <!-- Color theme for statusbar (Android only) -->
    <meta name="theme-color" content="#2196f4">
    <!-- Your app title -->
    <title>Co-Parent</title>
</head>

<body>

    <!-- App root element -->
    <div id="app">
        <div class="page" data-name="profile">

            <!-- Top Navbar -->
            <div class="navbar">
                <div class="navbar-bg"></div>
                <div class="navbar-inner">
                    <div class="left">
                        <a href="#" class="panel-toggle color-black">&#9776;</a>
                    </div>
                    <div class="title text-align-center">
                        Profile
                    </div>
                    <div class="right">
                        <a href="" onclick="updateProfile()">
                            <i class="f7-icons color-black">checkmark_alt</i>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Scrollable page content -->
            <div class="page-content">
                <div class="list no-hairlines">
                    <ul>

                        <div class="row profile-header-row">
                            <div class="col-100 profile-header">
                                <div class="circle">

                                </div>
                                <div class="profile-namecontainer" id="profile-namecontainer">
                                    <p id="profile-namecontainer-text" class="profile-namecontainer-text">Your Name</p>
                                </div>
                            </div>
                        </div>

                        <li class="item-content item-input item-input-outline">
                            <div class="item-inner">
                                <div class="item-title item-label">Email</div>
                                <div class="item-input-wrap">
                                    <input type="email" name="email" id="profile-email" required validate
                                        data-validate-on-blur="true" />
                                    <span class="input-clear-button"></span>
                                </div>
                            </div>
                        </li>


                        <li class="item-content item-input item-input-outline">
                            <div class="item-inner">
                                <div class="item-title item-label">First Name</div>
                                <div class="item-input-wrap">
                                    <input type="text" name="name" id="profile-fname" required validate
                                        data-validate-on-blur="true" />
                                    <span class="input-clear-button"></span>
                                </div>
                            </div>
                        </li>

                        <li class="item-content item-input item-input-outline">
                            <div class="item-inner">
                                <div class="item-title item-label">Last Name</div>
                                <div class="item-input-wrap">
                                    <input type="text" name="name" id="profile-lname" required validate
                                        data-validate-on-blur="true" />
                                    <span class="input-clear-button"></span>
                                </div>
                            </div>
                        </li>


                        <li class="item-content item-input item-input-outline">
                            <div class="item-inner">
                                <div class="item-title item-label">Password</div>
                                <div class="item-input-wrap">
                                    <input type="password" name="password" id="profile-password" />
                                    <span class="input-clear-button"></span>
                                </div>
                            </div>
                        </li>


                        <li class="item-content item-input item-input-outline">
                            <div class="item-inner">
                                <div class="item-title item-label">Phone</div>
                                <div class="item-input-wrap">
                                    <input type="tel" id="profile-phone" name="register-phone"
                                        placeholder="************" required validate data-validate-on-blur="true"
                                        data-error-message="Please enter number in the format 'xxx xxx xxxx'"
                                        pattern="\(?(\d{3})\)?[-\.\s]?(\d{3})[-\.\s]?(\d{4})" />
                                    <span class="input-clear-button"></span>
                                </div>
                            </div>
                        </li>

                        <!-- <li class="item-content item-input item-input-outline toggle-container">
                            <div class="item-inner flex-direction-row">
                                <span>Notify SMS</span>
                                <label class="toggle toggle-init color-green">
                                    <input type="checkbox" />
                                    <span class="toggle-icon"></span>
                                </label>
                            </div>
                        </li> -->

                    </ul>
                </div>
            </div>

        </div>

</body>

</html>