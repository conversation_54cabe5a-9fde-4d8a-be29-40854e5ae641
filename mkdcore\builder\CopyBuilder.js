/*Powered By: Manaknightdigital Inc. https://manaknightdigital.com/ Year: 2020*/
/**
 * Copy builder
 * @copyright 2020 Manaknightdigital Inc.
 * @link https://manaknightdigital.com
 * @license Proprietary Software licensing
 * <AUTHOR>
 *
 */

const fs = require('fs');
const path = require('path');
const Builder = require('./Builder');

module.exports = function (config) {
  this._config = config;
  this._copy = {};
  this._translations = [];
  this._render_list = [];
  Builder.call(this);

  this.set_copy = function (copy) {
    this._copy = copy;
  };

  this.build = function () {
    for (const key in this._copy) {
      if (this._copy.hasOwnProperty(key)) {
        let value = this._copy[key];
        try {
          if (fs.existsSync(key)) {
            let content = fs.readFileSync(key, 'utf8');
            content = this.inject_substitute(content, 'qwertyuiop', '');
            this.createDirectoriesRecursiveV2(path.join(__dirname, value));
            fs.writeFileSync(path.join(__dirname, value), content, {
              mode: 0775,
            });
          } else {
            console.error('COPY BUILDER FILE NOT EXIST ' + key);
          }
        } catch (err) {
          console.error('Copy Builder Build Error', err);
        }
      }
    }
  };

  this.destroy = function () {
    for (const key in this._copy) {
      if (this._copy.hasOwnProperty(key)) {
        let value = this._copy[key];
        try {
          if (fs.existsSync(key)) {
            fs.unlinkSync(value);
          }
        } catch (err) {
          console.error('Copy Builder Destroy Error', err);
        }
      }
    }
  };
};
