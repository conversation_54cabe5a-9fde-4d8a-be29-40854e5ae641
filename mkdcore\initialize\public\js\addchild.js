async function createChild() {
  var mainview = app.view.main;
  let cohortID = store.getters.cohortID.value;
  let childName = document.getElementById('addchild-childname').value;
  // let childAge = document.getElementById('addchild-childage').value;
  try {
    app.preloader.show();
    if (addChildValidateFields(childName)) {
      let body = {
        name: childName,
        age: 10,
      };
      let response = await fetch(`/api/user/children/cohort/${cohortID}`, {
        method: 'POST',
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
      });
      let resJSON = await response.json();
      if (response.status == 200) {
        app.preloader.hide();
        app.dialog.alert("Child added successfully");
        store.dispatch('setChildName', childName);
        mainview.router.navigate({ name: 'inviteCoParent' });
      }
      if (response.status == 404) {
        app.preloader.hide();
        app.dialog.alert("Co-Hort not found");
      }
    }
  } catch (error) {
    app.preloader.hide();
    app.dialog.alert(error);
  }
}

function addChildValidateFields(name) {
  var AllFieldsValidated = true;
  var errortoThrow = 'These field(s) are required: <br>';
  if (name == '') {
    errortoThrow += 'Child Name <br>';
    AllFieldsValidated = false;
  }
  // if (age == '') {
  //   errortoThrow += 'Child Age <br>';
  //   AllFieldsValidated = false;
  // }
  // if (parseInt(age) <= 0) {
  //   errortoThrow = 'Please enter valid child age';
  //   AllFieldsValidated = false;
  // }
  if (AllFieldsValidated == false) {
    throw errortoThrow;
  }
  return AllFieldsValidated;
}
