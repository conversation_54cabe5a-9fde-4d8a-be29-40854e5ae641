'use strict';

const { nanoid } = require('nanoid');
const csv = require('fast-csv');
const html_to_pdf = require('html-pdf-node');

const SessionService = require('../../services/SessionService');
const uploader = require('../tools/file/uploader');
const db = require('../../models');

module.exports = {
  initializeApi: function (app) {
    app.post('admin/image/s3_upload', SessionService.verifySessionMiddleware(role), async function (req, res, next) {
      try {
        const imageKey = nanoid();

        const upload = uploader.s3_upload({}, imageKey).single('image');

        upload(req, res, function (error) {
          if (error) {
            throw new Error(error);
          }
        });

        const imageUrl = imageKey;

        await db.image.insert({ url: imageUrl });

        res.status(201).json({
          success: true,
          message: 'Image uploaded successfully',
          data: { url: imageUrl },
        });
      } catch (error) {
        console.log(error);
        return res.status(500).json({
          success: false,
          message: 'Something went wrong',
          error: error.message,
        });
      }
    });

    app.post('admin/file/s3_upload', SessionService.verifySessionMiddleware(role), async function (req, res, next) {
      try {
        const fileKey = nanoid();

        const upload = uploader.s3_upload({}, fileKey).single('file');

        upload(req, res, function (error) {
          if (error) {
            throw new Error(error);
          }
        });

        const filenameUrl = fileKey;

        res.status(201).json({
          success: true,
          message: 'File uploaded successfully',
          data: { url: filenameUrl },
        });
      } catch (error) {
        console.log(error);
        return res.status(500).json({
          success: false,
          message: 'Something went wrong',
          error: error.message,
        });
      }
    });

    app.post('admin/image/local_upload', SessionService.verifySessionMiddleware(role), async function (req, res, next) {
      try {
        const filename = nanoid();

        const upload = uploader.local_upload('/uploads', filename).single('image');

        upload(req, res, function (error) {
          if (error) {
            throw new Error(error);
          }
        });

        const imageUrl = filename;

        await db.image.insert({ url: imageUrl });

        res.status(201).json({
          success: true,
          message: 'Image uploaded successfully',
          data: { url: imageUrl },
        });
      } catch (error) {
        console.log(error);
        return res.status(500).json({
          success: false,
          message: 'Something went wrong',
          error: error.message,
        });
      }
    });

    app.post('admin/file/local_upload', SessionService.verifySessionMiddleware(role), async function (req, res, next) {
      try {
        const filename = nanoid();

        const upload = uploader.local_upload('/uploads', filename).single('image');

        upload(req, res, function (error) {
          if (error) {
            throw new Error(error);
          }
        });

        const filenameUrl = filename;

        res.status(201).json({
          success: true,
          message: 'File uploaded successfully',
          data: { url: filenameUrl },
        });
      } catch (error) {
        console.log(error);
        return res.status(500).json({
          success: false,
          message: 'Something went wrong',
          error: error.message,
        });
      }
    });

    app.post('admin/csv/import', SessionService.verifySessionMiddleware(role), async function (req, res, next) {
      try {
        const fileRows = [];

        // open uploaded file
        csv
          .fromPath(req.file.path)
          .on('data', function (data) {
            fileRows.push(data); // push each row
          })
          .on('end', function () {
            console.log(fileRows);
            fs.unlinkSync(req.file.path); // remove temp file
            //process "fileRows" and respond
          });

        res.status(201).json({
          success: true,
          message: 'File uploaded successfully',
        });
      } catch (error) {
        console.log(error);
        return res.status(500).json({
          success: false,
          message: 'Something went wrong',
          error: error.message,
        });
      }
    });

    app.post('admin/convert/html-to-pdf', SessionService.verifySessionMiddleware(role), async function (req, res, next) {
      try {
        let options = { format: 'A4' };

        html_to_pdf.generatePdf(req.file, options).then((pdfBuffer) => {
          console.log('PDF Buffer:-', pdfBuffer);
        });

        res.status(201).json({
          success: true,
          message: 'File uploaded successfully',
        });
      } catch (error) {
        console.log(error);
        return res.status(500).json({
          success: false,
          message: 'Something went wrong',
          error: error.message,
        });
      }
    });

    return app;
  },
};
