const { request, response } = require('express');

const authService = require('../services/AuthService');
const jwtService = require('../services/JwtService');

module.exports = {
  /**
   * Login router
   * @param {request} req
   * @param {response} res
   */
  get: async function (req, res) {
    try {
      const payload = await authService.login(
        req.body.email,
        req.body.password,
      );

      const isForcePasswordChange = await authService.forcePasswordChange(
        payload.user,
      );

      if (isForcePasswordChange)
        res.status(200).json({
          success: false,
          code: 'FORCE_PASSWORD_CHANGE',
          payload: response,
        });

      const response = {
        access_token: jwtService.createAccessToken(payload),
        refresh_token: jwtService.createRefreshToken(payload),
      };

      res.status(200).json({ success: true, payload: response });
    } catch (error) {
      res.status(500).json({ success: false, message: error.message });
    }
  },
};
