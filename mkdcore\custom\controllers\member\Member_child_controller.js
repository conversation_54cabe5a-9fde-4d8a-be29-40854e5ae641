const app = require('express').Router();
const { Op } = require('sequelize');
const db = require('../../models');
const { protect } = require('../../middlewares/auth_middleware.js');
const UploadService = require('../../services/UploadService');
const upload = UploadService.upload('files/file');

app.get('/api/user/children/cohort/:cohort_id', protect, async function (req, res, next) {
  try {
    const { user } = req;
    const { cohort_id } = req.params;
    const belongToCohort = await db.cohort.findOne({
      where: {
        [Op.or]: {
          parent_1: user.id,
          parent_2: user.id,
        },
        id: cohort_id,
      },
    });
    if (!belongToCohort) {
      return res.status(404).json({
        status: 'fail',
        message: 'Cohort not found',
      });
    }
    const children = await db.child.findAll({
      where: { cohort_id },
    });
    return res.status(200).json({
      status: 'success',
      children,
    });
  } catch (error) {
    console.log(error);
    return res.status(400).json({
      error: true,
      message: error.message,
    });
  }
});

app.post('/api/user/children/cohort/:cohort_id', protect, async function (req, res, next) {
  try {
    const { cohort_id } = req.params;
    const { user } = req;
    const { name, age } = req.body;
    const belongToCohort = await db.cohort.findOne({
      where: {
        [Op.or]: {
          parent_1: user.id,
          parent_2: user.id,
        },
        id: cohort_id,
      },
    });
    if (!belongToCohort) {
      return res.status(404).json({
        status: 'fail',
        message: 'Cohort not found',
      });
    }

    const child = await db.child.insert({ cohort_id, name, age });

    return res.status(200).json({
      status: 'success',
      child,
    });
  } catch (error) {
    console.log(error);
    return res.status(400).json({
      error: true,
      message: error.message,
    });
  }
});

app.delete('/api/user/cohort/:cohort_id/child/:child', protect, async function (req, res, next) {
  try {
    const { child, cohort_id } = req.params;
    const { user } = req;
    const belongToCohort = await db.cohort.findOne({
      where: {
        [Op.or]: {
          parent_1: user.id,
          parent_2: user.id,
        },
        id: cohort_id,
      },
    });
    if (!belongToCohort) {
      return res.status(404).json({
        status: 'fail',
        message: 'Cohort not found',
      });
    }

    const childExist = await db.child.findOne({ where: { id: child, cohort_id } });

    if (!childExist) {
      return res.status(400).json({
        status: 'fail',
        message: 'Child not found',
      });
    }

    await childExist.destroy();
    return res.status(204).json({
      status: 'success',
      message: 'child removed with success',
    });
  } catch (error) {
    console.log(error);
    return res.status(400).json({
      error: true,
      message: error.message,
    });
  }
});

app.patch('/api/user/cohort/:cohort_id/child/:child', protect, async function (req, res, next) {
  try {
    const { child, cohort_id } = req.params;
    const { user } = req;
    const belongToCohort = await db.cohort.findOne({
      where: {
        [Op.or]: {
          parent_1: user.id,
          parent_2: user.id,
        },
        id: cohort_id,
      },
    });
    if (!belongToCohort) {
      return res.status(404).json({
        status: 'fail',
        message: 'Cohort not found',
      });
    }

    const childExist = await db.child.findOne({ where: { id: child, cohort_id } });

    if (!childExist) {
      return res.status(400).json({
        status: 'fail',
        message: 'Child not found',
      });
    }

    await db.child.edit(req.body, childExist.id);
    return res.status(200).json({
      status: 'success',
      message: 'child updated with success',
    });
  } catch (error) {
    console.log(error);
    return res.status(400).json({
      error: true,
      message: error.message,
    });
  }
});

module.exports = app;
