const AuthService = require('../../services/AuthService.js');
const SessionService = require('../../services/SessionService.js');
const JwtService = require('../../services/JwtService.js');
const app = require('express').Router();
const { Op } = require('sequelize');
const db = require('../../models');
const UploadService = require('../../services/UploadService');
const { protect } = require('../../middlewares/auth_middleware.js');

const upload = UploadService.upload('files/file');

const role = 2;

app.get('/member/dashboard', SessionService.verifySessionMiddleware(role, 'member'), async function (req, res, next) {
  res.render('member/Dashboard', {
    get_page_name: () => 'Dashboard',
    _base_url: '/member/dashboard',
  });
});

module.exports = app;
