/*Powered By: Manaknightdigital Inc. https://manaknightdigital.com/ Year: 2020*/
/**
 * Copy builder
 * @copyright 2020 Manaknightdigital Inc.
 * @link https://manaknightdigital.com
 * @license Proprietary Software licensing
 * <AUTHOR>
 *
 */

const fs = require('fs');
const path = require('path');
const Builder = require('./Builder');

module.exports = function ({ config }) {
  this._render_list = [];

  Builder.call(this);

  this.build = function () {
    try {
      const { file_upload, image_upload } = config;

      let router_template = fs.readFileSync('../mkdcore/source/image/router.js', 'utf8');

      let upload_template_image = fs.readFileSync('../mkdcore/source/image/upload.js', 'utf8');

      let upload_template_file = fs.readFileSync('../mkdcore/source/image/upload.js', 'utf8');

      //  image upload
      if (image_upload === 's3') {
        const viewModelName = 'image_upload_model';

        upload_template_image = this.inject_substitute(upload_template_image, 'route', '/api/replace-this');

        upload_template_image = this.inject_substitute(upload_template_image, 'cc_view_model_name', this.toCamelCaseString(viewModelName));

        upload_template_image = this.inject_substitute(upload_template_image, 'view_model_name', 'image_upload_s3' + '.js');

        upload_template_image = this.inject_substitute(upload_template_image, 'view_model_arguments', 'db.image');

        upload_template_image = this.inject_substitute(
          upload_template_image,
          'response_handler',
          'return res.status(201).json({success:true, message:"xyzimage_uploaded_successfully"});',
        );

        upload_template_image = this.inject_substitute(upload_template_image, 'error_handler', 'return res.status(500).json({success:false, message:"xyzsomething_went_wrong"})');

        this.generate_view_model_image('s3');
      } else if (image_upload === 'local') {
        const viewModelName = 'image_upload_model';

        upload_template_image = this.inject_substitute(upload_template_image, 'route', '/api/replace-this');

        upload_template_image = this.inject_substitute(upload_template_image, 'cc_view_model_name', this.toCamelCaseString(viewModelName));

        upload_template_image = this.inject_substitute(upload_template_image, 'view_model_name', 'image_upload_local' + '.js');

        upload_template_image = this.inject_substitute(upload_template_image, 'view_model_arguments', 'db.image');

        upload_template_image = this.inject_substitute(
          upload_template_image,
          'response_handler',
          'return res.status(201).json({success:true, message:"xyzimage_uploaded_successfully"});',
        );

        upload_template_image = this.inject_substitute(upload_template_image, 'error_handler', 'return res.status(500).json({success:false, message:"xyzsomething_went_wrong"})');

        this.generate_view_model_image('local');
      } else {
        upload_template_image = '';
      }

      // file upload
      if (file_upload === 's3') {
        const viewModelName = 'file_upload_model';

        upload_template_file = this.inject_substitute(upload_template_file, 'route', '/api/replace-this');

        upload_template_file = this.inject_substitute(upload_template_file, 'cc_view_model_name', this.toCamelCaseString(viewModelName));

        upload_template_file = this.inject_substitute(upload_template_file, 'view_model_name', 'file_upload_s3' + '.js');

        upload_template_file = this.inject_substitute(upload_template_file, 'view_model_arguments', 'db.image');

        upload_template_file = this.inject_substitute(
          upload_template_file,
          'response_handler',
          'return res.status(201).json({success:true, message:"xyzimage_uploaded_successfully"});',
        );

        upload_template_file = this.inject_substitute(upload_template_file, 'error_handler', 'return res.status(500).json({success:false, message:"xyzsomething_went_wrong"})');

        this.generate_view_model_file('s3');
      } else if (file_upload === 'local') {
        const viewModelName = 'file_upload_model';

        upload_template_file = this.inject_substitute(upload_template_file, 'route', '/api/replace-this');

        upload_template_file = this.inject_substitute(upload_template_file, 'cc_view_model_name', this.toCamelCaseString(viewModelName));

        upload_template_file = this.inject_substitute(upload_template_file, 'view_model_name', 'file_upload_local' + '.js');

        upload_template_file = this.inject_substitute(upload_template_file, 'view_model_arguments', 'db.image');

        upload_template_file = this.inject_substitute(
          upload_template_file,
          'response_handler',
          'return res.status(201).json({success:true, message:"xyzimage_uploaded_successfully"});',
        );

        upload_template_file = this.inject_substitute(upload_template_file, 'error_handler', 'return res.status(500).json({success:false, message:"xyzsomething_went_wrong"})');

        this.generate_view_model_file('local');
      } else {
        upload_template_file = '';
      }

      router_template = this.inject_substitute(router_template, 'image_upload', upload_template_image);

      router_template = this.inject_substitute(router_template, 'file_upload', upload_template_file);
      // let fileDirectoryPath = path.dirname('../../release/image/router.js');
      // if (!fs.existsSync(fileDirectoryPath)) {
      //   fs.mkdirSync(fileDirectoryPath);
      // }
      this.createDirectoriesRecursive('../../release/image/router.js');
      fs.writeFileSync(path.join(__dirname, '../../release/image/router.js'), router_template, {
        mode: 0775,
      });

      for (const key in this._render_list) {
        const page = this._render_list[key];
        try {
          this.writeFileSyncRecursive(key, page, { mode: 0775 });
        } catch (error) {
          console.log('Image Build Error', error);
        }
      }
    } catch (error) {
      console.error('Image Builder Build Error', error);
    }
  };

  /**
   * Image view model generator
   * @param {('s3'|'local')} storage Storage type
   */
  this.generate_view_model_image = function (storage) {
    const ucName = 'image_upload_' + storage;

    let viewModel = fs.readFileSync('../mkdcore/source/portal/Image_view_model.js', {
      encoding: 'utf8',
    });

    const year = new Date().getFullYear();
    viewModel = this.inject_substitute(viewModel, 'year', year);
    viewModel = this.inject_substitute(viewModel, 'uc_name', ucName);

    if (storage === 's3') {
      viewModel = this.inject_substitute(viewModel, 'upload_resource_operation', "s3_upload({}, filename, 'image').single('image')");
    } else if (storage === 'local') {
      viewModel = this.inject_substitute(viewModel, 'upload_resource_operation', "local_upload('/uploads', 'image').single('image')");
    }

    this._render_list['../../release/view_models/' + ucName + '.js'] = viewModel;
  };

  /**
   * File view model generator
   * @param {('s3'|'local')} storage Storage type
   */
  this.generate_view_model_file = function (storage) {
    const ucName = 'file_upload_' + storage;

    let viewModel = fs.readFileSync('../mkdcore/source/portal/Image_view_model.js', {
      encoding: 'utf8',
    });

    const year = new Date().getFullYear();
    viewModel = this.inject_substitute(viewModel, 'year', year);
    viewModel = this.inject_substitute(viewModel, 'uc_name', ucName);

    if (storage === 's3') {
      viewModel = this.inject_substitute(viewModel, 'upload_resource_operation', "s3_upload({}, filename, 'file').single('file')");
    } else if (storage === 'local') {
      viewModel = this.inject_substitute(viewModel, 'upload_resource_operation', "local_upload('/uploads', 'file').single('file')");
    }

    this._render_list['../../release/view_models/' + ucName + '.js'] = viewModel;
  };

  this.destroy = function () {};
};
