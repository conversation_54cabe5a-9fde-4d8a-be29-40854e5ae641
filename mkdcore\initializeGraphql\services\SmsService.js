/*Powered By: Manaknightdigital Inc. https://manaknightdigital.com/ Year: 2020*/
/**
 * SMS Service
 * @copyright 2020 Manaknightdigital Inc.
 * @link https://manaknightdigital.com
 * @license Proprietary Software licensing
 * <AUTHOR>
 *
 */

const accountSid = process.env.TWILIO_ACCOUNT_SID;
const authToken = process.env.TWILIO_AUTH_TOKEN;
const client = require('twilio')(accountSid, authToken);

module.exports = {
  transport: null,
  from: '+**************',

  init: function (config) {},
  /**
   * Send SMS
   * @param {string} to
   * @param {string} body
   */
  send: function (to, body) {
    let self = this;
    return new Promise(function (resolve, reject) {
      client.messages
        .create({
          body,
          from: self.from,
          to,
        })
        .then((message) => resolve(message.sid))
        .catch((error) => reject(error));
    });
  },

  /**
   * Send Email Template
   *
   * @param {string} to
   * @param {string} slug
   * @param {mixed} payload
   * @param {mixed} model
   */
  sendTemplate: async function (to, slug, payload, model) {
    let self = this;
    const db = require('../models');
    return new Promise(function (resolve, reject) {
      db.sms
        .findOne({
          where: {
            slug: slug,
          },
        })
        .then(function (result) {
          if (!result) {
            return reject(new Error('SMS Template not found'));
          }

          let body = result.body;

          for (const key in payload) {
            const element = object[key];
            body = body.replace(new RegExp('{{{' + key + '}}}', 'g'), element);
          }
        })
        .catch(function (err) {
          return reject(new Error('SMS Template not found'));
        });
    });
  },
};
