var $$ = Dom7;

//<PERSON>ript to Populate The Phone Numbers Available.
async function getAvailableNumbers() {
  try {
    app.preloader.show();

    let response = await fetch('/api/user/available_phones', {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
    });
    if (response.status == 200) {
      res = await response.json();
      res.phones.forEach((phone) => {
        html = `
                <option class="phone-selection-item" value="${phone.phone_number}">${phone.phone_number}</option>
                `;
        $$('#phone-selection').append(html);
      });
      app.preloader.hide();
    }
    if (response.status == 400) {
      app.preloader.hide();
      throw response.statusText;
    }
  } catch (error) {
    app.preloader.hide();
    console.log(error);
  }
}

//Script for checking Email
async function checkEmail() {
  let response = await fetch('/api/cohort', {
    method: 'GET',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
    },
  });
  let resJSON = await response.json();
  let sharedEmailofCohort = resJSON.data[resJSON.data.length - 1].shared_email;
  await this.store.dispatch('setSharedEmail', sharedEmailofCohort);
}

//Script for chosing the Phone Number
async function choseNumber(phoneNumber) {
  var mainview = app.view.main;
  let chosenNumber = phoneNumber;
  let cohortID = this.store.getters.cohortID.value;
  let sharedEmail = this.store.getters.sharedEmail.value;
  try {
    if (phoneNumberValidateField(chosenNumber)) {
      let body = {
        phone_number: chosenNumber,
      };
      app.preloader.show();
      let response = await fetch(`/api/user/order_phone/${cohortID}`, {
        method: 'POST',
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
      });
      let resJSON = await response.json();
      app.preloader.hide();
      if (response.status == 200) {
        app.dialog.alert('Co-Phone successfully reserved for your Co-Hort');
        if (sharedEmail == '' || sharedEmail == null) {
          mainview.router.navigate({ name: 'sharedEmail' });
        } else {
          mainview.router.navigate({ name: 'viewcohort' });
        }
      } else {
        throw "Couldn't reserve Co-Phone, Please try again.";
      }
    } else {
      throw 'Something went wrong, Please try again';
    }
    app.preloader.hide();
  } catch (error) {
    app.preloader.hide();
    app.dialog.alert(error);
  }
}

function phoneNumberValidateField(phone) {
  var AllFieldsValidated = true;
  var errortoThrow = '';
  if (phone == '') {
    errortoThrow = 'Please select a Co-Phone';
    AllFieldsValidated = false;
  }
  if (AllFieldsValidated == false) {
    throw errortoThrow;
  }
  return AllFieldsValidated;
}

//Script for Getting Data from CreateCohort Page
async function createCohortFromName() {
  var mainview = app.view.main;
  let cohortName = document.getElementById('create-cohort-cohort-name').value;
  let parentOneSplit = document.getElementById('create-cohort-parent-one-split').value;
  let parentTwoSplit = document.getElementById('create-cohort-parent-two-split').value;
  try {
    app.preloader.show();
    if (createCohortValidateField(cohortName, parentOneSplit, parentTwoSplit)) {
      let body = {
        name: cohortName,
        parent_1_default_split: parentOneSplit,
        parent_2_default_split: parentTwoSplit,
      };
      let response = await fetch('/api/cohort/add', {
        method: 'POST',
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
      });
      let resJSON = await response.json();
      console.log(response);
      console.log(resJSON);
      if (response.status == 200) {
        app.preloader.hide();
        await this.store.dispatch('setcohortID', resJSON.data);
        app.dialog.alert('Co-Hort created');
        mainview.router.navigate({ name: 'addchild' });
      }
      if (response.status == 400) {
        app.preloader.hide();
        throw "Couldn't create Co-Hort";
      }
    } else {
      app.preloader.hide();
      throw 'Something went wrong, Please try again.';
    }
  } catch (error) {
    app.preloader.hide();
    app.dialog.alert(error);
  }
}

function createCohortValidateField(cohortName, parent1Split, parent2Split) {
  var AllFieldsValidated = true;
  var errortoThrow = 'These field(s) are required: <br>';
  if (cohortName == '') {
    errortoThrow += 'Cohort Name <br>';
    AllFieldsValidated = false;
  }
  if (parent1Split == '' || parent2Split == '') {
    errortoThrow += `Co-Parent(s) Split <br>`;
    AllFieldsValidated = false;
  }
  if (AllFieldsValidated == false) {
    throw errortoThrow;
  }
  if (parseInt(parent1Split) + parseInt(parent2Split) !== 100) {
    errortoThrow = 'The split should not be less than or greater than 100%';
    throw errortoThrow;
  }
  return AllFieldsValidated;
}

function watchCohortSplit() {
  let parent1Split = document.getElementById('create-cohort-parent-one-split');
  let parent2Split = document.getElementById('create-cohort-parent-two-split');

  $('#create-cohort-parent-one-split').on('keyup change keydown', function (e) {
    if ($(this).val() > 100 && e.code !== 'Backspace' && e.code !== 'Delete') {
      e.preventDefault();
      $(this).val(100);
    }
    let splitRemain = 100 - parseInt(parent1Split.value);
    parent2Split.value = splitRemain;
  });

  $('#create-cohort-parent-two-split').on('keyup change keydown', function (e) {
    if ($(this).val() > 100 && e.code !== 'Backspace' && e.code !== 'Delete') {
      e.preventDefault();
      $(this).val(100);
    }
    let splitRemaing = 100 - parseInt(parent2Split.value);
    parent1Split.value = splitRemaing;
  });
}

async function searchbyAreaCode() {
  document.getElementById('number-container').textContent = '';
  let areacode = document.getElementById('sharedphoneareacode').value;
  app.preloader.show();

  try {
    let response = await fetch(`/api/user/available_phones?AreaCode=${areacode}`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
    });
    let resJSON = await response.json();
    app.preloader.hide();

    resJSON.phones.forEach((number) => {
      if (number.friendly_name.substring(1, 4) == areacode || areacode == '') {
        html = `
        <div class="number-listitems">
          <span>${number.phone_number}</span>
          <span><a data-number="${number.phone_number}" class="phone_options">Choose</a></span>
        </div>
        `;
        $$('#number-container').append(html);
      }
    });
    $$('.phone_options').on('click', function (e) {
      choseNumber(this.dataset.number);
    });
    if (document.getElementById('number-container').textContent == '') {
      html = `
      <p class="sharedphones-nophonemessage">Sorry, there seems to be no available phones with that area code, please try another area code.</p>
      `;
      $$('#number-container').append(html);
      app.preloader.hide();
    }
  } catch (error) {
    app.preloader.hide();
    console.log(error);
  }
}
