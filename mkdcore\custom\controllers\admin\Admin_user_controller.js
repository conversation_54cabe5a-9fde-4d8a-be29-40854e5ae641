'use strict';

const app = require('express').Router();
const Sequelize = require('sequelize');
const logger = require('../../services/LoggingService');
let pagination = require('../../services/PaginationService');
let SessionService = require('../../services/SessionService');
let JwtService = require('../../services/JwtService');
const ValidationService = require('../../services/ValidationService');
const PermissionService = require('../../services/PermissionService');
const UploadService = require('../../services/UploadService');
const AuthService = require('../../services/AuthService');
const db = require('../../models');
const helpers = require('../../core/helpers');

const role = 1;

app.get('/admin/users/:num', SessionService.verifySessionMiddleware(role, 'admin'), async function (req, res, next) {
  try {
    let session = req.session;
    let paginateListViewModel = require('../../view_models/user_admin_list_paginate_view_model');

    var viewModel = new paginateListViewModel(db.user, 'Users', session.success, session.error, '/admin/users');

    const format = req.query.format ? req.query.format : 'view';
    const direction = req.query.direction ? req.query.direction : 'ASC';
    const per_page = req.query.per_page ? req.query.per_page : 10;
    let order_by = req.query.order_by ? req.query.order_by : viewModel.get_field_column()[0];
    let orderAssociations = [];
    viewModel.set_order_by(order_by);
    let joins = order_by.includes('.') ? order_by.split('.') : [];
    order_by = order_by.includes('.') ? joins[joins.length - 1] : order_by;
    if (joins.length > 0) {
      for (let i = joins.length - 1; i > 0; i--) {
        orderAssociations.push(`${joins[i - 1]}`);
      }
    }
    // Check for flash messages
    const flashMessageSuccess = req.flash('success');
    if (flashMessageSuccess && flashMessageSuccess.length > 0) {
      viewModel.success = flashMessageSuccess[0];
    }
    const flashMessageError = req.flash('error');
    if (flashMessageError && flashMessageError.length > 0) {
      viewModel.error = flashMessageError[0];
    }

    viewModel.set_id(req.query.id ? req.query.id : '');
    viewModel.set_credential_email(req.query.credential_email ? req.query.credential_email : '');
    viewModel.set_first_name(req.query.first_name ? req.query.first_name : '');
    viewModel.set_last_name(req.query.last_name ? req.query.last_name : '');
    viewModel.set_status(req.query.status ? req.query.status : '');
    viewModel.set_created_at(req.query.created_at ? req.query.created_at : '');

    let where = helpers.filterEmptyFields({
      id: viewModel.get_id(),
      first_name: { [Sequelize.Op.like]: `${viewModel.get_first_name()}%` },
      last_name: { [Sequelize.Op.like]: `${viewModel.get_last_name()}%` },
      status: viewModel.get_status(),
      created_at: viewModel.get_created_at(),
    });

    let associatedWhere = helpers.filterEmptyFields({
      email: { [Sequelize.Op.like]: `${viewModel.get_credential_email()}%` },
    });
    const isAssociationRequired = Object.keys(associatedWhere).length > 0 ? true : false;

    const count = await db.user._count(where, [{ model: db.credential, where: associatedWhere, required: isAssociationRequired, as: 'credential' }]);

    let sort_url = '';

    if (req.originalUrl.includes('?')) {
      if (req.originalUrl.includes('order_by')) {
        let url_query = req.originalUrl.split('?')[1];
        sort_url = `${url_query.split('order_by')[0]}`;
      } else {
        sort_url = `${req.originalUrl.split('?')[1]}`;
      }
    }
    viewModel.set_total_rows(count);
    viewModel.set_per_page(+per_page);
    viewModel.set_page(+req.params.num);
    viewModel.set_query(req.query);
    viewModel.set_sort_base_url(`/admin/users/${+req.params.num}?${sort_url}`);
    viewModel.set_sort(direction);

    const list = await db.user.get_credential_paginated(
      db,
      associatedWhere,
      viewModel.get_page() - 1 < 0 ? 0 : viewModel.get_page(),
      viewModel.get_per_page(),
      where,
      order_by,
      direction,
      orderAssociations,
    );

    viewModel.set_list(list);

    viewModel.credential = await db.credential;

    if (format == 'csv') {
      const csv = viewModel.to_csv();
      return res
        .set({
          'Content-Type': 'text/csv',
          'Content-Disposition': 'attachment; filename="export.csv"',
        })
        .send(csv);
    }

    // if (format != 'view') {
    //   res.json(viewModel.to_json());
    // } else {
    // }

    return res.render('admin/User', viewModel);
  } catch (error) {
    console.error(error);
    viewModel.error = error.message || 'Something went wrong';
    return res.render('admin/User', viewModel);
  }
});

app.get('/admin/users-add', SessionService.verifySessionMiddleware(role, 'admin'), async function (req, res, next) {
  if (req.session.csrf === undefined) {
    req.session.csrf = SessionService.randomString(100);
  }

  const userAdminAddViewModel = require('../../view_models/user_admin_add_view_model');

  const viewModel = new userAdminAddViewModel(db.user, 'Add user', '', '', '/admin/users');

  res.render('admin/Add_User', viewModel);
});

app.post(
  '/admin/users-add',
  SessionService.verifySessionMiddleware(role, 'admin'),
  ValidationService.validateInput(
    { first_name: 'required', last_name: 'required', status: 'required' },
    { 'first_name.required': 'FirstName is required', 'last_name.required': 'LastName is required', 'status.required': 'Status is required' },
  ),
  async function (req, res, next) {
    if (req.session.csrf === undefined) {
      req.session.csrf = SessionService.randomString(100);
    }
    const userAdminAddViewModel = require('../../view_models/user_admin_add_view_model');

    const viewModel = new userAdminAddViewModel(db.user, 'Add user', '', '', '/admin/users');

    // TODO use separate controller for image upload
    //  {{{upload_field_setter}}}

    const { email, password, first_name, last_name, image, role_id, phone, status } = req.body;

    viewModel.form_fields = {
      ...viewModel.form_fields,
      email,
      password,
      first_name,
      last_name,
      image,
      role_id,
      phone,
      status,
    };

    try {
      if (req.validationError) {
        viewModel.error = req.validationError;
        return res.render('admin/Add_User', viewModel);
      }

      viewModel.session = req.session;

      const { email, password = '', role_id, ...rest } = viewModel.form_fields;
      const data = await AuthService.register(email, password, role_id, rest);

      if (!data) {
        viewModel.error = 'Something went wrong';
        return res.render('admin/Add_User', viewModel);
      }

      await db.activity_log.insert({
        action: 'ADD',
        name: 'Admin_user_controller.js',
        portal: 'admin',
        data: JSON.stringify({ email, password, first_name, last_name, image, role_id, phone, status }),
      });

      req.flash('success', 'User created successfully');
      return res.redirect('/admin/users/0');
    } catch (error) {
      console.error(error);
      viewModel.error = error.message || 'Something went wrong';
      return res.render('admin/Add_User', viewModel);
    }
  },
);

app.get('/admin/users-edit/:id', SessionService.verifySessionMiddleware(role, 'admin'), async function (req, res, next) {
  let id = req.params.id;
  if (req.session.csrf === undefined) {
    req.session.csrf = SessionService.randomString(100);
  }
  const userAdminEditViewModel = require('../../view_models/user_admin_edit_view_model');

  const viewModel = new userAdminEditViewModel(db.user, 'Edit user', '', '', '/admin/users');

  try {
    const exists = await db.user.get_user_credential(id, db);

    if (!exists) {
      req.flash('error', 'User not found');
      return res.redirect('/admin/users/0');
    }
    const values = exists;
    Object.keys(viewModel.form_fields).forEach((field) => {
      if (field === 'credential.email') {
        viewModel.form_fields[field] = values['credential']['email'];
        return;
      }
      viewModel.form_fields[field] = values[field] || '';
    });
    viewModel.credential = db.credential;
    return res.render('admin/Edit_User', viewModel);
  } catch (error) {
    console.error(error);
    viewModel.error = error.message || 'Something went wrong';
    return res.render('admin/Edit_User', viewModel);
  }
});

app.post('/admin/users-edit/:id', SessionService.verifySessionMiddleware(role, 'admin'), async function (req, res, next) {
  let id = req.params.id;
  if (req.session.csrf === undefined) {
    req.session.csrf = SessionService.randomString(100);
  }

  const userAdminEditViewModel = require('../../view_models/user_admin_edit_view_model');

  const viewModel = new userAdminEditViewModel(db.user, 'Edit user', '', '', '/admin/users');

  const { credential_email, first_name, last_name, role_id, image, phone, status } = req.body;

  viewModel.form_fields = {
    ...viewModel.form_fields,
    'credential.email': credential_email,
    first_name,
    last_name,
    role_id,
    image,
    phone,
    status,
  };

  delete viewModel.form_fields.id;

  try {
    if (req.validationError) {
      viewModel.error = req.validationError;
      return res.render('admin/Edit_User', viewModel);
    }

    const resourceExists = await db.user.get_user_credential(id, db);
    if (!resourceExists) {
      req.flash('error', 'User not found');
      return res.redirect('/admin/users/0');
    }

    viewModel.session = req.session;

    let data = await db.user.edit({ first_name, last_name, role_id, image, phone, status }, id);
    if (!data) {
      viewModel.error = 'Something went wrong';
      return res.render('admin/Edit_User', viewModel);
    }

    if (resourceExists.credential) {
      data = await db.credential.edit(helpers.filterEmptyFields({ email: credential_email }), resourceExists.credential.id);
      if (!data) {
        viewModel.error = 'Something went wrong';
        return res.render('admin/Edit_User', viewModel);
      }
    }

    await db.activity_log.insert({
      action: 'EDIT',
      name: 'Admin_user_controller.js',
      portal: 'admin',
      data: JSON.stringify({ credential_email, first_name, last_name, role_id, image, phone, status }),
    });

    req.flash('success', 'User edited successfully');

    return res.redirect('/admin/users/0');
  } catch (error) {
    console.error(error);
    viewModel.error = error.message || 'Something went wrong';
    return res.render('admin/Edit_User', viewModel);
  }
});

app.get(
  '/admin/users-view/:id',
  SessionService.verifySessionMiddleware(role, 'admin'),

  async function (req, res, next) {
    try {
      let id = req.params.id;

      const userAdminDetailViewModel = require('../../view_models/user_admin_detail_view_model');

      var viewModel = new userAdminDetailViewModel(db.user, 'User details', '', '', '/admin/users');

      const data = await db.user.get_user_credential(id, db);
      data.status = db.user.status_mapping()[data.status];
      data.role_id = db.user.role_id_mapping()[data.role_id];
      data['credential.two_factor_authentication'] = db.credential.two_factor_authentication_mapping()[data.credential.two_factor_authentication];
      data['credential.type'] = db.credential.type_mapping()[data.credential.type];
      data['credential.status'] = db.credential.status_mapping()[data.credential.status];
      data['credential.verify'] = db.credential.verify_mapping()[data.credential.verify];

      if (!data) {
        viewModel.error = 'User not found';
        viewModel.detail_fields = {
          ...viewModel.detail_fields,
          image: 'N/A',
          id: 'N/A',
          'credential.email': 'N/A',
          'credential.status': 'N/A',
          first_name: 'N/A',
          last_name: 'N/A',
          role_id: 'N/A',
        };
      } else {
        viewModel.detail_fields = {
          ...viewModel.detail_fields,
          image: data['image'] || 'N/A',
          id: data['id'] || 'N/A',
          'credential.email': data['credential']['email'] || 'N/A',
          'credential.status': data['credential.status'] || 'N/A',
          first_name: data['first_name'] || 'N/A',
          last_name: data['last_name'] || 'N/A',
          role_id: data['role_id'] || 'N/A',
        };
      }

      res.render('admin/View_User', viewModel);
    } catch (error) {
      console.error(error);
      viewModel.error = error.message || 'Something went wrong';
      viewModel.detail_fields = {
        ...viewModel.detail_fields,
        image: 'N/A',
        id: 'N/A',
        'credential.email': 'N/A',
        'credential.status': 'N/A',
        first_name: 'N/A',
        last_name: 'N/A',
        role_id: 'N/A',
      };
      res.render('admin/View_User', viewModel);
    }
  },
);

app.get('/admin/users-delete/:id', SessionService.verifySessionMiddleware(role, 'admin'), async function (req, res, next) {
  let id = req.params.id;

  const userAdminDeleteViewModel = require('../../view_models/user_admin_delete_view_model');

  const viewModel = new userAdminDeleteViewModel(db.user);

  try {
    const exists = await db.user.getByPK(id);

    if (!exists) {
      req.flash('error', 'User not found');
      return res.redirect('/admin/users/0');
    }

    viewModel.session = req.session;

    await db.user.realDelete(id);

    await db.activity_log.insert({
      action: 'DELETE',
      name: 'Admin_user_controller.js',
      portal: 'admin',
      data: JSON.stringify(exists),
    });

    req.flash('success', 'User deleted successfully');

    return res.redirect('/admin/users/0');
  } catch (error) {
    console.error(error);
    req.flash('error', error.message || 'Something went wrong');
    return res.redirect('/admin/users/0');
  }
});

// APIS

app.get('/admin/api/users', JwtService.verifyTokenMiddleware(role), async function (req, res, next) {
  try {
    const user_id = req.user_id;
    const session = req.session;
    let listViewModel = require('../../view_models/user_admin_list_paginate_view_model');
    let viewModel = new listViewModel(db.user, 'Users', session.success, session.error, '/admin/users');
    const direction = req.query.direction ? req.query.direction : 'ASC';
    const page = req.query.page ? parseInt(req.query.page) : 1;
    const limit = req.query.limit ? parseInt(req.query.limit) : 10;
    const offset = (page - 1) * limit;
    let order_by = req.query.order_by ? req.query.order_by : viewModel.get_field_column()[0];
    let orderAssociations = [];
    viewModel.set_order_by(order_by);
    let joins = order_by.includes('.') ? order_by.split('.') : [];
    order_by = order_by.includes('.') ? joins[joins.length - 1] : order_by;
    if (joins.length > 0) {
      for (let i = joins.length - 1; i > 0; i--) {
        orderAssociations.push(`${joins[i - 1]}`);
      }
    }

    viewModel.set_id(req.query.id ? req.query.id : '');
    viewModel.set_credential_email(req.query.credential_email ? req.query.credential_email : '');
    viewModel.set_first_name(req.query.first_name ? req.query.first_name : '');
    viewModel.set_last_name(req.query.last_name ? req.query.last_name : '');
    viewModel.set_status(req.query.status ? req.query.status : '');
    viewModel.set_created_at(req.query.created_at ? req.query.created_at : '');

    let where = helpers.filterEmptyFields({
      id: viewModel.get_id(),
      first_name: viewModel.get_first_name(),
      last_name: viewModel.get_last_name(),
      status: viewModel.get_status(),
      created_at: viewModel.get_created_at(),
    });

    let associatedWhere = helpers.filterEmptyFields({
      email: viewModel.get_credential_email(),
    });
    const isAssociationRequired = Object.keys(associatedWhere).length > 0 ? true : false;

    let include = [{ model: db.credential, where: associatedWhere, required: isAssociationRequired, as: 'credential' }];

    const { rows: allItems, count } = await db.user.findAndCountAll({
      where: where,
      limit: limit == 0 ? null : limit,
      offset: offset,
      include: include,
      distinct: true,
    });

    const response = {
      items: allItems,
      page,
      nextPage: count > offset + limit ? page + 1 : false,
      retrievedCount: allItems.length,
      fullCount: count,
    };

    return res.status(201).json({ success: true, data: response });
  } catch (error) {
    console.error(error);
    return res.status(500).json({ success: false, message: error.message || 'Something went wrong' });
  }
});

app.post(
  '/admin/api/users-add',
  JwtService.verifyTokenMiddleware(role),
  ValidationService.validateInput(
    { first_name: 'required', last_name: 'required', status: 'required' },
    { 'first_name.required': 'FirstName is required', 'last_name.required': 'LastName is required', 'status.required': 'Status is required' },
  ),
  async function (req, res, next) {
    const userAdminAddViewModel = require('../../view_models/user_admin_add_view_model');

    const viewModel = new userAdminAddViewModel(db.user);

    const { email, password, first_name, last_name, image, role_id, phone, status } = req.body;
    try {
      if (req.validationError) {
        return res.status(500).json({ success: false, message: req.validationError });
      }

      const { email, password = '', role_id, ...rest } = req.body;
      const data = await AuthService.register(email, password, role_id, rest);

      if (!data) {
        return res.status(500).json({ success: false, message: 'Something went wrong' });
      }

      await db.activity_log.insert({
        action: 'ADD',
        name: 'Admin_user_controller.js',
        portal: 'admin',
        data: JSON.stringify({ email, password, first_name, last_name, image, role_id, phone, status }),
      });

      return res.status(201).json({ success: true, message: 'User created successfully' });
    } catch (error) {
      return res.status(500).json({ success: false, message: 'Something went wrong' });
    }
  },
);

app.put('/admin/api/users-edit/:id', JwtService.verifyTokenMiddleware(role), async function (req, res, next) {
  let id = req.params.id;

  const userAdminEditViewModel = require('../../view_models/user_admin_edit_view_model');

  const viewModel = new userAdminEditViewModel(db.user);

  const { credential_email, first_name, last_name, role_id, image, phone, status } = req.body;

  try {
    if (req.validationError) {
      return res.status(500).json({ success: false, message: req.validationError });
    }

    const resourceExists = await db.user.getByPK(id);
    if (!resourceExists) {
      return res.status(404).json({ success: false, message: 'User not found' });
    }

    const data = await db.user.edit({ first_name, last_name, role_id, image, phone, status }, id);

    if (!data) {
      return res.status(500).json({ success: false, message: 'Something went wrong' });
    }

    await db.activity_log.insert({
      action: 'EDIT',
      name: 'Admin_user_controller.js',
      portal: 'admin',
      data: JSON.stringify({ credential_email, first_name, last_name, role_id, image, phone, status }),
    });

    return res.json({ success: true, message: 'User edited successfully' });
  } catch (error) {
    return res.status(500).json({ success: false, message: 'Something went wrong' });
  }
});

app.get('/admin/api/users-view/:id', JwtService.verifyTokenMiddleware(role), async function (req, res, next) {
  let id = req.params.id;

  const userAdminDetailViewModel = require('../../view_models/user_admin_detail_view_model');

  const viewModel = new userAdminDetailViewModel(db.user);

  try {
    const data = await db.user.get_user_credential(id, db);

    if (!data) {
      return res.status(404).json({ message: 'User not found', data: null });
    } else {
      const fields = {
        ...viewModel.detail_fields,
        image: data['image'] || '',
        id: data['id'] || '',
        email: data['credential']['email'] || '',
        status: data['credential']['status'] || '',
        first_name: data['first_name'] || '',
        last_name: data['last_name'] || '',
        role_id: data['role_id'] || '',
      };
      return res.status(200).json({ data: fields });
    }
  } catch (error) {
    return res.status(404).json({ message: 'Something went wrong', data: null });
  }
});

app.delete('/admin/api/users-delete/:id', JwtService.verifyTokenMiddleware(role), async function (req, res, next) {
  let id = req.params.id;

  const userAdminDeleteViewModel = require('../../view_models/user_admin_delete_view_model');

  const viewModel = new userAdminDeleteViewModel(db.user);

  try {
    const exists = await db.user.getByPK(id);

    if (!exists) {
      return res.status(404).json({ success: false, message: 'User not found' });
    }

    await db.user.realDelete(id);

    await db.activity_log.insert({
      action: 'DELETE',
      name: 'Admin_user_controller.js',
      portal: 'admin',
      data: JSON.stringify(exists),
    });

    return res.status(200).json({ success: true, message: 'User deleted successfully' });
  } catch (error) {
    return res.status(500).json({ success: false, message: 'Something went wrong' });
  }
});

module.exports = app;
