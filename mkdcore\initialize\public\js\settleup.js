var $$ = Dom7;

async function populateSettleUp() {
  try {
    app.preloader.show();
    document.getElementById('settleup-amount-payablecontainer').textContent = '';
    document.getElementById('settleup-expensecontainer').textContent = '';
    var selectedOption = document.getElementById('expenses-cohortselector');
    var id = selectedOption.options[selectedOption.selectedIndex].getAttribute('data-cohortid');

    let response = await fetch(`/api/expense/${id}`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
    });
    if (response.status == 200) {
      let loggedinID = JSON.parse(localStorage.getItem('user'));
      let loggedParentID;
      let resJSON = await response.json();
      console.log(resJSON);

      if (resJSON.parent1.id == loggedinID.id) {
        loggedParentID = loggedinID.id;
      } else {
        loggedParentID = loggedinID.id;
      }

      resJSON.expensesObj.forEach((expense) => {
        if (expense.expense.paid_by != loggedParentID) {
          html = `
                    <div class="card singleExpense">
                    <div class="card-header singleExpenseCardHeader">${expense.expense.expense_name}</div>
                    <div class="card-content card-content-padding expenseInfoContainer">
                        <div class="row no-margin">
                            <div class="col-60 viewFiles-infoCol">
                                <div class="block-title text-align-left info">
                                    ${expense.expense.notes}
                                </div>
                                <div class="block-title text-align-left info">
                                    Date: ${new Date(expense.expense.date).toLocaleString('en-us', { timeZone: 'UTC', month: 'short', day: 'numeric', year: 'numeric' })}
                                </div>
                                <div class="block-title text-align-left info">
                                    ${expense.expense.paidBy.first_name}
                                </div>
                                <div class="block-title text-align-left info">
                                    $${
                                      expense.expense.paidBy.id == expense.parent1.id
                                        ? parseInt(expense.parent2.amountToPay).toFixed(2)
                                        : parseInt(expense.parent1.amountToPay).toFixed(2)
                                    }
                                </div>
                            </div>
                            <div class="col-40 expenseInfoCallToAction">
                                <label class="toggle color-blue">
                                    <input type="checkbox" data-expenseid="${expense.expense.id}" data-expenseamount="${
            expense.expense.paidBy.id == expense.parent1.id ? parseInt(expense.parent2.amountToPay) : parseInt(expense.parent1.amountToPay)
          }"/>
                                    <span class="toggle-icon"></span>
                                </label>
                            </div>
                            </div>
                        </div>
                    </div>
                `;
          if (expense.expense.settlement == null) {
            $$('#settleup-expensecontainer').append(html);
          }
        }
      });
      app.preloader.hide();
      getNewRemainingAmount();
    } else if (response.status == 404) {
      html = `
                <div class="text-align-center amount-container-text">
                    N/A
                </div>
            `;
      $$('#amount-payablecontainer').append(html);

      html = `
                <p class="viewcohort-infomessage-text text-align-center">
                    You don't have any Co-Expenses to show, Add Co-Expenses to your Co-Hort to see them here.
                </p>
            `;
      $$('#expenses-tabs-supercontainer').append(html);
      app.preloader.hide();
    }
  } catch (error) {
    app.preloader.hide();
    console.log(error);
  }
}

async function getNewRemainingAmount() {
  try {
    app.preloader.show();
    var selectedOption = document.getElementById('expenses-cohortselector');
    var id = selectedOption.options[selectedOption.selectedIndex].getAttribute('data-cohortid');

    let response = await fetch(`/api/expense/${id}`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
    });

    let resJSON = await response.json();
    if (response.status == 200) {
      let loggedinID = JSON.parse(localStorage.getItem('user'));
      let loggedParentID;
      let newAmount = 0;

      if (resJSON.parent1.id == loggedinID.id) {
        loggedParentID = loggedinID.id;
      } else {
        loggedParentID = loggedinID.id;
      }

      resJSON.expensesObj.forEach((expense) => {
        if (expense.expense.paid_by != loggedParentID) {
          if (expense.expense.settlement == null) {
            if (expense.expense.paidBy.id == expense.parent1.id) {
              newAmount += parseInt(expense.parent2.amountToPay);
            } else {
              newAmount += parseInt(expense.parent1.amountToPay);
            }
          }
        }
      });

      document.getElementById('settleup-amount-payablecontainer').textContent = '';
      html = `
            <div class="text-align-center amount-container-text">
                $${parseInt(newAmount).toFixed(2)}
            </div>  
            `;
      $$('#settleup-amount-payablecontainer').append(html);
      app.preloader.hide();
    }
  } catch (error) {
    app.preloader.hide();
    console.log(error);
  }
}
