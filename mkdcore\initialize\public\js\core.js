/*Powered By: Manaknightdigital Inc. https://manaknightdigital.com/ Year: 2019*/
var mkd_events = (function () {
  var topics = {};
  var hOP = topics.hasOwnProperty;

  return {
    subscribe: function (topic, listener) {
      // Create the topic's object if not yet created
      if (!hOP.call(topics, topic)) topics[topic] = [];

      // Add the listener to queue
      var index = topics[topic].push(listener) - 1;

      // Provide handle back for removal of topic
      return {
        remove: function () {
          delete topics[topic][index];
        },
      };
    },
    publish: function (topic, info) {
      // If the topic doesn't exist, or there's no listeners in queue, just leave
      if (!hOP.call(topics, topic)) return;

      // Cycle through topics queue, fire!
      topics[topic].forEach(function (item) {
        item(info != undefined ? info : {});
      });
    },
  };
})();

const clearSearch = (e) => {
  e.preventDefault();
  window.location = location.pathname;
};
const clearSearchBtn = document.getElementById('filter-clear-search');
if (clearSearchBtn) clearSearchBtn.addEventListener('click', clearSearch);

function mkd_is_number(evt, obj) {
  var charCode = evt.which ? evt.which : event.keyCode;
  var value = obj.value;

  var minuscontains = value.indexOf('-') != -1;
  if (minuscontains) {
    if (charCode == 45) {
      return false;
    }
  }
  if (charCode == 45) {
    return true;
  }

  var dotcontains = value.indexOf('.') != -1;
  if (dotcontains) {
    if (charCode == 46) {
      return false;
    }
  }
  if (charCode == 46) {
    return true;
  }
  if (charCode > 31 && (charCode < 48 || charCode > 57)) {
    return false;
  }
  return true;
}

function mkd_export_table(url) {
  if (url.indexOf('?') > -1) {
    url = url + '&format=csv';
  } else {
    url = url + '?format=csv';
  }
  window.location.href = url;
}
$(document).ready(function () {
  $('#sidebarCollapse').on('click', function () {
    $('#sidebar').toggleClass('active');
  });

  //import csv code
  $('#btn-choose-csv').on('click', function (e) {
    e.preventDefault();
    $('#csv-file').trigger('click');
  });

  $('#csv-file').on('change', function () {
    $('#import-csv').trigger('submit');
  });

  $('#import-csv').on('submit', function (e) {
    e.preventDefault();
    var formData = new FormData(this);
    var url = $(this).attr('action');

    $(this).addClass('d-none');
    $.ajax({
      url: url,
      type: 'POST',
      data: formData,
      cache: false,
      contentType: false,
      processData: false,
      success: function (res) {
        var body_html = '';
        var head_html = '';
        if (res.preview == true) {
          var data = res.data;
          var header = data[0];
          data.shift();
          for (let headerItem of header) {
            head_html += `<th>${headerItem}</th>`;
          }
          for (let row of data) {
            body_html += '<tr>';
            for (let item of row) {
              body_html += `<td>${item} </td>`;
            }
            body_html += '</tr>';
          }

          $('#csv-table-head').html(head_html);
          $('#csv-table-body').html(body_html);
          $('#csv-table').removeClass('d-none');
          $('#btn-save-csv').removeClass('d-none');
        } else if (res.saved == true) {
          alert('Imported Successfully');
          location.reload();
        }
      },
    });
  });

  $('#btn-save-csv').on('click', function (e) {
    e.preventDefault();
    var model = $('#btn-csv-upload-dialog').data('model');
    console.log(model);
    $('#import-csv').attr('action', '/v1/api/file/import/' + model);
    $('#import-csv').trigger('submit');
  });

  $('.modal-image').on('click', function () {
    var src = $(this).attr('src');
    $('#modal-image-slot').attr('src', src);
    $('#modal-image-show').modal('show');
  });
});

const selectedRows = [];
// Buk Actions
const bulkSelectAll = document.getElementById('bulkSelectAll');

const deleteButton = document.getElementById('bulkDeleteButton');
const editButton = document.getElementById('bulkEditButton');
const originalDeleteHref = deleteButton?.href ?? '';

// Select All click
if (bulkSelectAll) {
  bulkSelectAll.addEventListener('click', (event) => {
    selectedRows.length = 0;

    const bulkSelectRows = document.querySelectorAll('.bulkSelect');

    const isSelectAll = event.target.checked;
    bulkSelectRows.forEach((item) => {
      item.checked = isSelectAll;
      if (isSelectAll) {
        selectedRows.push(item.dataset.id);
      }
    });
    // Update delete link
    if (isSelectAll) {
      if (deleteButton) deleteButton.style.display = 'inline-block';
      if (deleteButton) deleteButton.href = originalDeleteHref + selectedRows.join('|');
      if (editButton) editButton.style.display = 'inline-block';
    } else {
      if (deleteButton) deleteButton.style.display = 'none';
      if (deleteButton) deleteButton.href = originalDeleteHref;
      if (editButton) editButton.style.display = 'none';
    }
  });
}

// Handle Individual Select
const handleBulkSelectChange = function (event) {
  const id = event.dataset.id;
  if (event.checked) {
    selectedRows.push(id);
  } else {
    const findIndex = selectedRows.findIndex((item) => item === id);
    if (findIndex > -1) {
      selectedRows.splice(findIndex, 1);
    }
  }
  deleteButton.href = originalDeleteHref + selectedRows.join('|');

  deleteButton.style.display = selectedRows.length > 0 ? 'inline-block' : 'none';
  editButton.style.display = selectedRows.length > 0 ? 'inline-block' : 'none';
};
