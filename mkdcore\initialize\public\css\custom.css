/**Framework7 Icons Pack**/
@font-face {
  font-family: 'Framework7 Icons';
  font-style: normal;
  font-weight: 400;
  src: local('Framework7 Icons'), local('Framework7Icons-Regular'), url('../fonts/Framework7Icons-Regular.woff2') format('woff2'),
    url('../fonts/Framework7Icons-Regular.woff') format('woff'), url('../fonts/Framework7Icons-Regular.ttf') format('truetype');
}

.f7-icons,
.framework7-icons {
  font-family: 'Framework7 Icons';
  font-weight: normal;
  font-style: normal;
  font-size: 28px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-feature-settings: 'liga';
  -moz-font-feature-settings: 'liga=1';
  -moz-font-feature-settings: 'liga';
  font-feature-settings: 'liga';
}

.page-content {
  overflow-x: hidden !important;
}

.customButton {
  display: block;
  height: 40px;
  width: 92%;
  margin-inline: auto;
}

/**Logo Styling**/
.coparent-logo {
  display: block;
  margin-inline: auto;
  width: 50%;
}

.logoText {
  margin-block: 15px;
  font-size: 20px;
}

.coparent-logo-screen {
  width: 45%;
}

.logoText-screen {
  margin-block: 10px;
  min-height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
}

/**TopBar Styling**/
.left {
  width: 15%;
  justify-content: flex-start;
}

.panel-toggle {
  padding-left: 15px;
  font-size: 25px;
}

.title {
  width: 60%;
  justify-content: center;
}

.right {
  width: 15%;
  justify-content: flex-end !important;
}

.right a {
  padding-right: 15px;
}

.right a i {
  font-size: 25px;
}

/**SideNavrBar Content Styles**/
.sidenavBar {
  min-height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding-bottom: 10px;
  margin-top: 0;
  padding-top: 10px;
}

.sidebarNavIcons {
  font-size: 20px;
}

.navBarmenulist {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}

.menu-list .item-link {
  margin-inline: 0;
}

.sideNavBarBottomButtonContainer {
  margin-top: 0;
}

/**Profile page CSS**/
.profile-initialsofName {
  font-size: 30px;
}

.profile-header-row {
  justify-content: center;
  padding-top: 20px;
}

.profile-header {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.profile-namecontainer {
  margin-block: auto;
}

.circle {
  background-image: url('../uploads/placeholder.jpg');
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  width: 100px;
  height: 100px;
  border: 1px solid #bbbbbb;
  border-radius: 50%;
  text-align: center;
}

.profile-namecontainer-text {
  font-size: 20px;
  margin: 5px 0 20px;
}

.toggle-container {
  margin-block: 20px;
  padding-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 60px;
}

/**Expenses Screen Styling**/
.expensesChoseCohortSelect {
  border: 1px solid #bbbbbb;
  border-radius: 5px;
  width: 92%;
  margin-inline: auto;
}

.amount-container-text {
  font-size: 20px;
  color: gray;
}

.expenseRemainCardHeader {
  background-color: #8e8e93;
  color: white;
}

.expenseRemainingCard {
  margin-block: 30px;
}

.tab-switch-container {
  justify-content: center;
  margin-block: 30px;
  margin-inline: 16px;
}

.singleExpense {
  margin-top: 0;
  margin-bottom: 30px;
}

.singleExpenseCardHeader {
  background-color: #8e8e93;
  color: white;
}

.info {
  margin-block: 0;
}

.expenseInfoContainer {
  padding-block: 20px;
  padding-left: 0;
}

.expenseInfoCallToAction {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.tab-link {
  width: 50%;
}

/* 
.tab-link-active {
    background-color: #007AFF;
    color: white;
}

.tab-link-active button {
    color: white;
} */

/* .tab-link-active {
    color: white;
}

.button-active {
    background-color: #2196f3;
    color: white;
} */

/**Add Expense Styles **/
.smart-select-label {
  padding-left: 4%;
  padding-bottom: 1%;
}

.smart-select-list {
  min-height: 85px;
  display: flex;
  flex-direction: column;
  align-content: center;
  justify-content: center;
}

.addexpense-date {
  border: 1px solid #bbbbbb;
  border-radius: 5px;
  width: 96% !important;
  margin-inline: auto;
  padding-left: 3%;
}

.custom-date-label {
  padding-bottom: 1%;
}

.image-card-header {
  background-image: url(https://cdn.framework7.io/placeholder/nature-1000x600-3.jpg);
  min-height: 180px;
  color: white;
}

#signin {
  display: block;
  height: 40px;
  width: 92%;
  margin-inline: auto;
}

#register {
  display: block;
  height: 40px;
  width: 92%;
  margin-inline: auto;
}

#registerButton {
  display: block;
  height: 40px;
  width: 92%;
  margin-inline: auto;
}

#backtoLogin {
  display: block;
  height: 40px;
  width: 92%;
  margin-inline: auto;
}

.cohart-selection-container {
  display: flex;
  justify-content: center;
}

/**NewOrJoin Styles**/
.neworjoinheading {
  text-align: center;
  font-size: 20px;
  margin-bottom: 25px;
}

/**Join Cohort Styles**/
.joinheading {
  text-align: center;
  font-size: 20px;
  margin-bottom: 25px;
}

/**Create Cohort Styles**/
.create-cohort-proceed-button {
  display: block;
  height: 40px;
  width: 92%;
  margin-inline: auto;
}

.custom-size-35 {
    width: 35% !important;
}

/**Add Child Styles**/
.addchild-createbutton {
  display: block;
  height: 40px;
  width: 92%;
  margin-inline: auto;
}

/**Invite CoParent Styles**/
.inviteCoParentbutton {
  display: block;
  height: 40px;
  width: 92%;
  margin-inline: auto;
}

/**Shared Phone Styles**/
.search-button {
  display: block;
  height: 40px;
  width: 92%;
  margin-inline: auto;
}

/**Shared Email Styles**/
.email-selection-button {
  display: block;
  height: 40px;
  width: 92%;
  margin-inline: auto;
}

/**Swap Styles**/
.singleSwapCardHeader {
  background-color: #8e8e93;
  color: white;
}

.singleSwap {
  margin-top: 0;
  margin-bottom: 30px;
}

.swapInfoContainer {
  padding-block: 20px;
  padding-left: 0;
  padding-right: 20px;
}

.swapInfoCallToAction {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/**View Children Styles**/
.child-list {
  margin-bottom: 30px !important;
}

.childCard {
  margin-top: 0;
  margin-bottom: 30px;
}

.childrenChoseCohortSelect {
  border: 1px solid #bbbbbb;
  border-radius: 5px;
  width: 92%;
  margin-inline: auto;
}

.editChildButton {
  display: block;
  height: 40px;
  margin-inline: auto;
}

.childCardHeader {
  background-color: #8e8e93;
  color: white;
}

/**Edit Child Styles**/
.editchild-createbutton {
  display: block;
  height: 40px;
  width: 92%;
  margin-inline: auto;
}

.edit-child-edit-button {
  height: 40px;
}

/**Add Children Secondary Styles**/
.addchildrenChoseCohortSelect {
  border: 1px solid #bbbbbb;
  border-radius: 5px;
  width: 92%;
  margin-inline: auto;
}

.cohart-selection-item {
  padding-block: 3px;
  padding-inline: 5px;
}

.add-child-create-button {
  height: 40px;
}

/**Expense Report Popup Styles**/
.expenseReport-header {
  background-color: #8e8e93;
  color: white;
}

.view-expense-details-close-button {
  height: 40px;
}

.row {
  align-items: center;
  margin-block: 10px;
}

.amount-container {
  text-align: center;
}

.dues-container-row {
  border: 1px solid #bbb;
  margin-inline: 10px;
  height: 80px;
}

.settle-container-button {
  width: 100px;
  border: 1px solid #bbb;
  background: transparent;
  padding: 5px 20px;
  box-shadow: #bbb;
  box-shadow: 1px 1px #bbb;
}

.amount-container {
  font-size: 20px;
  font-weight: bold;
}

.unsettled-container-button {
  background: transparent;
  box-shadow: 1px 1px #bbb;
  border: 1px solid #bbb;
  padding-block: 5px;
}

.completed-container-button {
  background: transparent;
  box-shadow: 1px 1px #bbb;
  border: 1px solid #bbb;
  padding-block: 5px;
}

.completed-container-button:active {
  background-color: lightskyblue;
}

.unsettled-container-button:active {
  background-color: lightskyblue;
}

.settleup-expensetabs {
  border: 1px solid #bbb;
}

.expenses-tabs-container {
  margin-block: 20px;
  border: 1px solid #bbb;
  align-items: baseline;
}

.expenses-tabs-infocontainer {
  padding-left: 5px;
}

.expenses-tabs-actioncontainer-view {
  background: transparent;
  box-shadow: 1px 1px #bbb;
  border: 1px solid #bbb;
  padding-block: 5px;
  margin-top: 5px;
  margin-bottom: 5px;
  margin-right: 3px;
  width: 95%;
}

.expenses-tabs-actioncontainer-download {
  background: transparent;
  box-shadow: 1px 1px #bbb;
  border: 1px solid #bbb;
  padding-block: 5px;
  margin-top: 5px;
  margin-bottom: 5px;
  margin-right: 3px;
  width: 95%;
}

.upload-recipts-button {
  text-align: center;
  background-color: transparent;
  border: none;
  padding-block: 10px;
  margin: 0 auto;
  display: flex;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
}

.upload-recipts-button:active {
  background-color: lightskyblue;
}

.addExpensesUpload {
  display: flex;
  justify-content: center;
  align-items: center;
}

#upload-reciepts {
  margin-left: auto;
}

.settle-settle-container-button {
  width: 85%;
  border: 1px solid #bbb;
  background: transparent;
  padding: 5px 20px;
  box-shadow: #bbb;
  box-shadow: 1px 1px #bbb;
}

.settle-expenses-tabs-container {
  align-items: center;
}

.margin-bottom-20 {
  margin-bottom: 30px;
}

.h-80 {
  height: 80px;
}

/* .view-cohart-item {
    margin-inline: 5px;
    padding-bottom: 10px;
} */

.view-cohart-item p {
  margin-block: 0;
  /* padding: 5px 0 0; */
  font-weight: 400;
  margin-left: 0;
}

/* .viewcohart-manage-button {
    background-color: transparent;
    border: 1px solid #bbb;
    padding-block: 10px;
    width: 95%;
    margin: 5px;
    display: block;
    margin-inline: auto;
}

.viewcohart-invite-button {
    background-color: transparent;
    border: 1px solid #bbb;
    padding-block: 10px;
    width: 95%;
    margin: 5px;
    display: block;
    margin-inline: auto;
} */

.viewcohart-manage-button {
  margin-top: 10px;
  margin-bottom: 10px;
}

.viewcohart-invite-button {
  margin-top: 10px;
}

.view-cohart-item-heading {
  font-size: 20px;
  font-style: normal;
  font-weight: 400;
  /* margin-left: 5px; */
  margin-top: 0;
  margin-bottom: 5px;
}

.view-cohart-item-phone {
  font-size: 15px;
  font-style: normal;
  font-weight: 500;
  margin-left: 5px;
}

.view-cohart-item-email {
  font-size: 15px;
  font-style: normal;
  font-weight: 500;
  margin-left: 5px;
}

.view-cohart-item-deliverheading {
  font-size: 15px;
  font-style: normal;
  font-weight: 600;
  /* margin-left: 5px; */
  margin-top: 10px;
  margin-bottom: 5px;
}

.view-cohart-item-delieverto {
  margin-block: 0;
}

.view-cohart-item-delieverto {
  padding-block: 10px;
}

.view-cohart-item-delieverto-first {
  overflow: hidden;
  margin: 0;
}

.view-cohart-item-delieverto-second {
  overflow: hidden;
  margin: 0;
}

.edit-cohort-email-invite-button {
  display: block;
  margin: 10px auto;
  border: 1px solid #bbb;
  border-radius: 5px;
  width: 90%;
  padding-block: 15px;
  background-color: transparent;
  margin-left: auto;
  margin-right: auto;
}

.edit-cohort-sms-invite-button {
  display: block;
  margin: 10px auto;
  border: 1px solid #bbb;
  border-radius: 5px;
  width: 90%;
  padding-block: 15px;
  background-color: transparent;
  margin-left: auto;
  margin-right: auto;
}

/* .phone-selection {
    width: max-content;
    display: block;
    margin-inline: auto;
    text-align: center;
    border: 1px solid black;
    padding: 15px 40px
} */

.phone-selection {
  border: 1px solid black;
}

.viewcohort-icon {
  font-size: 20px;
}

.viewcohort-infomessagecontainer {
  margin-block-start: 0;
  margin-block-end: 16px;
}

/* .viewcohort-infomessage-text {
    text-align: center;
    font-size: 20px;
} */

/* .viewcohort-infomessage-button {
    padding-block: 15px;
    background-color: transparent;
    margin-top: 20px;
    border: 1px solid #bbbbbb;
    display: block;
    margin-inline: auto;
    width: 70%;
    color: black;
    text-align: center;
} */

/* .create-cohort-proceed-button {
    text-align: center;
    border: 1px solid #bbbbbb;
    padding: 15px 20px;
    display: block;
    margin-inline: auto;
    width: 70%;
    color: rgba(0, 0, 0, 0.65);
    border-radius: 5px;
    margin-top: 20px;
} */

.create-cohort-phone-selection {
  border: 1px solid #bbbbbb;
  padding: 5px 20px;
  display: block;
  margin-inline: auto;
  color: #bbbbbb;
}

.phone-selection-button {
  padding-block: 15px;
  background-color: transparent;
  margin-top: 20px;
  border: 1px solid #bbbbbb;
  display: block;
  margin-inline: auto;
  width: 70%;
  color: #bbbbbb;
}

.phone-selection {
  padding-inline: 10px !important;
  display: block;
  width: 90% !important;
  margin-inline: auto;
  border: 1px solid #bbbbbb;
}

/* .email-selection-button {
    padding-block: 15px;
    background-color: transparent;
    margin-top: 20px;
    border: 1px solid #bbbbbb;
    display: block;
    margin-inline: auto;
    width: 70%;
    color: #bbbbbb;
} */

.logouttext {
  font-size: 15px;
  margin-right: 20px;
  color: black;
  font-weight: bold;
}

/* .search-button {
    width: 90%;
    display: block;
    margin-inline: auto;
    padding: 10px 0;
    background-color: transparent;
    border: 1px solid #bbbbbb;
    border-radius: 5px;
} */

.number-container {
  min-height: 250px;
  margin-block: 20px;
  margin-inline: 20px;
  padding-left: 10px;
  border: 1px solid #bbbbbb;
}

.number-listitems {
  margin-block: 10px;
  display: flex;
  gap: 30px;
  justify-content: flex-start;
  align-items: center;
}

/* .addchild-createbutton {
    justify-content: center;
    border: 1px solid #bbb;
    padding: 10px 0;
    width: 90%;
    display: block;
    text-align: center;
    margin-inline: auto;
    margin-block: 20px;
    color: black;
} */

.domainurl {
  position: absolute;
  top: 7px;
  left: 200px;
  color: gray;
  cursor: none;
}

.percentagesymbol {
  position: absolute;
  top: 7px;
  right: 40px;
  color: gray;
  cursor: none;
}

.parent1email {
  word-break: break-all;
}

.parent1phone {
  word-break: break-all;
}

.parent2email {
  word-break: break-all;
}

.parent2phone {
  word-break: break-all;
}

.color-black {
  color: black;
}

/* .settleup-popup {
    width: 90%;
    height: 60%;
    position: absolute;
    top: 25%;
    border-radius: 5px;
    left: 5%;
} */

.settleup-popup-submitbutton {
  margin-top: 20px;
  width: 93%;
  display: block;
  margin-inline: auto;
}

.settleup-popup-close-button {
  position: absolute;
  z-index: 999;
  right: 2%;
  top: -20%;
}

.custom-size-20 {
  font-size: 20px;
}

.settleup-popup-list {
  margin-block: 20%;
}

.standard-tab-button {
  background-color: transparent;
  border: 1px solid #bbb;
  padding-block: 10px;
}

.swap-tab-switch-container {
  margin: 30px 0 60px 0;
}

.balance-row {
  min-height: 100px;
  margin-block: 30px;
  width: 95%;
  margin-inline: auto;
  border: 1px solid #bbb;
}

.left-container {
  text-align: center;
}

.right-container {
  text-align: center;
}

.expenses-noexpensemessage {
  margin-inline: 30px;
  margin-top: 50px;
  text-align: center;
  font-weight: 600;
}

.center-box {
  display: flex;
  align-items: center;
  justify-content: center;
}

.form-btn {
  border: none;
  outline: none;
  background-color: transparent;
}

.tab .page-content {
  padding-top: 0;
}

.item-input-outline .item-label {
  left: 0;
  margin-bottom: 5px;
}

.viewExpense-popup-close-button {
  position: relative;
  z-index: 999;
  left: 95%;
}

.invitepage-invitebutton {
  display: block;
  width: 92%;
  height: 40px;
  margin-inline: auto;
  margin-block: 20px;
}

.view-cohort-cohort-email {
  word-break: break-all;
}

.view-cohort-cohort-email-container {
  align-self: flex-start;
}

.swapSuperOptionSelector {
  padding: 0;
  width: 92%;
  margin-inline: auto;
  margin-top: 30px;
  margin-bottom: 0;
}

.swapSubOptionSelector {
  padding: 0;
  width: 92%;
  margin-inline: auto;
  margin-top: 30px;
  margin-bottom: 30px;
}

.viewFiles-infoCol {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 20px;
}

.files-list {
  margin-bottom: 30px !important;
}

.viewChildren-cardItem {
  padding: 20px;
}

.viewChildren-cardItem-age {
  margin-bottom: 20px;
}

.viewChildren-cardItem-name {
  margin-bottom: 20px;
  overflow: visible;
}

.expense-details-pop-note {
  overflow: visible;
  word-break: break-word;
}

.swap-apporoved-check-button {
  display: block;
  margin-bottom: 30px;
  height: 40px;
  width: 92%;
  margin-inline: auto;
}

.infoSwap {
  overflow: visible;
}

.infoFiles {
  height: auto;
}

.page-start-padding {
  margin-top: 10px !important;
}

.infoSwapTimeType {
  text-transform: capitalize;
}

th {
  text-transform: capitalize;
}
