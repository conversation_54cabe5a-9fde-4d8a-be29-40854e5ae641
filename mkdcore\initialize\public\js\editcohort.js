// async function addCohort() {
//     let parentOne = document.getElementById("edit-cohort-parent1").value;
//     let parentTwo = document.getElementById("edit-cohort-parent2").value;
//     let email = document.getElementById("edit-cohort-email").value;
//     let phone = document.getElementById("edit-cohort-phone").value;
//     let parentOneSplit = document.getElementById("edit-cohort-parent1split").value;
//     let parentTwoSplit = document.getElementById("edit-cohort-parent2split").value;

//     let body = {
//         'parent1': parentOne,
//         'parent2': parentTwo,
//         'email': email,
//         'phone': phone,
//         'parent1split': parentOneSplit,
//         'parent2split': parentTwoSplit
//     }
//     let response = await fetch('', {
//         method: 'POST',
//         headers: {
//             Accept: 'application/json',
//             'Content-Type': 'application/json',
//         },
//         body: JSON.stringify(body)
//     })

//     let resJSON = await response.json();
//     console.log(resJSON)
// }

// async function getCurrentValues() {
//     alert("I Will add current Data")
// }