<!DOCTYPE html>
<html>

<head>
  <!-- Required meta tags-->
  <meta charset="utf-8" />
  <meta name="viewport"
    content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no, viewport-fit=cover" />
  <meta name="apple-mobile-web-app-capable" content="yes" />
  <!-- Color theme for statusbar (Android only) -->
  <meta name="theme-color" content="#2196f3" />
  <!-- Your app title -->
  <title>Co-Parent</title>
</head>

<body>
  <!-- App root element -->
  <div id="app">
    <div class="page" data-name="swap">
      <!-- Top Navbar -->
      <div class="navbar">
        <div class="navbar-bg"></div>
        <div class="navbar-inner">
          <div class="left">
            <a href="#" class="panel-toggle color-black">&#9776;</a>
          </div>
          <div class="title text-align-center">Co-Swap</div>
          <div class="right">
            <a href="/swapRequest/"><i class="f7-icons color-black">plus_circle</i></a>
          </div>
        </div>
      </div>

      <!-- Scrollable page content -->
      <div class="page-content">
        <div class="list no-hairlines no-margin-vertical">
          <ul class="page-start-padding">

            <li class="smart-select-list justify-content-flex-end">
              <div class="item-title item-label smart-select-label">Choose Co-Hort</div>
              <a class="item-link smart-select smart-select-init" data-open-in="sheet" data-close-on-select="true">
                <select name="selected-cohort" onchange="getSwaps()" id="swap-cohort-drop-filter" required validate
                  data-validate-on-blur="true">

                </select>
                <div class="item-content expensesChoseCohortSelect swapChoseCohortSelect">
                  <div class="item-inner">
                    <div class="item-title">Choose Co-Hort</div>
                  </div>
                </div>
              </a>
            </li>

            <div class="segmented segmented-raised block swapSuperOptionSelector">
              <a href="#balance" class="tab-link button button-outline">Swap Bank</a>
              <a href="#pending" class="tab-link button button-outline">Pending</a>
              <a href="#upcoming" class="tab-link button button-outline">Upcoming</a>
              <a href="#past_swaps" class="tab-link button button-outline">Previous</a>
            </div>
              
            <div class="tabs-animated-wrap">
              <div class="tabs">
                <div class="tab page-content tab-active no-padding-top" id="balance">
                  <div class="block segmented segmented-raised swapSubOptionSelector" id="parent-balance-tabs">
                    <a href="#parent1-balance" class="tab-link button">Parent 1</a>
                    <a href="#parent2-balance" class="tab-link button">Parent 2</a>
                  </div>

                  <div class="tabs">
                    <div class="tab page-content tab-active" id="parent1-balance"></div>
                    <div class="tab page-content" id="parent2-balance"></div>
                  </div>
                </div>
                <div class="tab page-content" id="pending">
              
                  <div class="tab page-content tab-active" id="parent1-pending"></div>
                  
                </div>
                <div class="tab page-content" id="upcoming">
                  <div class="tab page-content tab-active" id="parent1-upcoming"></div> 
                </div>
                <div class="tab page-content no-padding-top" id="past_swaps">
                  <div class="block segmented segmented-raised swapSubOptionSelector" id="parent-past-tabs">
                    <a href="#parent1-past" class="tab-link button">Parent 1</a>
                    <a href="#parent2-past" class="tab-link button">Parent 2</a>
                  </div>

                  <div class="tabs">
                    <div class="tab page-content tab-active" id="parent1-past"></div>
                    <div class="tab page-content" id="parent2-past"></div>
                  </div>
                </div>
              </div>
            </div>
          </ul>
        </div>
      </div>
</body>

</html>