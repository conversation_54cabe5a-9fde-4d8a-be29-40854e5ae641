const app = require('express').Router();
const { Op } = require('sequelize');
const db = require('../../models');
const { protect } = require('../../middlewares/auth_middleware.js');
const { getAliases, create<PERSON>lia<PERSON> } = require('../../services/ForwardEmailService');

app.get('/api/user/get_shared_email/:cohort_id', protect, async function (req, res, next) {
  try {
    await getAliases();
    const user = req.user;
    const { cohort_id } = req.params;
    const belongToCohort = await db.cohort.findOne({
      where: {
        [Op.or]: {
          parent_1: user.id,
          parent_2: user.id,
        },
        id: cohort_id,
      },
    });

    if (!belongToCohort) {
      return res.status(404).json({
        status: 'fail',
        message: 'Cohort not found',
      });
    }
    res.status(200).json({
      status: 'success',
    });
  } catch (error) {
    return res.status(400).json({
      error: true,
      message: error.message,
    });
  }
});

app.post('/api/user/set_shared_email/:cohort_id', protect, async function (req, res, next) {
  try {
    const user = req.user;
    const { cohort_id } = req.params;
    const { name } = req.body;
    const belongToCohort = await db.cohort.findOne({
      where: {
        [Op.or]: {
          parent_1: user.id,
          parent_2: user.id,
        },
        id: cohort_id,
      },
    });

    if (!belongToCohort) {
      return res.status(404).json({
        status: 'fail',
        message: 'Cohort not found',
      });
    }

    let isExist = await db.cohort.findOne({where:{
      shared_email: `${name}@cphsend.com`
    }});

    if(isExist){
      return res.status(404).json({
        status: 'fail',
        message: 'This co-email is already taken',
      });
    }

    const parent1 = await db.user.get_user_credential(belongToCohort.parent_1, db);
    let parent2 = null;
    if (belongToCohort.parent_2) {
      parent2 = await db.user.get_user_credential(belongToCohort.parent_2, db);
    }
    const recipients = `${parent1?.credential?.email || ''} ${parent2?.credential?.email || ''}`;
    const created_alias = await createAlias({ name, recipients });
    await db.cohort.edit({ shared_email: `${name}@cphsend.com` }, cohort_id);

    res.status(200).json({
      status: 'success',
      created_alias,
    });
  } catch (error) {
    return res.status(400).json({
      error: true,
      message: error.message,
    });
  }
});

module.exports = app;
