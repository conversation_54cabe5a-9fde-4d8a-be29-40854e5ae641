<% if(it.layout_clean_mode) {%>
  <% layout("../layouts/admin/Clean") %>
  <% } else {%>
  <% layout("../layouts/admin/Main") %>
  <%}%>
  
  
  <div class="tab-content mx-4 my-4" id="nav-tabContent">
  
  <%~ includeFile("../partials/admin/GlobalResponse.eta", it) %>
  
  <section>
  <div class="row">
      <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
          <div class="card" id="expense_filter_listing">
              <div class="card-body">
                <h5 class="primaryHeading2 text-md-left">
                      <%= it.get_heading() %> Search
                </h5>
                  <form action="/admin/expenses/0" method="get" accept-charset="utf-8">
                      <div class="row">
                      
  <div class="col-xl-4 col-lg-4 col-md-4 col-sm-12 col-12">
  
  <div class="form-group">
  
    <label for="ID">ID</label>
  
    <input type="text" class="form-control" id="id" name="id" value="<%= it.get_id() %>" onkeypress="return event.charCode >= 48 && event.charCode <= 57"/>
  
  </div>
  
  </div>
  
  <div class="col-xl-4 col-lg-4 col-md-4 col-sm-12 col-12">
  
  <div class="form-group">
  
    <label for="Cohort">Co-Hort</label>
  
    <input type="text" class="form-control" id="cohort_id" name="cohort_id" value="<%= it.get_cohort_id() %>" onkeypress="return event.charCode >= 48 && event.charCode <= 57"/>
  
  </div>
  
  </div>
  
  <div class="col-xl-4 col-lg-4 col-md-4 col-sm-12 col-12">
  
  <div class="form-group">
  
    <label for="Date">Date</label>
  
    <input type="date" class="form-control" id="date" name="date" value="<%= it.get_date() %>"/>
  
  </div>
  
  </div>
  
  <div class="col-xl-4 col-lg-4 col-md-4 col-sm-12 col-12">
  
  <div class="form-group">
  
    <label for="Co-Expense Name">Co-Expense Name</label>
  
    <input type="text" class="form-control" id="expense_name" name="expense_name" value="<%= it.get_expense_name() %>"/>
  
  </div>
  
  </div>
  
  <div class="col-xl-4 col-lg-4 col-md-4 col-sm-12 col-12">
  
  <div class="form-group">
  
    <label for="Paid By">Paid By</label>
  
    <input type="text" class="form-control" id="paid_by" name="paid_by" value="<%= it.get_paid_by() %>"/>
  
  </div>
  
  </div>
  
  <div class="col-xl-4 col-lg-4 col-md-4 col-sm-12 col-12">
  
  <div class="form-group">
  
    <label for="Amount">Amount</label>
  
    <input type="text" class="form-control" id="amount" name="amount" value="<%= it.get_amount() %>"/>
  
  </div>
  
  </div>
  
  <div class="col-xl-4 col-lg-4 col-md-4 col-sm-12 col-12">
  
  <div class="form-group">
  
    <label for="Name Children">Children</label>
  
    <input type="text" class="form-control" id="children" name="children" value="<%= it.get_children() %>"/>
  
  </div>
  
  </div>
  
  <div class="col-xl-4 col-lg-4 col-md-4 col-sm-12 col-12">
  
  <div class="form-group">
  
    <label for="Status">Status</label>
  
    <select name="status" class="form-control">
  
      <option value="">All</option>
  
      <% Object.keys(it.status_mapping()).forEach(function(value){ %>
  
        <option value="<%= value %>" <%= (it.get_status() == value && it.get_status() != '') ? 'selected' : '' %> > <%= it.status_mapping()[value]%> </option>
      <%}); %>
  
    </select>
  
  </div>
  
  </div>
  
                      <div style="width:100%;height:10px;display:block;float:none;"></div>
                          <div class="col-xl-4 col-lg-4 col-md-4 col-sm-12 col-12">
                             <div class="form-group">
                                  <input type="submit" name="submit" class="btn btn-primary" value="Search">
                                  <button id="filter-clear-search" style="width: 50%;" class="btn btn-secondary ml-2">Clear Search</button>
  
                              </div>
                          </div>
                      </div>
                  </form>
              </div>
          </div>
      </div>
  </section>
  
  
  
  <div class="d-flex align-items-center justify-content-between">
    <h5 class="primaryHeading2 d-flex justify-content-between mt-2 my-4">
      <%= it.get_heading() %>
    </h5>
  
    <div class="d-flex align-items-center">
       
      
      <span class="add-part d-flex justify-content-md-end   ml-1"><a class="btn btn-primary btn-sm" target="__blank" href="/admin/expenses-add"><i class="fas fa-plus-circle"></i></a></span>
    </div>
  </div>
  
  
  <section class="table-placeholder bg-white mb-5 p-3 pl-4 pr-4 pt-4" style='height:auto;'>
      <div class="mb-2 d-flex align-items-center justify-content-between d-none">
        <div>
          <small class="d-flex align-items-baseline">
            Show <select name="page_length" class="form-control form-control-sm mx-2" style="max-width: 60px;" onchange="window.location='0?per_page='+this.value">
              <option <%= it.get_per_page() == 10 ? 'selected' : '' %> >10</option>
              <option <%= it.get_per_page() == 25 ? 'selected' : '' %> >25</option>
              <option <%= it.get_per_page() == 50 ? 'selected' : '' %> >50</option>
              <option <%= it.get_per_page() == 100 ? 'selected' : '' %> >100</option>
            </select>
             entries
          </small>
        </div>      
  
        <div class="d-flex align-items-center">
          
        </div>
      </div>
      <div class="table-responsive">
      <table class="table table-mh br w-100 table-bordered table-striped">
          <thead class='thead-white text-nowrap'>
            
            <% it.get_column().forEach(function(data, index) { %>
              <% if ( data == "Image" || it.get_order_by().length < 1 || it.get_field_column()[index] == '' || it.get_field_column()[index] == undefined) { %>
                <th scope="col" class="paragraphText text-left"><%= data %></th>
              <% } else { %>
                <% if (it.get_order_by() == it.get_field_column()[index]) { %>
                  <% if (it.get_sort() == 'ASC') { %>
                    <th scope='col' class='paragraphText text-left'>
                      <a class="text-dark" href="<%= it.get_sort_base_url() + '&order_by=' + it.get_field_column()[index] + '&direction=DESC' %>"><%= data %>
                        <% /* <i class='fas fa-sort-up' style='vertical-align: -0.35em;'></i> */ %>
                        <i style="margin-top: 0.45rem;float:right;color: #aaa;font-size: small;" class="fas fa-long-arrow-alt-down"></i>                    
                        <i style="margin-top: 0.45rem;float:right;font-size: small;" class="fas fa-long-arrow-alt-up"></i>
                      </a>
                    </th>
                  <% } else { %>
                    <th scope='col' class='paragraphText text-left'>
                      <a class="text-dark" href="<%= it.get_sort_base_url() + '&order_by=' + it.get_field_column()[index] + '&direction=ASC' %>"><%= data %>                      
                      <i style="margin-top: 0.45rem;float:right;font-size: small;" class="fas fa-long-arrow-alt-down"></i>
                      <i style="margin-top: 0.45rem;float:right;color: #aaa;font-size: small;" class="fas fa-long-arrow-alt-up"></i>
                      </a>
                    </th>
                  <% } %>
              <% } else { %>
                  <th scope='col' class='paragraphText text-left'>
                    <a class="text-dark" href="<%= it.get_sort_base_url() + '&order_by=' + it.get_field_column()[index] +  '&direction=ASC' %>"><%= data %>
                      <i style="margin-top: 0.45rem;float:right;color: #aaa;font-size: small;" class="fas fa-long-arrow-alt-down"></i>
                      <i style="margin-top: 0.45rem;float:right;color: #aaa;font-size: small;" class="fas fa-long-arrow-alt-up "></i>
                    </a>
                  </th>
                <% } %>
              <% } %>
            <% }) %> 
            </thead>
          <tbody class="tbody-light">
            <% it.get_list().forEach(function(data) { %>
                <tr>
                  
                  
                <td> 
                <%= data.id %>
                </td>
              
  
  
                <td> 
                <%= new Date(data.date).toLocaleString("en-us",{month:"short",day:"numeric",year:"numeric"}) %>
                </td>
              
  
  
                <td> 
                <%= data.cohort_id %>
                </td>
              
  
  
                <td> 
                <%= data.expense_name %>
                </td>
              
  
  
                <td> 
                <%= data.paid_by %>
                </td>
              
  
  
                <td> 
                <%= data.amount %>
                </td>
              
  
  
                <td> 
                <%= data.children %>
                </td>
              
  
  
                <td> 
                <%= data.notes %>
                </td>
              
  
  
                <td> 
                <a href="/uploads/<%= data.receipt %>" target="_blank"><%= data.receipt %></a> 
                </td>
              
  
  
                <td> 
                <%= it.ucFirst(it.status_mapping()[data.status]) %>
                </td>
              
  
  <td><a class="btn btn-link  link-underline text-underline  btn-sm" target="_blank" href="/admin/expenses-view/<%= data.id %>">View</a>&nbsp;<a target="_blank" class="btn btn-link  link-underline text-underline  btn-sm" href="/admin/expenses-edit/<%= data.id %>">Edit</a>&nbsp;<a class="btn btn-link  link-underline text-underline text-danger btn-sm" href="/admin/expenses-delete/<%= data.id %>">Delete</a>&nbsp;</td>
                </tr>
            <% }) %>
          </tbody>
      </table>
      <p class="pagination_custom"><%~ it.get_links() %></p>
      </div>
  </section>
  </div>
  