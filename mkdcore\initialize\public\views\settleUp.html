<!DOCTYPE html>
<html>

<head>
    <!-- Required meta tags-->
    <meta charset="utf-8">
    <meta name="viewport"
        content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no, viewport-fit=cover">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <!-- Color theme for statusbar (Android only) -->
    <meta name="theme-color" content="#2196f3">
    <!-- Your app title -->
    <title>Co-Parent</title>
</head>

<body>
    <!-- App root element -->
    <div id="app">

        <div class="page" data-name="settleUp">

            <!-- Top Navbar -->
            <div class="navbar">
                <div class="navbar-bg"></div>
                <div class="navbar-inner">
                    <div class="left">
                        <a class="link back">
                            <i class="icon icon-back color-black"></i>
                            <span class="if-not-md">Back</span>
                        </a>
                    </div>
                    <div class="title text-align-center">
                        Settle Up
                    </div>
                    <div class="right">

                    </div>
                </div>
            </div>

            <!-- Scrollable page content -->
            <div class="page-content no-swipe-panel">
                <div class="list no-hairlines">

                    <div class="card expenseRemainingCard">
                        <div class="card-header expenseRemainCardHeader">Your Remaining Dues</div>
                        <div class="card-content card-content-padding">
                            <div class="row">
                                <div class="col-50" id="settleup-amount-payablecontainer">
                                    <!--This is a Placeholder, Value is coming from Javascript-->
                                </div>
                                <div class="col-50">
                                    <a href="#" class="popup-open" data-popup=".settleup-popup"
                                        onclick="intializeInput()">
                                        <button class="button button-raised button-fill">
                                            Approve Checked
                                        </button>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="settleup-expensecontainer" id="settleup-expensecontainer">
                        <!-- This is a placeholder, data is coming from Javascript-->
                    </div>

                </div>
            </div>

            <div class="popup settleup-popup">
                <div class="block">
                    <span class="settleup-popup-close-button">
                        <a href="" class="popup-close">
                            <i class="f7-icons color-black custom-size-20">xmark</i>
                        </a>
                    </span>
                    <div class="list no-hairlines settleup-popup-list">

                        <ul>

                            <li>
                                <div class="item-content item-input">
                                    <div class="item-inner">
                                        <div class="item-title item-label custom-date-label">Date</div>
                                        <div class="item-input-wrap addexpense-date">
                                            <input type="text" placeholder="Select Date" readonly="readonly"
                                                id="settleup-popup-datepaid" required validate
                                                validate-on-blur="true" />
                                        </div>
                                    </div>
                                </div>
                            </li>

                            <li class="item-content item-input item-input-outline">
                                <div class="item-inner">
                                    <div class="item-title item-label">Amount</div>
                                    <div class="item-input-wrap">
                                        <input type="number" name="amount" id="settleup-popup-amountpaid" disabled />
                                        <span class="input-clear-button"></span>
                                    </div>
                                </div>
                            </li>

                            <li class="smart-select-list">
                                <div class="item-title item-label smart-select-label">Payment Method</div>
                                <a class="item-link smart-select smart-select-init" data-open-in="sheet"
                                    data-close-on-select="true">
                                    <select placeholder="Please choose..." id="settleup-popup-paymentmethod">
                                        <option value="etransfer" data-paymentmethod="1">E Transfer</option>
                                        <option value="creditcard" data-paymentmethod="2">Credit Card</option>
                                        <option value="debit" data-paymentmethod="3">Debit</option>
                                        <option value="banktransfer" data-paymentmethod="4">Bank Transfer</option>
                                    </select>
                                    <div class="item-content expensesChoseCohortSelect">
                                        <div class="item-inner">
                                            <div class="item-title">Payment Method</div>
                                        </div>
                                    </div>
                                </a>
                            </li>

                            <a onclick="settleUpExpense()" target="_blank">
                                <button class="button button-raised button-fill settleup-popup-submitbutton">
                                    Submit
                                </button>
                            </a>

                        </ul>

                    </div>
                </div>
            </div>


        </div>

    </div>

</body>

</html>