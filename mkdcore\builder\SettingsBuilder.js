/*Powered By: Manaknightdigital Inc. https://manaknightdigital.com/ Year: 2020*/
/**
 * Copy builder
 * @copyright 2020 Manaknightdigital Inc.
 * @link https://manaknightdigital.com
 * @license Proprietary Software licensing
 * <AUTHOR>
 *
 */

const fs = require('fs');
const path = require('path');
const Builder = require('./Builder');
const fse = require('fs-extra');

const merge_package = require('../source/utils/helpers/merge-package');

module.exports = function (config) {
  this._config = config;
  this._render_list = [];
  Builder.call(this);

  /**
   * Copy graphql files into release folder
   */
  this.copy_graphql = function (settings) {
    if (settings.copy_graphql) {
      try {
        console.log('Copying graphql files...');

        const copies = [
          {
            src: path.join(__dirname, '../../', 'releaseGraphql', 'resolvers'),
            dst: path.join(__dirname, '../../', 'release', 'resolvers'),
          },
          // {
          //   src: path.join(__dirname, '../../releaseGraphql', 'routes'),
          //   dst: path.join(__dirname, '../../release', 'routes/api'),
          // },
          {
            src: path.join(__dirname, '../../', 'releaseGraphql', 'types'),
            dst: path.join(__dirname, '../../', 'release', 'types'),
          },
          {
            src: path.join(__dirname, '../../', 'releaseGraphql', 'app.js'),
            dst: path.join(__dirname, '../../', 'release', 'graphqlApp.js'),
          },
          // {
          //   src: path.join(__dirname, '../../', 'releaseGraphql', 'server.js'),
          //   dst: path.join(__dirname, '../../', 'release', 'graphqlServer.js'),
          // },
        ];

        let appJs = fs.readFileSync(path.join(__dirname, '..', 'initializeGraphql', 'server.js'), 'utf-8');

        appJs = appJs.replace(/const app([\s\S]*?)\)/g, `const app = require('./graphqlApp')`);

        this.writeFileSyncRecursive('../../release/graphqlServer.js', appJs, { mode: 0075 });

        copies.forEach((copy) => {
          fse.copySync(copy.src, copy.dst);
        });

        // merge package.json
        const dst_package = fs.readFileSync(path.join(__dirname, '../../release', 'package.json'));
        const src_package = fs.readFileSync(path.join(__dirname, '../initializeGraphql', 'package.json'));

        fs.writeFileSync(path.join(__dirname, '../../release', 'package.json'), merge_package(dst_package, src_package));

        console.log('Graphql files copied successfully into release folder.');
      } catch (error) {
        console.log('Error file copying graphql files:', error);
      }
    } else return null;
  };

  this.build = function () {
    this.copy_graphql(this._config.settings);
  };

  this.destroy = function () {};
};
