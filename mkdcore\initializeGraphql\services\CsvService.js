const { request, response } = require('express');

const multer = require('multer');
const csv = require('fast-csv');
const converter = require('json-2-csv');
const fs = require('fs');

const db = require('../../models');

module.exports = {
  /**
   * Import CSV file
   * @param {request} req
   * @param {response} res
   */
  csv_import: function (req, res) {
    try {
      const upload = multer({ dest: 'tmp/csv/' }).single('file');

      upload(req, res, async function (error) {
        if (error) {
          throw new Error(error);
        }
        const fileRows = [];

        csv
          .fromPath(req.file.path)
          .on('data', function (data) {
            fileRows.push(data); // push each row
          })
          .on('end', function () {
            fs.unlinkSync(req.file.path); // remove temp file
          });

        await db[req.table].batchInsert(fileRows);
      });

      return true;
    } catch (error) {
      console.log(error);
    }
  },
  /**
   * Export CSV file
   * @param {request} req
   * @param {response} res
   */
  csv_export: async function (req, res) {
    try {
      let fields = await db[req.table].getAll(req.where);
      fields = JSON.stringify(fields); // do this needed?

      const csv = await converter.json2csvAsync(fields);

      res.header('Content-Type', 'text/csv');
      res.attachment(req.table + '.csv');

      return res.send(csv);
    } catch (error) {
      console.log(error);
    }
  },
};
