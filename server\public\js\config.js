// API Configuration
// Switch between local development and live production backend

const CONFIG = {
  // Set to 'local' for localhost development or 'live' for production backend
  MODE: 'live', // Change this to 'local' when you want to use localhost

  // API Base URLs
  API_BASE_URLS: {
    local: 'http://localhost:3017',
    live: 'https://app.coparenthub.com',
  },

  // Stripe configuration
  stripe_public_key: 'pk_test_51K9FHpAQLMONiHkQVG9QqwhMGKiwmB5Uvyggo9KoCcBcQckynYRJa5vuA9icWlya0jYFRNl6Fmeagkb82CQkuOQs00wgY1hG0k',

  // Get the current API base URL based on mode
  getApiBaseUrl() {
    return this.API_BASE_URLS[this.MODE];
  },

  // Helper function to build full API URLs
  buildApiUrl(endpoint) {
    // Remove leading slash if present to avoid double slashes
    const cleanEndpoint = endpoint.startsWith('/') ? endpoint.substring(1) : endpoint;
    return `${this.getApiBaseUrl()}/${cleanEndpoint}`;
  },
};

// Helper function for making API calls with the correct base URL
async function apiCall(endpoint, options = {}) {
  const url = CONFIG.buildApiUrl(endpoint);

  // Default headers
  const defaultHeaders = {
    Accept: 'application/json',
    'Content-Type': 'application/json',
  };

  // Merge with provided options
  const finalOptions = {
    ...options,
    headers: {
      ...defaultHeaders,
      ...(options.headers || {}),
    },
  };

  try {
    const response = await fetch(url, finalOptions);
    return response;
  } catch (error) {
    console.error('API call failed:', error);
    throw error;
  }
}

// Export for use in other files
window.CONFIG = CONFIG;
window.apiCall = apiCall;

// For Node.js compatibility (if needed)
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    stripe_public_key: CONFIG.stripe_public_key,
  };
}
