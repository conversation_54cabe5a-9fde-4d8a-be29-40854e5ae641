/*Powered By: Manaknightdigital Inc. https://manaknightdigital.com/ Year: 2020*/
/**
 * Builder
 * @copyright 2020 Manaknightdigital Inc.
 * @link https://manaknightdigital.com
 * @license Proprietary Software licensing
 * <AUTHOR>
 *
 */

const { constants } = require('crypto');
const fs = require('fs');
const path = require('path');

module.exports = function () {
  this._translations = {};
  /*
   * Performs a deep merge of `source` into `target`.
   * Mutates `target` only but not its objects and arrays.
   *
   * <AUTHOR> by [jhildenbiddle](https://stackoverflow.com/a/48218209).
   */
  this.mergeDeep = function (target, source) {
    const isObject = (obj) => obj && typeof obj === 'object';

    if (!isObject(target) || !isObject(source)) {
      return source;
    }

    Object.keys(source).forEach((key) => {
      const targetValue = target[key];
      const sourceValue = source[key];

      if (Array.isArray(targetValue) && Array.isArray(sourceValue)) {
        target[key] = targetValue.concat(sourceValue);
      } else if (isObject(targetValue) && isObject(sourceValue)) {
        target[key] = this.mergeDeep(Object.assign({}, targetValue), sourceValue);
      } else {
        target[key] = sourceValue;
      }
    });

    return target;
  };

  this.set_translate_text = function (translation) {
    this._translations = translation;
  };

  this.inject_substitute = function (text, normalKey, value) {
    text = text.replace(new RegExp('{{{' + normalKey + '}}}', 'g'), value);
    for (let key in this._translations) {
      text = text.replace(new RegExp(key, 'g'), this._translations[key]);
    }
    return text;
  };

  this.convertToString = function (input) {
    if (input) {
      if (typeof input === 'string') {
        return input;
      }

      return String(input);
    }
    return '';
  };

  // convert string to words
  this.toWords = function (input) {
    input = this.convertToString(input);

    var regex = /[A-Z\xC0-\xD6\xD8-\xDE]?[a-z\xDF-\xF6\xF8-\xFF]+|[A-Z\xC0-\xD6\xD8-\xDE]+(?![a-z\xDF-\xF6\xF8-\xFF])|\d+/g;

    return input.match(regex);
  };

  // convert the input array to camel case
  this.toCamelCase = function (inputArray) {
    let result = '';

    for (let i = 0, len = inputArray.length; i < len; i++) {
      let currentStr = inputArray[i];

      let tempStr = currentStr.toLowerCase();

      if (i != 0) {
        // convert first letter to upper case (the word is in lowercase)
        tempStr = tempStr.substr(0, 1).toUpperCase() + tempStr.substr(1);
      }

      result += tempStr;
    }

    return result;
  };

  this.toCamelCaseString = function (input) {
    let words = this.toWords(input);

    return this.toCamelCase(words);
  };

  this.ucFirst = function (string) {
    return string.charAt(0).toUpperCase() + string.slice(1);
  };
  this.capitalize = function (words) {
    var separateWord = words.toLowerCase().split(' ');
    for (var i = 0; i < separateWord.length; i++) {
      separateWord[i] = separateWord[i].charAt(0).toUpperCase() + separateWord[i].substring(1);
    }
    return separateWord.join(' ');
  };
  // create dir if doesn't exist while writing file
  this.writeFileSyncRecursive = function writeFileSyncRecursive(filename, content, charset) {
    this.createDirectoriesRecursive(filename);
    fs.writeFileSync(path.join(__dirname, filename), content, { mode: 0775 });
  };
  this.writeFileSyncRecursiveV2 = function writeFileSyncRecursive(filename, content, charset) {
    this.createDirectoriesRecursiveV2(filename);
    fs.writeFileSync(filename, content, { mode: 0775 });
  };
  this.createDirectoriesRecursive = function (filePath) {
    let fileDirectoryPath = path.dirname(path.join(__dirname, filePath));
    if (!fs.existsSync(fileDirectoryPath)) {
      fs.mkdirSync(fileDirectoryPath, { recursive: true });
    }
  };
  this.createDirectoriesRecursiveV2 = function (filePath) {
    let fileDirectoryPath = path.dirname(filePath);
    if (!fs.existsSync(fileDirectoryPath)) {
      fs.mkdirSync(fileDirectoryPath, { recursive: true });
    }
  };
  // Copy files inside folder synchronously
  this.copyFolderRecursiveSync = function (source, target) {
    var files = [];

    // var targetFolder = path.join(target, path.basename(source));
    var targetFolder = target;
    if (!fs.existsSync(targetFolder)) {
      fs.mkdirSync(targetFolder, { recursive: true });
    }

    if (fs.lstatSync(source).isDirectory()) {
      files = fs.readdirSync(source);
      files.forEach(function (file) {
        var curSource = path.join(source, file);
        if (fs.lstatSync(curSource).isDirectory()) {
          copyFolderRecursiveSync(curSource, targetFolder);
        } else {
          fs.copyFileSync(curSource, path.join(targetFolder, file));
        }
      });
    }
  };

  // Files and folder name inside dir
  this.directoryContents = function (_path) {
    try {
      if (!fs.existsSync(_path)) {
        throw new Error('No such path.');
      }
      const allFilesAndFolders = fs.readdirSync(_path);
      const format = allFilesAndFolders.map((item) => {
        const isFile = fs.lstatSync(path.join(_path, item)).isFile();
        return {
          name: item,
          isFile,
        };
      });
      return format;
    } catch (error) {
      console.log('Index File Generation Error', error);
    }
    return [];
  };
  this.moveElement = function (array, initialIndex, finalIndex) {
    return array.splice(finalIndex, 0, array.splice(initialIndex, 1)[0]);
  };
  // Generate index.js for all the files inside the dir
  this.generateIndexFile = function (_path, importFilesInsideImport = false) {
    try {
      if (!fs.existsSync(_path)) {
        throw new Error('No such path.');
      }
      const allFilesAndFolders = fs.readdirSync(_path);
      const _allFilesAndFolders = new Set(allFilesAndFolders);

      // remove index.js
      const index = allFilesAndFolders.indexOf('index.js');
      if (index !== -1) {
        allFilesAndFolders.splice(index, 1);
      }

      if ('index.js' in _allFilesAndFolders) {
        console.log('Index.js already exists');
        return;
      }

      let content = '';
      if (importFilesInsideImport) {
        const allFilesImports = [...allFilesAndFolders].map((item) => {
          const name = item.replace(/.js$/g, '');
          return `const ${this.ucFirst(this.toCamelCaseString(name))} = require("./${name}");`;
        });
        content = allFilesImports.join('\n');
      }
      fs.writeFileSync(path.join(_path, 'index.js'), content, { mode: 0775 });
      // It will be all files if validation is done first

      return { content, allFiles: allFilesAndFolders };
    } catch (error) {
      console.log('Index File Generation Error', error);
      return { content: '', allFiles: [] };
    }
  };

  // stripe pipes and return as an array
  this.stripPipe = function (string) {
    return string.split('|');
  };

  this.typeToLabel = function (type) {
    if (type.includes('_')) {
      return this.ucFirst(type.split('_').join(' '));
    } else {
      return this.ucFirst(type);
    }
  };

  // profile_admin => ProfileAdmin
  this.variableUnderscore = function (term) {
    return term
      .split('_')
      .map((e) => this.ucFirst(e))
      .join('');
  };
};
