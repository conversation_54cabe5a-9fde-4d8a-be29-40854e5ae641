<!DOCTYPE html>
<html>

<head>
    <!-- Required meta tags-->
    <meta charset="utf-8">
    <meta name="viewport"
        content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no, viewport-fit=cover">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <!-- Color theme for statusbar (Android only) -->
    <meta name="theme-color" content="#2196f4">
    <!-- Your app title -->
    <title>Coparent</title>
    <!-- Path to your custom app styles-->
    <!-- <link rel="stylesheet" href="path/to/my-app.css"> -->
</head>

<body>

    <!-- App root element -->
    <div id="app">
        <div class="page" data-name="viewcohart">
            <!-- Top Navbar -->
            <div class="navbar">
                <div class="navbar-bg"></div>
                <div class="navbar-inner">
                    <div class="left">

                    </div>
                    <div class="title">
                        Cohart
                    </div>
                    <div class="right">
                        <a href="/editcohort/">
                            Edit Cohort
                        </a>
                    </div>
                </div>
            </div>
            <!-- Scrollable page content -->
            <div class="page-content">
                <div class="view-cohart-container" id="view-cohart-container">
                    <!--This is a skeleton, It is not being rendered. Rendering is done through Javascript!-->
                    <!-- <div class="view-cohart-item">
                        <p class="view-cohart-item-heading">Cohart</p>
                        <p class="view-cohart-item-phone">Phone: +16477849172</p>
                        <p class="view-cohart-item-email">Email: <EMAIL></p>
                        <p class="view-cohart-item-deliverheading">Deliver To</p>
                        <div class="row no-gap view-cohart-item-delieverto">
                            <div class="col-50 row no-gap view-cohart-item-delieverto-first">
                                <div class="col-20 view-cohart-item-delieverto-first-icon">
                                    <svg xmlns:dc="http://purl.org/dc/elements/1.1/"
                                        xmlns:cc="http://creativecommons.org/ns#"
                                        xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
                                        xmlns:svg="http://www.w3.org/2000/svg" xmlns="http://www.w3.org/2000/svg"
                                        xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
                                        xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape" width="40"
                                        height="40" viewBox="0 0 248.03149 248.03149" id="svg4526" version="1.1"
                                        inkscape:version="0.91 r13725" sodipodi:docname="user.svg">
                                        <defs id="defs4528" />
                                        <sodipodi:namedview id="base" pagecolor="#ffffff" bordercolor="#666666"
                                            borderopacity="1.0" inkscape:pageopacity="0.0" inkscape:pageshadow="2"
                                            inkscape:zoom="0.35" inkscape:cx="-105" inkscape:cy="520"
                                            inkscape:document-units="px" inkscape:current-layer="layer1"
                                            showgrid="false" inkscape:window-width="1536" inkscape:window-height="801"
                                            inkscape:window-x="-8" inkscape:window-y="-8"
                                            inkscape:window-maximized="1" />
                                        <metadata id="metadata4531">
                                            <rdf:RDF>
                                                <cc:Work rdf:about="">
                                                    <dc:format>image/svg+xml</dc:format>
                                                    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage" />
                                                    <dc:title></dc:title>
                                                </cc:Work>
                                            </rdf:RDF>
                                        </metadata>
                                        <g inkscape:label="Laag 1" inkscape:groupmode="layer" id="layer1"
                                            transform="translate(0,-804.33071)">
                                            <path
                                                style="opacity:1;fill:#030904;fill-opacity:1;fill-rule:nonzero;stroke:#000000;stroke-width:0.50507629;stroke-opacity:1"
                                                d="m 76.42344,1042.717 c -2.2224,-0.2267 -4.7225,-0.6821 -5.5559,-1.012 -0.8334,-0.3299 -9.3384,-2.8569 -18.9001,-5.6157 -9.5617,-2.7588 -17.4523,-5.0834 -17.5347,-5.1657 -0.2391,-0.2392 2.2788,-42.51159 2.8639,-48.08136 2.2676,-21.58406 6.0483,-32.16101 14.8649,-41.58634 7.5909,-8.11487 14.9224,-11.77177 27.5448,-13.73911 5.5107,-0.85889 39.4433,-1.23111 47.6206,-0.52237 13.0001,1.12673 21.3056,3.10399 29.1231,6.93322 3.9457,1.93271 5.526,3.0883 9.1422,6.68524 9.4197,9.36932 13.7657,22.358 15.8544,47.38281 1.1656,13.96551 2.4884,42.59711 1.9903,43.07931 -0.9176,0.8884 -17.7367,5.5971 -23.3654,6.5414 -20.4406,3.4292 -70.2575,6.467 -83.6481,5.1006 z"
                                                id="path4293-4" inkscape:connector-curvature="0" />
                                            <ellipse
                                                style="opacity:1;fill:#000700;fill-opacity:1;fill-rule:nonzero;stroke:#000000;stroke-opacity:1"
                                                id="path4295-9" cx="106.96198" cy="883.10352" rx="40.658642"
                                                ry="38.327599" />
                                        </g>
                                    </svg>
                                </div>
                                <div class="col-80 view-cohart-item-delieverto-first-info">
                                    <p>+16477849172</p>
                                    <p>Email: <EMAIL></p>
                                </div>
                            </div>
                            <div class="col-50 row no-gap view-cohart-item-delieverto-second">
                                <div class="col-20 view-cohart-item-delieverto-first-icon">
                                    <svg xmlns:dc="http://purl.org/dc/elements/1.1/"
                                        xmlns:cc="http://creativecommons.org/ns#"
                                        xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
                                        xmlns:svg="http://www.w3.org/2000/svg" xmlns="http://www.w3.org/2000/svg"
                                        xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
                                        xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape" width="40"
                                        height="40" viewBox="0 0 248.03149 248.03149" id="svg4526" version="1.1"
                                        inkscape:version="0.91 r13725" sodipodi:docname="user.svg">
                                        <defs id="defs4528" />
                                        <sodipodi:namedview id="base" pagecolor="#ffffff" bordercolor="#666666"
                                            borderopacity="1.0" inkscape:pageopacity="0.0" inkscape:pageshadow="2"
                                            inkscape:zoom="0.35" inkscape:cx="-105" inkscape:cy="520"
                                            inkscape:document-units="px" inkscape:current-layer="layer1"
                                            showgrid="false" inkscape:window-width="1536" inkscape:window-height="801"
                                            inkscape:window-x="-8" inkscape:window-y="-8"
                                            inkscape:window-maximized="1" />
                                        <metadata id="metadata4531">
                                            <rdf:RDF>
                                                <cc:Work rdf:about="">
                                                    <dc:format>image/svg+xml</dc:format>
                                                    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage" />
                                                    <dc:title></dc:title>
                                                </cc:Work>
                                            </rdf:RDF>
                                        </metadata>
                                        <g inkscape:label="Laag 1" inkscape:groupmode="layer" id="layer1"
                                            transform="translate(0,-804.33071)">
                                            <path
                                                style="opacity:1;fill:#030904;fill-opacity:1;fill-rule:nonzero;stroke:#000000;stroke-width:0.50507629;stroke-opacity:1"
                                                d="m 76.42344,1042.717 c -2.2224,-0.2267 -4.7225,-0.6821 -5.5559,-1.012 -0.8334,-0.3299 -9.3384,-2.8569 -18.9001,-5.6157 -9.5617,-2.7588 -17.4523,-5.0834 -17.5347,-5.1657 -0.2391,-0.2392 2.2788,-42.51159 2.8639,-48.08136 2.2676,-21.58406 6.0483,-32.16101 14.8649,-41.58634 7.5909,-8.11487 14.9224,-11.77177 27.5448,-13.73911 5.5107,-0.85889 39.4433,-1.23111 47.6206,-0.52237 13.0001,1.12673 21.3056,3.10399 29.1231,6.93322 3.9457,1.93271 5.526,3.0883 9.1422,6.68524 9.4197,9.36932 13.7657,22.358 15.8544,47.38281 1.1656,13.96551 2.4884,42.59711 1.9903,43.07931 -0.9176,0.8884 -17.7367,5.5971 -23.3654,6.5414 -20.4406,3.4292 -70.2575,6.467 -83.6481,5.1006 z"
                                                id="path4293-4" inkscape:connector-curvature="0" />
                                            <ellipse
                                                style="opacity:1;fill:#000700;fill-opacity:1;fill-rule:nonzero;stroke:#000000;stroke-opacity:1"
                                                id="path4295-9" cx="106.96198" cy="883.10352" rx="40.658642"
                                                ry="38.327599" />
                                        </g>
                                    </svg>
                                </div>
                                <div class="col-80 view-cohart-item-delieverto-first-info">
                                    <p>+16477849172</p>
                                    <p>Email: <EMAIL></p>
                                </div>
                            </div>
                        </div>
                        <button class="viewcohart-manage-button">Manage</button>
                        <button class="viewcohart-invite-button">Invite Parent</button>
                    </div> -->
                </div>
            </div>
        </div>
    </div>

    $$(document).on('page:init', function (e) {
    console.log("I ran");
    })

</body>

</html>