const app = require('express').Router();
const { Op } = require('sequelize');
const db = require('../../models');
const { protect } = require('../../middlewares/auth_middleware.js');
const UploadService = require('../../services/UploadService');
const upload = UploadService.upload('files/file');

app.get('/api/user/files/:cohort_id', protect, async function (req, res, next) {
  try {
    const { user } = req;
    const { cohort_id } = req.params;
    const belongToCohort = await db.cohort.findOne({
      where: {
        [Op.or]: {
          parent_1: user.id,
          parent_2: user.id,
        },
        id: cohort_id,
      },
    });
    if (!belongToCohort) {
      return res.status(404).json({
        status: 'fail',
        message: 'Cohort not found',
      });
    }
    const files = await db.file.findAll({
      where: { cohort_id },
      include: [
        {
          model: db.cohort,
          required: false,
          as: 'cohort',
        },
        {
          model: db.user,
          as: 'user',
        },
      ],
    });
    return res.status(200).json({
      status: 'success',
      files,
    });
  } catch (error) {
    console.log(error);
    return res.status(400).json({
      error: true,
      message: error.message,
    });
  }
});

app.post('/api/user/files/:cohort', protect, upload.single('file'), async function (req, res, next) {
  try {
    console.log('****************');
    console.log(req.body);
    const { cohort } = req.params;
    const { user } = req;
    const belongToCohort = await db.cohort.findOne({
      where: {
        [Op.or]: {
          parent_1: user.id,
          parent_2: user.id,
        },
        id: cohort,
      },
    });
    if (!belongToCohort) {
      return res.status(404).json({
        status: 'fail',
        message: 'Cohort not found',
      });
    }
    if (!req.file) {
      return res.status(400).json({
        status: 'fail',
        message: 'Please upload a file',
      });
    }

    const file_url = req.file.filename;

    const file = await db.file.insert({ upload_by: user.id, notes: req.body.notes, label: req.body.label, cohort_id: cohort, file_url: `/uploads/${file_url}` });

    return res.status(200).json({
      status: 'success',
      file,
    });
  } catch (error) {
    console.log(error);
    return res.status(400).json({
      error: true,
      message: error.message,
    });
  }
});

app.delete('/api/user/files/:file', protect, async function (req, res, next) {
  try {
    const { file } = req.params;
    const { user } = req;

    const fileExist = await db.file.findOne({ where: { upload_by: user.id, id: file } });

    if (!fileExist) {
      return res.status(400).json({
        status: 'fail',
        message: 'File not found',
      });
    }

    await fileExist.destroy();
    return res.status(204).json({
      status: 'success',
      message: 'file removed with success',
    });
  } catch (error) {
    console.log(error);
    return res.status(400).json({
      error: true,
      message: error.message,
    });
  }
});

app.patch('/api/user/files/:file', protect, upload.single('file'), async function (req, res, next) {
  try {
    const { file } = req.params;
    const { user } = req;

    const fileExist = await db.file.findOne({ where: { upload_by: user.id, id: file } });

    if (!fileExist) {
      return res.status(400).json({
        status: 'fail',
        message: 'File not found',
      });
    }

    if (!req.file) {
      return res.status(400).json({
        status: 'fail',
        message: 'Please upload a file',
      });
    }

    const file_url = req.file.filename;

    const updatedFile = await db.file.edit({ file_url, label: req.body.label }, file);
    return res.status(200).json({
      status: 'success',
      message: updatedFile,
    });
  } catch (error) {
    console.log(error);
    return res.status(400).json({
      error: true,
      message: error.message,
    });
  }
});

module.exports = app;
