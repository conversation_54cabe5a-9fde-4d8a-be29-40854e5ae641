function setDefaultEmail() {
  var childname = store.getters.childName.value.pop();
  console.log(childname);
  childname = childname.replace(/\s+/g, '');
  childnamealias = childname.toLowerCase();
  document.getElementById('shared-email').value = childnamealias;
}

async function choseEmail() {
  var mainview = app.view.main;
  app.preloader.show();

  let selectedEmailAlias = document.getElementById('shared-email').value;
  let cohortID = this.store.getters.cohortID.value;
  try {
    if (sharedEmailValidateEmail(selectedEmailAlias)) {
      let body = {
        name: selectedEmail<PERSON>lia<PERSON>,
      };
      let response = await fetch(`/api/user/set_shared_email/${cohortID}`, {
        method: 'POST',
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
      });

      if (response.status == 200) {
        app.preloader.hide();
        // app.dialog.alert('Co-Email has been set');
        mainview.router.navigate({ name: 'viewcohort' });
      } else {
        app.preloader.hide();
        throw 'This Co-Email is taken, Please try another one.';
      }
    } else {
      app.preloader.hide();
      throw 'Something went wrong, Please try again';
    }
  } catch (error) {
    app.preloader.hide();
    app.dialog.alert(error)
  }
}

function sharedEmailValidateEmail(email) {
  var AllFieldsValidated = true;
  var errortoThrow = '';
  if (email == '') {
    errortoThrow = 'Co-Email alias is required';
    AllFieldsValidated = false;
    throw errortoThrow;
  }
  if (email.includes('@') || email.includes('.')) {
    errortoThrow = "Your alias can't contain special characters";
    AllFieldsValidated = false;
    throw errortoThrow;
  }
  return AllFieldsValidated;
}
