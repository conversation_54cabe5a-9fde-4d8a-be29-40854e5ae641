<% if(it.layout_clean_mode) {%> <% layout("../layouts/admin/Clean") %> <% } else {%> <% layout("../layouts/admin/Main") %> <%}%> <%~ includeFile("../partials/admin/Breadcrumb",
it)%>

<div class="tab-content mx-4 my-4">
  <div class="row">
    <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
      <%~ includeFile("../partials/admin/GlobalResponse.eta", it) %>
      <div class="card" style="border-bottom: 1px solid #ccc">
        <div class="card-body">
          <h4 class="primaryHeading2 text-md-left"><%= it.heading %></h4>
          <table class="table table-striped">
            <tr>
              <th>ID:</th>
              <td><%= it.detail_fields["id"] %></td>
            </tr>

            <tr>
              <th>Co-Hort:</th>
              <td><%= it.detail_fields["cohort_id"] %></td>
            </tr>

            <tr>
              <th>Co-Expense Name:</th>
              <td><%= it.detail_fields["expense_name"] %></td>
            </tr>

            <tr>
              <th>Date:</th>
              <td><%= it.detail_fields["date"] %></td>
            </tr>

            <tr>
              <th>Amount:</th>
              <td><%= it.detail_fields["amount"] %></td>
            </tr>

            <tr>
              <th>Paid By:</th>
              <td><%= it.detail_fields["paid_by"] %></td>
            </tr>

            <tr>
              <th>Parent 1 split:</th>
              <td><%= it.detail_fields["parent_1_split"] %></td>
            </tr>

            <tr>
              <th>Parent 2 split:</th>
              <td><%= it.detail_fields["parent_2_split"] %></td>
            </tr>

            <tr>
              <th>Status:</th>
              <td><%= it.detail_fields["status"] %></td>
            </tr>

            <tr>
              <th>Receipt:</th>
              <td>
                <a href="/uploads/<%= it.detail_fields['receipt'] %>" target="_blanck"><%= it.detail_fields["receipt"] %></a>
              </td>
            </tr>

            <tr>
              <th>Settlement:</th>
              <td><%= it.detail_fields["settlement"] %></td>
            </tr>

            <tr>
              <th>Children:</th>
              <td><%= it.detail_fields["children"] %></td>
            </tr>

            <tr>
              <th>Notes:</th>
              <td><%= it.detail_fields["notes"] %></td>
            </tr>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>
