'use strict';

const app = require('express').Router();
const DB = require('../../models');
const helpers = require('../../core/helpers');
const { protect, handleRemoveCohort } = require('../../middlewares/auth_middleware');
const { Op } = require('sequelize');
const sequelize = require('sequelize');
const StripeService = require('../../services/StripeApi');

const verifySubs = async (req, res, next) => {
  const { user } = req;
  const { quantity, plan } = req.user.subscription;
  const cohorts = await DB.cohort.findAndCountAll({
    where: {
      [Op.or]: {
        parent_1: user.id,
        parent_2: user.id,
      },
    },
  });
  if (cohorts.count >= quantity) {
    return res.status(400).json({
      status: 'error',
      message: 'Reached the maximun amount of cohorts',
    });
  }
  next();
};

app.post('/api/cohort/add', protect, async (req, res, next) => {
  try {
    const cohorts = await DB.cohort.findAll({
      where: {
        [DB.Sequelize.Op.or]: [{ parent_1: req.user.id }, { parent_2: req.user.id }],
      },
    });

    const cohort = await DB.cohort.insert({
      parent_1: req.user.id,
      parent_2: req.body.parent2,
      name: req.body.name,
      shared_email: req.body.shared_email,
      shared_phone: req.body.shared_phone,
      parent_1_default_split: req.body.parent_1_default_split,
      parent_2_default_split: req.body.parent_2_default_split,
      status: '0',
    });

    const { subscription: subObj } = req.user;
    const { itemId, quantity } = subObj;

    let addedQty = 1;
    console.log(cohorts);
    if (cohorts.length === 0) {
      addedQty = 0;
    }
    if (quantity == 0) {
      addedQty = 1;
    }

    const subscription = await StripeService.stripeType('subscription_update', {
      subscriptionId: req.user.subscription.id,
      params: {
        items: [
          {
            id: itemId,
            quantity: quantity + addedQty,
          },
        ],
      },
    });

    if (cohort) return res.status(200).json({ data: cohort });
    else throw new Error('cohort not added!');
  } catch (error) {
    console.log(error);
    return res.status(400).json({
      error: true,
      message: error.message,
    });
  }
});
app.patch('/api/cohort/edit/:cohort_id', protect, async (req, res, next) => {
  try {
    const { user } = req;
    const { cohort_id } = req.params;
    const belongToCohort = await DB.cohort.findOne({
      where: {
        [Op.or]: {
          parent_1: user.id,
          parent_2: user.id,
        },
        id: cohort_id,
      },
    });
    if (!belongToCohort) {
      return res.status(404).json({
        status: 'fail',
        message: 'Cohort not found',
      });
    }
    const editedCohort = await DB.cohort.edit(req.body, cohort_id);
    res.status(200).json({
      status: 'success',
      message: 'cohort updated',
    });
  } catch (error) {
    console.log(error);
    return res.status(400).json({
      error: true,
      message: error.message,
    });
  }
});

app.get('/api/cohort', protect, async function (req, res, next) {
  try {
    console.log(req.user.subscription);
    let parentId = req.user.id;
    const cohorts = await DB.cohort.findAll({
      where: {
        [DB.Sequelize.Op.or]: [{ parent_1: parentId }, { parent_2: parentId }],
      },
    });

    const forwardsObj = await DB.calls_list.findAll({
      where: {
        [Op.and]: [
          sequelize.where(sequelize.fn('month', sequelize.col('created_at')), new Date().getMonth() + 1),
          sequelize.where(sequelize.fn('year', sequelize.col('created_at')), new Date().getFullYear()),
        ],
      },
      attributes: ['cohort_id', 'created_at', [sequelize.fn('sum', sequelize.col('duration')), 'sumDuration']],
      group: ['cohort_id'],
      required: false,
    });

    const forwards = forwardsObj.map((frw) => {
      return { cohort_id: frw.dataValues.cohort_id, sum: frw.dataValues.sumDuration };
    });

    const extraObj = await DB.extra_minutes.findAll({
      where: {
        [Op.and]: [
          sequelize.where(sequelize.fn('month', sequelize.col('created_at')), new Date().getMonth() + 1),
          sequelize.where(sequelize.fn('year', sequelize.col('created_at')), new Date().getFullYear()),
        ],
      },
      attributes: ['cohort_id', 'created_at', [sequelize.fn('sum', sequelize.col('amount')), 'amount']],
      group: ['cohort_id'],
      required: false,
    });

    const extra_minutes = extraObj.map((ext) => {
      return { cohort_id: ext.dataValues.cohort_id, amount: ext.dataValues.amount };
    });

    console.log(extra_minutes);

    // const cohorts = await DB.cohort.findAll({
    //   where: {
    //     [DB.Sequelize.Op.or]: [{ parent_1: parentId }, { parent_2: parentId }],
    //   },
    //   include: [
    //     {
    //       model: DB.calls_list,
    //       as: 'forwards',
    //       where: { [Op.and]: [sequelize.where(sequelize.fn('month', sequelize.col('forwards.created_at')), new Date().getMonth() + 1)] },
    //       attributes: ['cohort_id', 'created_at', [sequelize.fn('sum', sequelize.col('duration')), 'sumDuration']],
    //       group: [sequelize.fn('month', sequelize.col('forwards.created_at'))],
    //       required: false,
    //     },
    //   ],
    // });

    // console.log(cohorts);

    let parentIds = [];
    parentIds.push(parentId);
    cohorts.forEach((element) => {
      parentIds.push(element.parent_1 == parentId ? element.parent_2 : element.parent_1);
    });

    const parents = await DB.user.findAll({
      where: {
        id: parentIds,
      },
      include: [
        {
          model: DB.credential,
          require: true,
          as: 'credential',
          attributes: ['email'],
        },
      ],
    });

    for await (let cohort of cohorts) {
      cohort.dataValues.parent_1_info = await parents.find((parent) => parent.id == cohort.dataValues.parent_1);
      cohort.dataValues.parent_2_info = await parents.find((parent) => parent.id == cohort.dataValues.parent_2);
    }

    // console.log('Cohorts', cohorts);
    const nCohorts = cohorts.map((chrt) => {
      const chrtObj = { ...chrt.dataValues };
      const hasSum = forwards.find((frwrd) => chrt.dataValues.id === frwrd.cohort_id);
      if (hasSum) {
        chrtObj.sumDuration = hasSum.sum;
      } else {
        chrtObj.sumDuration = 0;
      }
      const hasExtra = extra_minutes.find((ext) => chrt.dataValues.id === ext.cohort_id);
      if (hasExtra) {
        chrtObj.extra_minutes = hasExtra.amount;
      } else {
        chrtObj.extra_minutes = 0;
      }
      return chrtObj;
    });

    return res.status(200).json({ data: nCohorts });
  } catch (error) {
    console.log(error);
    return res.status(400).json({
      error: true,
      message: error.message,
    });
  }
});

app.get('/api/cohort/:cohort_id', protect, async (req, res, next) => {
  try {
    const { user } = req;
    const { cohort_id } = req.params;
    const belongToCohort = await DB.cohort.findOne({
      where: {
        [Op.or]: {
          parent_1: user.id,
          parent_2: user.id,
        },
        id: cohort_id,
      },
      include: [
        {
          model: DB.user,
          as: 'parent1',
        },
        {
          model: DB.user,
          as: 'parent2',
        },
      ],
    });
    if (!belongToCohort) {
      return res.status(404).json({
        status: 'fail',
        message: 'Cohort not found',
      });
    }
    res.status(200).json({
      status: 'success',
      cohort: belongToCohort,
    });
  } catch (error) {
    console.log(error);
    res.status(400).json({
      status: 'error',
      message: 'There was an error',
    });
  }
});

app.delete('/api/cohort/:cohort_id', protect, async (req, res, next) => {
  try {
    
    const { user } = req;
    const { cohort_id } = req.params;
    const { subscription: subObj } = req.user;
    const { itemId, id, quantity } = subObj;

    

    await DB.cohort.update({ parent_1: null }, { where: { parent_1: user.id, id: cohort_id } });

    await DB.cohort.update({ parent_2: null }, { where: { parent_2: user.id, id: cohort_id } });
    const subscription = await StripeService.stripeType('subscription_update', {
      subscriptionId: req.user.subscription.id,
      params: {
        items: [
          {
            id: itemId,
            quantity: quantity - 1,
          },
        ],
      },
    });

    await handleRemoveCohort(cohort_id, DB);

    res.status(200).json({
      status: 'success',
    });
  } catch (error) {
    console.log(error);
    res.status(400).json({
      status: 'error',
      message: 'There was an error',
    });
  }
});

module.exports = app;
