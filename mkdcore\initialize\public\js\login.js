async function loginAPI() {
  var mainview = app.view.main;
  let loginEmail = document.getElementById('login-email').value;
  let loginPassword = document.getElementById('login-password').value;
  try {
    if (loginValidatefields(loginEmail, loginPassword)) {
      let body = {
        email: loginEmail,
        password: loginPassword,
      };
      app.preloader.show();

      let response = await fetch('/api/user/login', {
        method: 'POST',
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
      });
      let resJSON = await response.json();

      if (response.status == 400) {
        app.preloader.hide();
        throw 'Incorrect login credentials';
      } else if (response.status == 200) {
        app.preloader.hide();
        mainview.router.navigate({ name: 'viewcohort' });
        localStorage.setItem('user', JSON.stringify(resJSON.user));
      }
    } else {
      throw 'Something went wrong, Please try again';
    }
  } catch (error) {
    app.preloader.hide();
    app.dialog.alert(error);
  }
}

function loginValidatefields(email, password) {
  var AllFieldsValidated = true;
  var errortoThrow = 'These field(s) are required: <br>';
  if (email == '') {
    errortoThrow += 'Email Address<br>';
    AllFieldsValidated = false;
  }
  if (password == '') {
    errortoThrow += 'Password<br>';
    AllFieldsValidated = false;
  }
  if (AllFieldsValidated == false) {
    throw errortoThrow;
  }
  return AllFieldsValidated;
}

async function logoutUser() {
  var mainview = app.view.main;
  app.preloader.show();

  let response = await fetch('/api/user/logout', {
    method: 'POST',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
    },
  });
  if (response.status == 200) {
    app.preloader.hide();
    mainview.router.navigate({ name: '/' });
  } else {
    app.preloader.hide();

    app.dialog.alert('Something went wrong, Please try again');
  }
}
