/*Powered By: Manaknightdigital Inc. https://manaknightdigital.com/ Year: 2020*/
/**
 * App builder
 * @copyright 2020 Manaknightdigital Inc.
 * @link https://manaknightdigital.com
 * @license Proprietary Software licensing
 * <AUTHOR>
 *
 */

const fs = require('fs');
const { merge, mergeWith, isArray, isObject, concat } = require('lodash');

const Builder = require('./Builder');
const ConfigBuilder = require('./ConfigBuilder');
const CopyBuilder = require('./CopyBuilder');
const ModelBuilder = require('./ModelBuilder_v2');
const RoleBuilder = require('./RoleBuilder');
const ControllerBuilder = require('./ControllerBuilder');
const SkeletonBuilder = require('./SkeletonBuilder');
const PortalBuilder = require('./PortalBuilder');
const GraphqlBuilder = require('./GraphqlBuilder');
const ImageBuilder = require('./ImageBuilder');
const OAuthBuilder = require('./OAuthBuilder');
const PackageBuilder = require('./PackageBuilder');
// const UserBuilder = require("./UserBuilder");
// const ReportBuilder = require("./ReportBuilder");
// const SettingBuilder = require("./SettingBuilder");
// const RouteBuilder = require("./RouteBuilder");
const CronjobBuilder = require('./CronjobBuilder');
// const PowerByBuilder = require("./PowerByBuilder");
const SettingsBuilder = require('./SettingsBuilder');

module.exports = function (config) {
  Builder.call(this);
  this._config = config;
  this._modular_packages = [];
  this._active_packages = [];
  this._routes = [];
  this._roles = [];
  this._translations = {};
  this._render_list = [];

  // Merge all the models, controller etc of packages if enabled
  this.construct_packages_menus = function (packages = []) {
    packages.forEach((package) => {
      const packageConfig = require(`../source/packages/${package.name}/configuration.js`)(package);
      const package_menu = packageConfig.menu;
      if (package_menu) {
        let menu_portals = Object.keys(package_menu);
        menu_portals.forEach((menu_portal) => {
          let portal_index = this._config.portals.findIndex((portal) => portal.name === menu_portal);
          let portal_menu = this._config.portals[portal_index].menu;
          this._config.portals[portal_index].menu = { ...portal_menu, ...package_menu[menu_portal] };
        });
      }
    });
  };

  this.init_modular_packages = function (packages = []) {
    packages.forEach((package) => {
      const packageConfig = require(`../source/packages/${package.name}/configuration.js`)(package);
      this._config.models.forEach((model, index) => {
        packageConfig.models.forEach((_model, _index) => {
          if (model.name == _model.name) {
            this._config.models[index].join = concat(this._config.models[index].join, packageConfig.models[_index].join);
            packageConfig.models.splice(_index, 1);
          }
        });
      });
      this._config = this.mergeDeep(this._config, packageConfig);
    });
  };
  this.rearrangeMenus = function (portals) {
    portals.forEach((portal) => {
      //find profile and logout index and put them last and last -1
      // let menuLen = Object.keys(portal.menu).length;
      // let logoutIndex = Object.keys(portal.menu).indexOf('xyzLogout');
      // let profileIndex = Object.keys(portal.menu).indexOf('xyzProfile');
      let logoutValue = portal.menu['xyzLogout'];
      let profileValue = portal.menu['xyzProfile'];
      delete portal.menu['xyzLogout'];
      delete portal.menu['xyzProfile'];
      portal.menu['xyzProfile'] = profileValue;
      portal.menu['xyzLogout'] = logoutValue;
    });
  };
  this.init = function () {
    const packageKeys = Object.keys(this._config.packages);

    for (let i = 0; i < packageKeys.length; i++) {
      const package = this._config.packages[packageKeys[i]];

      if (typeof package === 'object' ? package.active : package) {
        this._modular_packages.push({
          name: packageKeys[i],
          settings: package,
        });
      }
    }
    this._active_packages = Object.keys(this._config.packages).filter((packageKey) => {
      return this._config.packages[packageKey].active == true;
    });
    this.init_modular_packages(this._modular_packages);
    this.construct_packages_menus(this._modular_packages);
    this.rearrangeMenus(this._config.portals);

    this._routes = this._config['routes'];
    const translationKeys = Object.keys(this._config['translations']).sort(function (a, b) {
      return b.length - a.length;
    });
    for (let i = 0; i < translationKeys.length; i++) {
      const key = translationKeys[i];
      this._translations[key] = this._config['translations'][key];
    }

    let packageBuilder = new PackageBuilder(this._config);
    packageBuilder.set_packages(this._active_packages);
    this._render_list.push(packageBuilder);

    let configBuilder = new ConfigBuilder(this._config);
    configBuilder.set_translate_text(this._translations);
    configBuilder.set_config(this._config.config);
    configBuilder.set_database(this._config.database);
    this._render_list.push(configBuilder);

    let roleBuilder = new RoleBuilder(this._config);
    roleBuilder.set_translate_text(this._translations);
    roleBuilder.set_roles(this._config.roles);
    this._roles = this._config.roles;
    this._render_list.push(roleBuilder);

    let skeletonBuilder = new SkeletonBuilder(this._config);
    skeletonBuilder.set_translate_text(this._translations);
    skeletonBuilder.set_roles(this._config.roles);
    this._render_list.push(skeletonBuilder);

    let modelBuilder = new ModelBuilder(this._config);
    modelBuilder.set_translate_text(this._translations);
    modelBuilder.set_model(this._config.models);
    this._render_list.push(modelBuilder);

    let portalBuilder = new PortalBuilder(this._config);
    portalBuilder.set_translate_text(this._translations);
    portalBuilder.set_roles(this._config.roles);
    portalBuilder.set_portals(this._config.portals);
    portalBuilder.set_models(this._config.models);
    this._render_list.push(portalBuilder);

    let controllerBuilder = new ControllerBuilder(this._config);
    controllerBuilder.set_translate_text(this._translations);
    controllerBuilder.set_model(this._config.models);
    controllerBuilder.set_controllers(this._config.controllers);
    controllerBuilder.set_portals(this._config.portals);
    controllerBuilder.set_roles(this._config.roles);
    this._render_list.push(controllerBuilder);

    let imageBuilder = new ImageBuilder(this._config);
    this._render_list.push(imageBuilder);

    let graphqlBuilder = new GraphqlBuilder(this._config);
    this._render_list.push(graphqlBuilder);

    let cronjobBuilder = new CronjobBuilder(this._config);
    cronjobBuilder.set_cronjobs(this._config.cronjob);
    this._render_list.push(cronjobBuilder);

    let settingsBuilder = new SettingsBuilder(this._config);
    this._render_list.push(settingsBuilder);

    let copyBuilder = new CopyBuilder(this._config);
    copyBuilder.set_translate_text(this._translations);
    copyBuilder.set_copy(this._config.copy);
    this._render_list.push(copyBuilder);
  };

  this.build = function () {
    this.init();
    for (let i = 0; i < this._render_list.length; i++) {
      this._render_list[i].build();
    }
  };

  this.destroy = function () {
    this.init();
    for (let i = 0; i < this._render_list.length; i++) {
      this._render_list[i].destroy();
    }
  };
};
