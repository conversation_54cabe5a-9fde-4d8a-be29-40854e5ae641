<!DOCTYPE html>
<html>

<head>
    <!-- Required meta tags-->
    <meta charset="utf-8">
    <meta name="viewport"
        content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no, viewport-fit=cover">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <!-- Color theme for statusbar (Android only) -->
    <meta name="theme-color" content="#2196f3">
    <!-- Your app title -->
    <title>Co-Expenses</title>
</head>

<body>
    <!-- App root element -->
    <div id="app">
        <div class="page" data-name="editChild">

            <!-- Top Navbar -->
            <div class="navbar">
                <div class="navbar-bg"></div>
                <div class="navbar-inner">
                    <div class="left">
                        <a class="link back">
                            <i class="icon icon-back color-black"></i>
                            <span class="if-not-md">Back</span>
                        </a>
                    </div>
                    <div class="title text-align-center">
                        Edit Children
                    </div>
                    <div class="right">

                    </div>
                </div>
            </div>

            <!-- Scrollable page content -->
            <div class="page-content no-swipe-panel">
                <div class="list no-hairlines">
                    <ul>

                        <li class="item-content item-input item-input-outline margin-top">
                            <div class="item-inner">
                                <div class="item-title item-label">Child Name</div>
                                <div class="item-input-wrap">
                                    <input type="text" name="childname" id="editchildsecondary-childname"
                                        placeholder="Child Name" required validate />
                                    <span class="input-clear-button"></span>
                                </div>
                            </div>
                        </li>

                        <!-- <li class="item-content item-input item-input-outline">
                            <div class="item-inner">
                                <div class="item-title item-label">Co-Child Age</div>
                                <div class="item-input-wrap">
                                    <input type="number" name="childname" id="editchildsecondary-childage"
                                        placeholder="Age" required validate min="1" />
                                    <span class="input-clear-button"></span>
                                </div>
                            </div>
                        </li> -->

                        <li>
                            <a class="editchild-createbutton" onclick="updateChildInformation()">
                                <button class="button button-raised button-fill margin-vertical edit-child-edit-button">
                                    Edit Child Information
                                </button>
                            </a>
                        </li>

                    </ul>
                </div>
            </div>
        </div>
    </div>
</body>

</html>