const JwtService = require('../services/JwtService.js');
const db = require('../models');
const StripeService = require('../services/StripeApi');
const { releaseNumber } = require('../services/TelnyxService.js');
const { deleteAlias } = require('../services/ForwardEmailService.js');

exports.protect = async (req, res, next) => {
  try {
    const token = req.cookies.jwt;
    const userId = JwtService.verifyAccessToken(token);
    if (!userId) {
      return res.status(401).json({
        status: 'fail',
        message: 'Please Login',
      });
    }
    let userObj = await db.user.get_user_credential(userId, db);
    const customerSub = await StripeService.stripeType('subscription_list_all', { customer: userObj.stripe_uid, status: 'all', expand: ['data.default_payment_method'] });
    const subscription = customerSub.data.find((sub) => sub.status === 'active');
    userObj.dataValues.subscription = {
      id: subscription?.id,
      itemId: subscription?.items?.data[0]?.id,
      plan: subscription?.plan?.product,
      quantity: subscription?.quantity,
    };

    req.user = { ...userObj.dataValues };
    next();
  } catch (error) {
    console.log(error);
    res.status(500).json({
      error,
    });
  }
};

exports.handleRemoveCohort = async (id, db) => {
  try {
    const cohort = await db.cohort.findByPk(id);
    console.log(cohort.parent_1);
    if (!cohort.parent_1 && !cohort.parent_2) {
      if (cohort.shared_phone) {
        await releaseNumber(cohort.shared_phone);
      }
      if (cohort.shared_email) {
        await deleteAlias(cohort.shared_email.split('@')[0]);
      }
      await cohort.destroy();
    }
  } catch (error) {
    console.log(error);
  }
};
