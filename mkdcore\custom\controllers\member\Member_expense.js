'use strict';

const app = require('express').Router();
const db = require('../../models');
const helpers = require('../../core/helpers');
let JwtService = require('../../services/JwtService');
const ValidationService = require('../../services/ValidationService');
const { protect } = require('../../middlewares/auth_middleware.js');
const UploadService = require('../../services/UploadService');
const { Op } = require('sequelize');
const Sequelize = require('sequelize');
const upload = UploadService.upload('files/file');

const role = 2;

app.post('/api/expenses-add', protect, upload.single('receipt'), async function (req, res, next) {
  const { cohort_id, expense_name, date, amount, paid_by, parent_1_split, parent_2_split, settlement, children, notes } = req.body;
  try {
    const data = await db.expense.insert({
      cohort_id,
      expense_name,
      date,
      amount,
      paid_by,
      parent_1_split,
      parent_2_split,
      status: 0,
      receipt: req?.file?.filename,
      settlement,
      children,
      notes,
    });
    console.log(data);

    return res.status(201).json({ success: true, message: 'Expense created successfully' });
  } catch (error) {
    console.log(error);
    return res.status(500).json({ success: false, message: 'Something went wrong' });
  }
});

app.get('/api/expense/:cohort_id/:status', protect, async function (req, res, next) {
  try {
    const { user } = req;
    const { cohort_id, status } = req.params;
    const belongToCohort = await db.cohort.findOne({
      where: {
        [Op.or]: {
          parent_1: user.id,
          parent_2: user.id,
        },
        id: cohort_id,
      },
    });
    if (!belongToCohort) {
      return res.status(404).json({
        status: 'fail',
        message: 'Cohort not found',
      });
    }
    const expenses = await db.expense.findAll({ where: { cohort_id, status }, include: [{ model: db.user, as: 'paidBy' }] });
    let parent1_all_exp_total = 0;
    let parent2_all_exp_total = 0;
    const expensesObj = expenses.map((expense) => {
      const parent1_total = (expense.amount * expense.parent_1_split) / 100;
      parent1_all_exp_total += parent1_total;
      const parent2_total = (expense.amount * expense.parent_2_split) / 100;
      parent2_all_exp_total += parent2_total;
      return { expense, parent1: { id: belongToCohort.parent_1, amountToPay: parent1_total }, parent2: { id: belongToCohort.parent_2, amountToPay: parent2_total } };
    });
    let amountOwesP1 = 0;
    let amountOwesP2 = 0;

    expenses.forEach((exp) => {
      if (exp.paidBy.id === belongToCohort.parent_1) {
        amountOwesP2 += (exp.amount * exp.parent_2_split) / 100;
      } else if (exp.paidBy.id === belongToCohort.parent_2) {
        amountOwesP1 += (exp.amount * exp.parent_1_split) / 100;
      }
    });

    let amountToSettleP1 = 0;
    let amountToSettleP2 = 0;

    const settlements = await db.settle_amount.findAll({
      where: { cohort_id },
      attributes: ['parent_send', [Sequelize.fn('sum', Sequelize.col('amount')), 'total']],
      group: ['parent_send'],
    });

    let p1Settled = 0;
    let p2Settled = 0;

    settlements.forEach((setls) => {
      if (setls.parent_send == belongToCohort.parent_1) p1Settled = setls.dataValues.total;
      if (setls.parent_send == belongToCohort.parent_2) p2Settled = setls.dataValues.total;
    });

    amountOwesP1 -= p1Settled;
    amountOwesP2 -= p2Settled;

    if (amountOwesP1 >= amountOwesP2) {
      amountToSettleP1 = amountOwesP1 - amountOwesP2;
    } else if (amountOwesP1 < amountOwesP2) {
      amountToSettleP2 = amountOwesP2 - amountOwesP1;
    }

    console.log(p1Settled, p2Settled);

    res.status(200).json({
      status: 'success',
      expensesObj,
      settlements,
      parent1: { id: belongToCohort.parent_1, amountToPay: parent1_all_exp_total, amountOwesP1, amountToSettleP1 },
      parent2: { id: belongToCohort.parent_2, amountToPay: parent2_all_exp_total, amountOwesP2, amountToSettleP2 },
    });
  } catch (error) {
    console.log(error);
    res.status(400).json({
      status: 'error',
      message: 'There was an error',
    });
  }
});

app.get('/api/cohort/:cohort_id/expense/:expense_id', protect, async function (req, res, next) {
  try {
    const { user } = req;
    const { cohort_id, expense_id } = req.params;
    const belongToCohort = await db.cohort.findOne({
      where: {
        [Op.or]: {
          parent_1: user.id,
          parent_2: user.id,
        },
        id: cohort_id,
      },
    });
    if (!belongToCohort) {
      return res.status(404).json({
        status: 'fail',
        message: 'Cohort not found',
      });
    }
    const expense = await db.expense.findOne({ where: { cohort_id, id: expense_id }, include: [{ model: db.user, as: 'paidBy' }] });
    const parent1_total = (expense.amount * expense.parent_1_split) / 100;
    const parent2_total = (expense.amount * expense.parent_2_split) / 100;
    res.status(200).json({
      status: 'success',
      expense,
      parent1: {
        id: belongToCohort.parent_1,
        amountToPay: parent1_total,
      },
      parent2: {
        id: belongToCohort.parent_2,
        amountToPay: parent2_total,
      },
    });
  } catch (error) {
    console.log(error);
    res.status(400).json({
      status: 'error',
      message: 'There was an error',
    });
  }
});

app.put('/api/cohort/:cohort_id/expense/:expense_id', protect, async function (req, res, next) {
  try {
    const { user } = req;
    const { cohort_id, expense_id } = req.params;
    const belongToCohort = await db.cohort.findOne({
      where: {
        [Op.or]: {
          parent_1: user.id,
          parent_2: user.id,
        },
        id: cohort_id,
      },
    });
    if (!belongToCohort) {
      return res.status(404).json({
        status: 'fail',
        message: 'Cohort not found',
      });
    }

    const data = await db.expense.update(req.body, { where: { id: expense_id } });
    res.status(200).json({
      status: 'success',
    });
  } catch (error) {
    console.log(error);
    res.status(400).json({
      status: 'error',
      message: 'There was an error',
    });
  }
});

module.exports = app;
