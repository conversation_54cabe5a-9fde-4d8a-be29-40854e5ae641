var $$ = Dom7;
const stripe = Stripe('pk_live_51K9FHpAQLMONiHkQxRZecgdxhU2fAXfcorjVXrg53xjii6fv1mX1BWn0cuLMMtg5X1es1KLR3Dv4H7oa5v3wkGz500yNDTkMCW');

function formatPhoneNumber(phoneNumberString) {
  var cleaned = ('' + phoneNumberString).replace(/\D/g, '');
  var match = cleaned.match(/^(1|)?(\d{3})(\d{3})(\d{4})$/);
  if (match) {
    var intlCode = match[1] ? '+1 ' : '';
    return ['(', match[2], ') ', match[3], '-', match[4]].join('');
  }
  return 'N/A';
}

async function getCohortView() {
  try {
    app.preloader.show();

    let response = await fetch('/api/cohort', {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
    });
    let userRes = await fetch('/api/cohort', {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
    });
    app.preloader.hide();

    if (response.status == 200) {
      let res = await response.json();
      if (res.data.length == 0) {
        html = `
                <div class="viewcohort-infomessage">
                    <div class="block block-strong inset">
                        <p class="viewcohort-infomessage-text text-align-center">
                            No Active Co-Hort Found
                        </p>
                            <a href="/createcohort/" class="button button-raised button-fill">
                                Create new Co-Hort
                            </a>
                        
                            <a href="/join/"  style="margin-top:4px;" class="button button-raised button-fill ">
                                Join Co-Hort
                            </a>
                    </div>
                </div>
                `;
        $$('#view-cohart-container').append(html);
      } else {
        res.data.forEach((cohort) => {
          html = `
                    <div class="card">
                        <div class="card-content card-content-padding">
                            <div class="view-cohart-item">
                            <h6 class="view-cohart-item-heading margin-bottom"><b> Co-Hort Name: </b> ${cohort.name}</h6>
                            <p class="view-cohart-item-phone margin-bottom no-margin-top"><b>Co-Phone:</b> ${formatPhoneNumber(cohort?.shared_phone)}</p>
                            <p class="view-cohart-item-email margin-bottom no-margin-top"><b>Co-Email:</b> ${cohort.shared_email || 'N/A'}</p>
                            <h6 class="view-cohart-item-deliverheading margin-bottom no-margin-top"><b> Deliver To </b></h6>
                            <div class="row">
                              <div class="col-50 text-align-center">
                                  <i class="f7-icons color-black">
                                      person_crop_circle_fill
                                  </i>
                              </div>
                              <div class="col-50 text-align-center">
                                  <i class="f7-icons color-black">
                                      person_crop_circle_fill
                                  </i>
                              </div>
                            </div>
                            <div class="row margin-bottom">
                              <div class="col-50 text-align-left display-flex 
                                  flex-direction-row justify-content-flex-start 
                                  align-items-flex-start align-self-flex-start">
                                  <div class="col-20">
                                      <b>Name:&nbsp;</b>
                                  </div>
                                  <div class="col-80">
                                  ${cohort?.parent_1_info ? cohort.parent_1_info.first_name + ' ' + cohort?.parent_1_info?.last_name : 'N/A'}
                                  </div>
                              </div>
                              <div class="col-50 text-align-left display-flex 
                                  flex-direction-row justify-content-flex-start 
                                  align-items-flex-start align-self-flex-start">
                                  <div class="col-20">
                                      <b>Name:&nbsp;</b>
                                  </div>
                                  <div class="col-80">
                                    ${cohort?.parent_2_info ? cohort.parent_2_info.first_name + ' ' + cohort?.parent_2_info?.last_name : 'N/A'}
                                  </div>
                              </div>
                            </div>
                            <div class="row margin-bottom">
                              <div class="col-50 text-align-left display-flex 
                                  flex-direction-row justify-content-flex-start 
                                  align-items-flex-start align-self-flex-start">
                                  <div class="col-20">
                                      <b>Phone:&nbsp;</b>
                                  </div>
                                  <div class="col-80">
                                    ${formatPhoneNumber(cohort?.parent_1_info?.phone)}
                                  </div>
                              </div>
                              <div class="col-50 text-align-left display-flex 
                                  flex-direction-row justify-content-flex-start 
                                  align-items-flex-start align-self-flex-start">
                                  <div class="col-20">
                                      <b>Phone:&nbsp;</b>
                                  </div>
                                  <div class="col-80">
                                  ${formatPhoneNumber(cohort?.parent_2_info?.phone)}
                                  </div>
                              </div>
                            </div>
                            <div class="row margin-bottom">
                              <div
                                  class="col-50 text-align-left display-flex 
                                  flex-direction-row justify-content-flex-start view-cohort-cohort-email-container">
                                  <div class="col-20">
                                      <b>Email:&nbsp;</b>
                                  </div>
                                  <div class="col-80 view-cohort-cohort-email">
                                    ${cohort?.parent_1_info?.credential?.email || 'N/A'}
                                  </div>
                              </div>
                              <div
                                  class="col-50 text-align-left display-flex 
                                  flex-direction-row justify-content-flex-start view-cohort-cohort-email-container">
                                  <div class="col-20">
                                      <b>Email:&nbsp;</b>
                                  </div>
                                  <div class="col-80 view-cohort-cohort-email">
                                    ${cohort?.parent_2_info?.credential?.email || 'N/A'}
                                  </div>
                              </div>
                            </div>

                            <div class="row margin-bottom">
                              <div
                                  class="col-50 text-align-left display-flex 
                                  flex-direction-row justify-content-flex-start view-cohort-cohort-email-container">
                                  <div class="col-20">
                                    <b>Usage:&nbsp;</b>
                                  </div>
                                  <div class="col-80 view-cohort-cohort-email">
                                    ${cohort.sumDuration ? Math.ceil(cohort.sumDuration / 60) : 0} / ${200 + cohort.extra_minutes * 1} minutes
                                  </div>
                              </div>
                              <div class="col-50">
                                  <a href="/extra-minutes/?cohort=${cohort.id}"  class="col button button-raised button-fill">Buy minutes</a>
                              </div>
                            </div>

                            <div class="margin-bottom">                              
                              <div id="coh-prog-${cohort.id}" class="progressbar color-blue" data-progress="10"></div>
                              </div>
                                <button class="button button-raised button-fill viewcohart-manage-button" data-cohortid="${cohort.id}">
                                  <a href="" class="color-white">Manage</a>
                                </button>
                                ${
                                  !cohort.parent_2_info
                                    ? `<button class="button button-raised button-fill viewcohart-invite-button" data-cohortid="${cohort.id}"><a href="" class="color-white">Invite Co-Parent</a></button>`
                                    : ''
                                }
                              </div>
                            </div>
                          </div>
                `;
          $$('#view-cohart-container').append(html);
          app.progressbar.set(`#coh-prog-${cohort.id}`, (cohort?.sumDuration / 60 / (200 + cohort.extra_minutes * 1)) * 100, 1500);
        });

        $$('.viewcohart-manage-button').on('click', function (e) {
          editCohort(this.dataset.cohortid, res.data);
        });
        $$('.viewcohart-invite-button').on('click', function (e) {
          inviteParent(this.dataset.cohortid);
        });
      }
    } else {
      throw "Couldn't retrieve Co-Hort, Please try again";
    }coupon
  } catch (error) {
    app.dialog.alert(error);
  }
}

async function removeCohort() {
  const cohortId = this.store.getters.activeCohort.value;
  app.dialog.confirm('If you delete this Co-Hort, your Co-Phone number and Co-Email address will be deleted. This cannot be undone.<br/> Are you sure?', 'Warning', async () => {
    try {
      app.preloader.show();
      const res = await fetch(`/api/cohort/${cohortId}`, {
        method: 'DELETE',
      });
      app.preloader.hide();
      app.view.main.router.navigate({ name: 'viewcohort' });
    } catch (error) {
      app.preloader.hide();
    }
  });
}

function editCohort(coID, res) {
  let cohortInfo = res.find((o) => o.id == coID);
  localStorage.setItem('cohortInfo', JSON.stringify(cohortInfo));
  this.store.dispatch('setActiveCohort', coID);

  app.view.main.router.navigate({ name: 'cohortedit1' });
}

function inviteParent(coID) {
  this.store.dispatch('setActiveCohort', coID);
  app.view.main.router.navigate({ name: 'cohartedit2' });
}

async function updateCohort() {
  try {
    let cohortName = document.getElementById('edit-cohort-cohort-name').value;
    let parent1Split = document.getElementById('edit-cohort-parent-one-split').value;
    let parent2Split = document.getElementById('edit-cohort-parent-two-split').value;
    let cohortID = this.store.getters.activeCohort.value;

    if (validateUpdateCohort(cohortName, parent1Split, parent2Split)) {
      let body = {
        name: cohortName,
        parent_1_default_split: parent1Split,
        parent_2_default_split: parent2Split,
      };
      let response = await fetch(`/api/cohort/edit/${cohortID}`, {
        method: 'PATCH',
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
      });
      if (response.status == 200) {
        app.dialog.alert('Co-Hort information updated');
        app.view.main.router.navigate({ name: 'viewcohort' });
      } else {
        throw "Couldn't update Co-Hort, Please try again";
      }
    }
  } catch (error) {
    app.dialog.alert(error);
  }
}

function validateUpdateCohort(cohortName, parent1Split, parent2Split) {
  var AllFieldsValidated = true;
  var errortoThrow = 'These Feild(s) are Required: <br>';
  if (cohortName == '') {
    AllFieldsValidated = false;
    errortoThrow += 'Co-Hort Name <br>';
  }
  if (parent1Split == '' || parent2Split == '') {
    AllFieldsValidated = false;
    errortoThrow += `Co-Parent(s) Split <br>`;
  }
  // if (parent2Split == '') {
  //   AllFieldsValidated = false;
  //   errortoThrow += 'Co-Parent Two Split <br>';
  // }
  if (AllFieldsValidated == false) {
    throw errortoThrow;
  }
  if (parseInt(parent1Split) + parseInt(parent2Split) != 100) {
    errortoThrow = 'The split should be equal to 100%';
    throw errortoThrow;
  }
  return AllFieldsValidated;
}

async function sendInvite() {
  try {
    app.preloader.show();
    let name = document.getElementById('invite-name').value;
    let email = document.getElementById('invite-email').value;
    let phoneEntered = document.getElementById('invite-phone').value;
    let phoneIn = '+1' + document.getElementById('invite-phone').value;
    let phone = phoneIn.replace(/\s+/g, '');
    let cohortID = this.store.getters.activeCohort.value;
    if (document.querySelectorAll('.input-invalid').length == 0 && validateInviteFields(email, phoneEntered, name)) {
      let body = {
        name,
        email: email,
        phone: phone,
        cohort_id: cohortID,
      };
      let response = await fetch('/api/user/invite', {
        method: 'POST',
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
      });
      if (response.status == 200) {
        app.preloader.hide();

        app.dialog.alert('Invitation sent', 'Success');
        app.view.main.router.navigate({ name: 'viewcohort' });
      }
      if (response.status == 400) {
        app.preloader.hide();
        app.dialog.alert('Invalid number, Please enter a valid number');
      }
    } else {
      app.preloader.hide();
      throw 'Please enter the required field(s)';
    }
  } catch (error) {
    app.preloader.hide();
    app.dialog.alert(error);
  }
}

function validateInviteFields(email, phoneEntered, name) {
  var AllFieldsValidated = true;
  var errortoThrow = `These field(s) are required: <br>`;
  if (email == '') {
    errortoThrow += `Co-Parent's Email<br>`;
    AllFieldsValidated = false;
  }
  if (phoneEntered == '') {
    errortoThrow += `Co-Parent's Phone<br>`;
    AllFieldsValidated = false;
  }
  if (name == '') {
    errortoThrow += `Co-Parent's Name<br>`;
    AllFieldsValidated = false;
  }
  if (AllFieldsValidated == false) {
    throw errortoThrow;
  }
  return AllFieldsValidated;
}

async function extraMinutesCheckout(cohort_id, quantity) {
  let response = await fetch('/api/payment/session/extra-minutes', {
    method: 'POST',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ cohort_id, quantity }),
  });
  const resJSON = await response.json();
  const btnsContainer = $$('#extra-minutes-buttons')[0];
  const html = `
  <a href="${resJSON.session.url}" style="margin-bottom:5px" target="_blank" class="external button button-fill">
    Proceed to checkout
  </a>
`;
  btnsContainer.innerHTML = html;
}
function setButtonsForCheckout(page) {
  const btnsContainer = $$('#extra-minutes-buttons')[0];

  const html = `
    <button onClick="extraMinutesCheckout(${page?.route?.query?.cohort},1)" style="margin-bottom:5px" class="button button-fill">
      Get 50 minutes For $2.50
    </button>
    <button onClick="extraMinutesCheckout(${page?.route?.query?.cohort},4)"  class="button button-fill">
      Get 200 minutes For $5.00
    </button>
  `;
  btnsContainer.innerHTML = html;
}

async function setSubscriptionForm(page) {
  document.getElementById('sub-submit').setAttribute('disabled', true);
  document.getElementById('sub-submit').innerHTML = 'Please wait...';

  app.preloader.show();
  const res = await fetch('/api/user/profile');
  const userJSON = await res.json();
  let response = await fetch('/api/create-subscription', {
    method: 'POST',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ customer: userJSON.user.stripe_uid }),
  });
  const resJSON = await response.json();
  const options = {
    clientSecret: resJSON.clientSecret,
  };
  const elements = stripe.elements(options);
  const paymentElement = elements.create('card');
  paymentElement.mount('#payment-container');
  setTimeout(() => {
    document.getElementById('sub-submit').removeAttribute('disabled');
    document.getElementById('sub-submit').textContent = 'Subscribe $14.99/month';
    app.preloader.hide();
    document.getElementById('sub-submit').addEventListener('click', async (e) => {
      e.preventDefault();

      app.preloader.show();

      const res = await stripe.confirmCardPayment(resJSON.clientSecret, {
        payment_method: {
          card: paymentElement,
        },
      });
      app.preloader.hide();
      if (res.error) {
        app.dialog.alert(res.error.message);
      } else {
        app.view.main.router.navigate({ name: page?.route?.query?.redirect ? page?.route?.query?.redirect : 'viewcohort' });
      }
    });
  }, 4000);
}



function setButtonsForCheckout(page) {
  const btnsContainer = $$('#extra-minutes-buttons')[0];

  const html = `
    <button onClick="extraMinutesCheckout(${page?.route?.query?.cohort},1)" style="margin-bottom:5px" class="button button-fill">
      Get 50 minutes For $2.50
    </button>
    <button onClick="extraMinutesCheckout(${page?.route?.query?.cohort},4)"  class="button button-fill">
      Get 200 minutes For $5.00
    </button>
  `;
  btnsContainer.innerHTML = html;
}

async function redirectToSubscribe(name) {
  const res = await fetch('/api/user/profile');
  const resJSON = await res.json();
  if (!resJSON.user.subscription.id) {
    return app.view.main.router.navigate(`/subscription/?redirect=${name}`);
  }
}
async function preventSubscribe() {
  const res = await fetch('/api/user/profile');
  const resJSON = await res.json();
  if (resJSON.user.subscription.id) {
    return app.view.main.router.navigate({ name: 'viewcohort' });
  }
}
