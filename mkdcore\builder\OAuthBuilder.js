/*Powered By: Manaknightdigital Inc. https://manaknightdigital.com/ Year: 2020*/
/**
 * Config builder
 * @copyright 2020 Manaknightdigital Inc.
 * @link https://manaknightdigital.com
 * @license Proprietary Software licensing
 * <AUTHOR>
 *
 */

const fs = require('fs');
const Builder = require('./Builder');

module.exports = function ({ portals, graphql, roles }) {
  this._render_list = [];

  Builder.call(this);

  this.build = function () {
    // console.log('OAuth');
    portals.forEach((portal) => {
      const role = roles.find((item) => item.name === portal.role);

      if (portal['google-login'] || portal['google-register']) {
        let google_router_template = fs.readFileSync(
          '../mkdcore/source/routes/oAuth_router.js',
          'utf8',
        );

        const authentication_url_router = this.generate_authentication_url(
          portal,
          'google',
        );

        google_router_template = this.inject_substitute(
          google_router_template,
          'initialize',
          authentication_url_router,
        );

        if (portal['google-login']) {
          const authentication_login_router = this.generate_login(
            portal,
            'google',
          );

          google_router_template = this.inject_substitute(
            google_router_template,
            'login',
            authentication_login_router,
          );
        } else {
          google_router_template = this.inject_substitute(
            google_router_template,
            'login',
            '',
          );
        }

        if (portal['google-register']) {
          const authentication_register_router = this.generate_register(
            portal,
            'google',
          );

          google_router_template = this.inject_substitute(
            google_router_template,
            'register',
            authentication_register_router,
          );
        } else {
          google_router_template = this.inject_substitute(
            google_router_template,
            'register',
            '',
          );
        }

        if (portal.api) {
          let apiPortalGoogle = fs.readFileSync(
            '../mkdcore/source/portal/api_portal_google.js',
            'utf-8',
          );

          apiPortalGoogle = this.inject_substitute(
            apiPortalGoogle,
            'portal',
            portal.name,
          );
          apiPortalGoogle = this.inject_substitute(
            apiPortalGoogle,
            'role_id',
            role.id,
          );

          google_router_template = this.inject_substitute(
            google_router_template,
            'api',
            apiPortalGoogle,
          );
        }

        this._render_list[
          '../../release/routes/google.js'
        ] = google_router_template;
      }

      if (portal['facebook-login'] || portal['facebook-register']) {
        let google_router_template = fs.readFileSync(
          '../mkdcore/source/routes/oAuth_router.js',
          'utf8',
        );

        const authentication_url_router = this.generate_authentication_url(
          portal,
          'facebook',
        );

        google_router_template = this.inject_substitute(
          google_router_template,
          'initialize',
          authentication_url_router,
        );

        if (portal['facebook-login']) {
          const authentication_login_router = this.generate_login(
            portal,
            'facebook',
          );

          google_router_template = this.inject_substitute(
            google_router_template,
            'login',
            authentication_login_router,
          );
        } else {
          google_router_template = this.inject_substitute(
            google_router_template,
            'login',
            '',
          );
        }

        if (portal['facebook-register']) {
          const authentication_register_router = this.generate_register(
            portal,
            'facebook',
          );

          google_router_template = this.inject_substitute(
            google_router_template,
            'register',
            authentication_register_router,
          );
        } else {
          google_router_template = this.inject_substitute(
            google_router_template,
            'register',
            '',
          );
        }

        if (portal.api) {
          // let apiPortalGoogle = fs.readFileSync(
          //   '../mkdcore/source/portal/api_portal_facebook.js',
          //   'utf-8',
          // );

          // apiPortalGoogle = this.inject_substitute(
          //   apiPortalGoogle,
          //   'portal',
          //   portal.name,
          // );
          // apiPortalGoogle = this.inject_substitute(
          //   apiPortalGoogle,
          //   'role_id',
          //   role.id,
          // );

          google_router_template = this.inject_substitute(
            google_router_template,
            'api',
            '',
          );
        }

        this._render_list[
          '../../release/routes/facebook.js'
        ] = google_router_template;
      }
    });

    if (graphql['google-auth']) {
      let google_router_template = fs.readFileSync(
        '../mkdcore/source/routes/oAuth_router.js',
        'utf8',
      );

      const authentication_url_router = this.generate_authentication_url(
        null,
        'google',
      );

      google_router_template = this.inject_substitute(
        google_router_template,
        'initialize',
        authentication_url_router,
      );

      const authentication_login_router = this.generate_login(null, 'google');

      google_router_template = this.inject_substitute(
        google_router_template,
        'login',
        authentication_login_router,
      );

      const authentication_register_router = this.generate_register(
        null,
        'google',
      );

      google_router_template = this.inject_substitute(
        google_router_template,
        'register',
        authentication_register_router,
      );

      this._render_list[
        '../../releaseGraphql/routes/google.js'
      ] = google_router_template;
    }

    if (graphql['facebook-auth']) {
      let facebook_router_template = fs.readFileSync(
        '../mkdcore/source/routes/oAuth_router.js',
        'utf8',
      );

      const authentication_url_router = this.generate_authentication_url(
        null,
        'facebook',
      );

      facebook_router_template = this.inject_substitute(
        facebook_router_template,
        'initialize',
        authentication_url_router,
      );

      const authentication_login_router = this.generate_login(null, 'facebook');

      facebook_router_template = this.inject_substitute(
        facebook_router_template,
        'login',
        authentication_login_router,
      );

      const authentication_register_router = this.generate_register(
        null,
        'facebook',
      );

      facebook_router_template = this.inject_substitute(
        facebook_router_template,
        'register',
        authentication_register_router,
      );

      this._render_list[
        '../../releaseGraphql/routes/facebook.js'
      ] = facebook_router_template;
    }

    for (const key in this._render_list) {
      const page = this._render_list[key];
      try {
        this.writeFileSyncRecursive(key, page, { mode: 0775 });
      } catch (error) {
        console.log('oAuth Build Error', error);
      }
    }
  };

  this.generate_authentication_url = (portal, auth) => {
    const code = `initialize: async function (req, res) {
      try {
        const authenticationUrl = oAuthServer.${auth}.generateAuthURL({
          redirect_uri:  process.env.DYNAMIC_CONFIG_${auth.toUpperCase()}_REDIRECT_URI,
          client_id:   process.env.DYNAMIC_CONFIG_${auth.toUpperCase()}_CLIENT_ID,
        });
  
        res
          .status(200)
          .json({ success: true, payload: { url: authenticationUrl } });
      } catch (error) {
        res.status(500).json({ success: false, message: error.message });
      }
    },`;

    return code;
  };

  this.generate_login = (portal, auth) => {
    const dbOperation = (provider) => {
      if (provider === 'google') {
        return 'const payload = await oAuthServer.login(googleUser.id);';
      }

      if (provider === 'facebook') {
        return 'const payload = await oAuthServer.login(facebookUser.id);';
      }
    };

    const code = `
    login: async function (req, res) {
      try {
        const authToken = await oAuthServer.${auth}.generateAuthToken({
          redirect_uri:  process.env.DYNAMIC_CONFIG_${auth.toUpperCase()}_REDIRECT_URI,
          client_id:   process.env.DYNAMIC_CONFIG_${auth.toUpperCase()}_CLIENT_ID,
          client_secret:   process.env.DYNAMIC_CONFIG_${auth.toUpperCase()}_CLIENT_SECRET,
          auth_code: req.body.auth_code,
        });
  
        const ${auth}User = await oAuthServer.${auth}.getUserInfo(authToken);

        ${dbOperation(auth)}

        const response = {
          access_token: jwtService.createAccessToken(payload),
          refresh_token: jwtService.createRefreshToken(payload),
        };
  
        res
          .status(200)
          .json({ success: true, payload: response });
      } catch (error) {
        res.status(500).json({ success: false, message: error.message });
      }
    },`;

    return code;
  };

  this.generate_register = (portal, auth) => {
    const dbOperation = (provider) => {
      if (provider === 'google') {
        return `  const payload = await oAuthServer.register({
          provider: 'g',
          id: googleUser.id,
          email: googleUser.email,
          first_name: googleUser.given_name,
          last_name: googleUser.family_name,
          image: googleUser.picture,
        });
        `;
      }

      if (provider === 'facebook') {
        return `  const payload = await oAuthServer.register({
          provider: 'f',
          id: facebookUser.id,
          email: facebookUser.email,
          first_name: facebookUser.first_name,
          last_name: facebookUser.last_name,
          image: '',
        });
        `;
      }
    };

    const code = `
    register: async function (req, res) {
      try {
        const authToken = await oAuthServer.${auth}.generateAuthToken({
          redirect_uri:  process.env.DYNAMIC_CONFIG_${auth.toUpperCase()}_REDIRECT_URI,
          client_id:   process.env.DYNAMIC_CONFIG_${auth.toUpperCase()}_CLIENT_ID,
          client_secret:   process.env.DYNAMIC_CONFIG_${auth.toUpperCase()}_CLIENT_SECRET,
          auth_code: req.body.auth_code,
        });
  
        const ${auth}User = await oAuthServer.${auth}.getUserInfo(authToken);

        ${dbOperation(auth)}

        const response = {
          access_token: jwtService.createAccessToken(payload),
          refresh_token: jwtService.createRefreshToken(payload),
        };
  
        res
          .status(200)
          .json({ success: true, payload: response });
      } catch (error) {
        res.status(500).json({ success: false, message: error.message });
      }
    },`;

    return code;
  };

  this.destroy = function () {
    try {
      const fileName = '../../release/.env';
      if (fs.existsSync(fileName)) {
        fs.unlinkSync(fileName);
      }
    } catch (err) {
      console.error('Config Builder Destroy Error', err);
    }
  };
};
