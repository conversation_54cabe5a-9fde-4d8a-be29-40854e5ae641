<!DOCTYPE html>
<html>
  <head>
    <!-- Required meta tags-->
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no, viewport-fit=cover" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <!-- Color theme for statusbar (Android only) -->
    <meta name="theme-color" content="#2196f3" />
    <!-- Your app title -->
    <title>Co-Parent</title>
  </head>

  <body>
    <!-- App root element -->
    <div id="app">
      <form onsubmit="requestSwap()" class="page form-ajax-submit" data-name="swap-request">
        <!-- Top Navbar -->
        <div class="navbar">
          <div class="navbar-bg"></div>
          <div class="navbar-inner">
            <div class="left">
              <a class="link back">
                <i class="icon icon-back color-black"></i>
                <span class="if-not-md">Back</span>
              </a>
            </div>
            <div class="title text-align-center">Request Co-Swap</div>
            <div class="right">
              <button class="form-btn" type="submit"><i class="f7-icons color-black">checkmark_alt</i></button>
            </div>
          </div>
        </div>

        <!-- Scrollable page content -->
        <div class="page-content no-swipe-panel">
          <div class="card">
            <div class="card-content padding-vertical">
              <div class="list no-hairlines">
                <ul>
                  <li class="smart-select-list">
                    <div class="item-title item-label smart-select-label">Choose Co-Hort</div>
                    <a class="item-link smart-select smart-select-init" data-open-in="sheet" data-close-on-select="true">
                      <select name="selected-cohort" id="swap-cohort-drop" required validate data-validate-on-blur="true"></select>
                      <div class="item-content expensesChoseCohortSelect swapRequestChoseCohortSelect">
                        <div class="item-inner">
                          <div class="item-title">Choose Co-Hort</div>
                        </div>
                      </div>
                    </a>
                  </li>

                  <li>
                    <div class="item-content item-input">
                      <div class="item-inner">
                        <div class="item-title item-label custom-date-label">Co-Swap Date</div>
                        <div class="item-input-wrap addexpense-date">
                          <input type="text" placeholder="Select Date" readonly="readonly" id="requestswap-date" required validate validate-on-blur="true" />
                        </div>
                      </div>
                    </div>
                  </li>

                  <li class="smart-select-list">
                    <div class="item-title item-label smart-select-label">Time Type</div>
                    <a class="item-link smart-select smart-select-init" data-open-in="sheet" data-close-on-select="true">
                      <select id="request-timetype" placeholder="Please choose..." onchange="handleTypeChange(event);">
                        <option value="0">Weekday Day</option>
                        <option value="1">Weekday Night</option>
                        <option value="2">Weekend Day</option>
                        <option value="3">Weekend Night</option>
                        <option value="4">Holiday Day</option>
                        <option value="5">Holiday Night</option>
                        <option value="6">Hours</option>
                      </select>
                      <div class="item-content expensesChoseCohortSelect">
                        <div class="item-inner">
                          <div class="item-title">Choose Time</div>
                        </div>
                      </div>
                    </a>
                  </li>

                  <li class="item-content item-input item-input-outline" id="swap-qty-dd" style="display: none">
                    <div class="item-inner">
                      <div class="item-title item-label">Quantity</div>
                      <div class="item-input-wrap">
                        <input type="number" name="quantity" id="requestswap-quantity" min="1" value="1" validate data-validate-on-blur="true" />
                        <span class="input-clear-button"></span>
                      </div>
                    </div>
                  </li>

                  <li class="item-content item-input item-input-outline">
                    <div class="item-inner">
                      <div class="item-title item-label">Notes</div>
                      <div class="item-input-wrap">
                        <textarea id="request-notes" placeholder="Notes about the swap..."></textarea>
                        <span class="input-clear-button"></span>
                      </div>
                    </div>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
  </body>
</html>
