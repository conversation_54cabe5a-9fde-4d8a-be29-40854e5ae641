<!-- <!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Co-Parent</title>
</head>

<body>

    <div id="app">
        <div class="page" data-name="index">
            <h1
                style="position: relative; text-align: center; border-color: darkblue; border-style:groove; background-color:darkslateblue; color: aliceblue;">
                <a style="color: aliceblue;" href="views/login.html">Begin</a>
            </h1>
        </div>
    </div>
</body>

</html> -->


<!DOCTYPE html>
<html>

<head>
    <!-- Required meta tags-->
    <meta charset="utf-8" />
    <meta name="viewport"
        content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no, viewport-fit=cover" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <!-- Color theme for statusbar (Android only) -->
    <meta name="theme-color" content="#2196f3" />
    <!-- Your app title -->
    <title>Co-Parent</title>
    <!-- Path to Framework7 Library Bundle CSS -->
    <link rel="stylesheet" href="./css/framework7-bundle.min.css" />
    <preference name="scheme" value="app" />
    <preference name="hostname" value="localhost" />
    <!-- Path to your custom app styles-->
    <link rel="stylesheet" href="./css/custom.css" />
    <script src="https://js.stripe.com/v3/"></script>

</head>

<body>
    <!-- App root element -->
    <div id="app" store="{store}">
        <div class="view view-main"></div>
        <div class="panel panel-left panel-cover panel-init">
            <div class="block sidenavBar">
                <div class="sideNavBarLogoContainer">
                    <img src="./uploads/CPH_Logo.png" class="coparent-logo" alt="CoParent Logo">
                </div>

                <div class="text-align-center logoText color-black no-margin-vertical" style="font-weight: 500;">Co-Parent Hub</div>

                <div class="list menu-list no-hairlines navBarmenulist">
                    <ul>

                     
                        <li>
                            <a href="/viewcohort/" class="item-content item-link panel-close color-black">
                                <div class="item-media">
                                    <i class="icon f7-icons sidebarNavIcons">doc_circle_fill</i>
                                </div>
                                <div class="item-inner">
                                    <div class="item-title">View Co-Hort</div>
                                </div>
                            </a>
                        </li>

                        <li>
                            <a href="/viewchildren/" class="item-content item-link panel-close color-black">
                                <div class="item-media">
                                    <i class="icon f7-icons sidebarNavIcons">folder_fill_badge_person_crop</i>
                                </div>
                                <div class="item-inner">
                                    <div class="item-title">Children</div>
                                </div>
                            </a>
                        </li>

                        <li>
                            <a href="/expenses/" class="item-content item-link panel-close color-black">
                                <div class="item-media">
                                    <i class="icon f7-icons sidebarNavIcons">money_dollar_circle_fill</i>
                                </div>
                                <div class="item-inner">
                                    <div class="item-title">Co-Expenses</div>
                                </div>
                            </a>
                        </li>

                        <li>
                            <a href="/swap/" class="item-content item-link panel-close color-black">
                                <div class="item-media">
                                    <i class="icon f7-icons sidebarNavIcons">arrow_right_arrow_left_circle_fill</i>
                                </div>
                                <div class="item-inner">
                                    <div class="item-title">Co-Swap</div>
                                </div>
                            </a>
                        </li>

                        <li>
                            <a href="/files/" class="item-content item-link panel-close color-black">
                                <div class="item-media">
                                    <i class="icon f7-icons sidebarNavIcons">folder_circle_fill</i>
                                </div>
                                <div class="item-inner">
                                    <div class="item-title">Co-Files</div>
                                </div>
                            </a>
                        </li>

                        <li>
                            <a href="/billing/" class="item-content item-link panel-close color-black">
                                <div class="item-media">
                                    <i class="icon f7-icons sidebarNavIcons">creditcard_fill</i>
                                </div>
                                <div class="item-inner">
                                    <div class="item-title">Billing</div>
                                </div>
                            </a>
                        </li>

                        <li>
                            <a href="/profile/" class="item-content item-link panel-close color-black">
                                <div class="item-media">
                                    <i class="icon f7-icons sidebarNavIcons">person_alt_circle_fill</i>
                                </div>
                                <div class="item-inner">
                                    <div class="item-title">Profile</div>
                                </div>
                            </a>
                        </li>
                        

                    </ul>
                </div>

                <div class="sideNavBarBottomButtonContainer">
                    <a target="_blank" class="panel-close" onclick="logoutUser()">
                        <button class="col button button-raised button-fill">
                            Logout
                        </button>
                    </a>
                </div>
                <a href="https://www.coparenthub.com/terms-of-service/" style="text-align: center; color: gray;" target="_blank" class="external">
                    Terms of Service
                </a>
            </div>
        </div>

        <div class="page no-navbar no-toolbar no-swipeback login-screen-page no-swipe-panel">
            <div class="page-content login-screen-content">
                <div class="login-screen-title">
                    <img src="./uploads/CPH_Logo.png" class="coparent-logo-screen" alt="CoParent Logo">
                    <div class="block-title text-align-center logoText-screen color-black">Co-Parent Hub</div>
                    <p>Sign In</p>
                </div>
                <form>
                    <div class="list">
                        <ul>
                            <li class="item-content item-input item-input-outline">
                                <div class="item-inner">
                                    <div class="item-title item-label">Email</div>
                                    <div class="item-input-wrap">
                                        <input type="text" id="login-email" name="email" placeholder="Your Email"
                                            required />
                                        <span class="input-clear-button"></span>
                                    </div>
                                </div>
                            </li>
                            <li class="item-content item-input item-input-outline">
                                <div class="item-inner">
                                    <div class="item-title item-label">Password</div>
                                    <div class="item-input-wrap">
                                        <input type="password" id="login-password" name="password"
                                            placeholder="Your Password" required required validate
                                            data-validate-on-blur="true" />
                                        <span class="input-clear-button"></span>
                                    </div>
                                </div>
                            </li>
                            <li class="item-content item-input item-input-outline">
                                <div class="item-inner" style="align-items:flex-end">
                                    <a class="" href="/forgot/">Forgot Password ?</a>
                                </div>
                            </li>

                        </ul>
                    </div>
                    <div class="list">
                        <ul>
                            <li>
                                <a target="_blank" onclick="loginAPI()">
                                    <button class="button button-raised button-fill margin-vertical" id="signin">
                                        Sign In
                                    </button>
                                </a>
                            </li>

                            <li>
                                <a target="_blank" href="/register/">
                                    <button class="button button-raised button-fill margin-vertical" id="register">
                                        Register
                                    </button>
                                </a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>
        </div>
    </div>

    </div>
    <!-- Path to Framework7 Library Bundle JS-->
    <script type="text/javascript" src="./js/framework7-bundle.min.js"></script>

    <!-- Path to Jquery Import-->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"
        integrity="sha256-/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=" crossorigin="anonymous"></script>

    <!-- Path to your app js-->
    <script type="text/javascript" src="./store.js"></script>
    <script type="text/javascript" src="./main_app.js"></script>
    <script type="text/javascript" src="./js/login.js"></script>
    <script type="text/javascript" src="./js/register.js"></script>
    <script type="text/javascript" src="./js/viewcohart.js"></script>
    <script type="text/javascript" src="./js/viewfiles.js"></script>
    <script type="text/javascript" src="./js/expenses.js"></script>
    <script type="text/javascript" src="./js/inviteCoParent.js"></script>
    <script type="text/javascript" src="./js/sharedPhone.js"></script>
    <script type="text/javascript" src="./js/sharedEmail.js"></script>
    <script type="text/javascript" src="./js/join.js"></script>
    <script type="text/javascript" src="./js/addchild.js"></script>
    <script type="text/javascript" src="./js/profile.js"></script>
    <script type="text/javascript" src="./js/settleup.js"></script>
    <script type="text/javascript" src="./js/billing.js"></script>
    <script type="text/javascript" src="./js/swapRequest.js"></script>
    <script type="text/javascript" src="./js/viewchildren.js"></script>
    <script type="text/javascript" src="./js/addchildrensecondary.js"></script>
</body>

</html>