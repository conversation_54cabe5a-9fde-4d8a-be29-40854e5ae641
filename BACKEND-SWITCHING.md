# Backend Switching Guide

This guide explains how to safely switch between your local development backend and the live production backend at `https://app.coparenthub.com`.

## 🎯 Quick Start

### Switch to Live Backend
```bash
node switch-backend.js live
```

### Switch to Local Backend  
```bash
node switch-backend.js local
```

### Check Current Setting
```bash
node switch-backend.js status
```

## 🔧 How It Works

The system uses a centralized configuration in `server/public/js/config.js` that:

1. **Defines API base URLs** for both local and live environments
2. **Provides a helper function** (`apiCall`) that automatically uses the correct base URL
3. **Allows easy switching** between environments without code changes

## 📁 Files Modified

- `server/public/js/config.js` - Main configuration file
- `server/public/index.html` - Includes config.js before other scripts
- `server/public/js/expenses.js` - Updated to use apiCall function (example)

## 🚀 Usage Examples

### For Development
```bash
# Use local backend while developing
node switch-backend.js local
npm run dev  # Start your local server
```

### For Testing with Live Data
```bash
# Switch to live backend to test with production data
node switch-backend.js live
# Your frontend will now use https://app.coparenthub.com APIs
```

### Check Status
```bash
node switch-backend.js status
# Output: Current backend mode: live
#         Using: https://app.coparenthub.com
```

## ⚠️ Important Notes

1. **CORS**: Make sure the live backend allows requests from your local frontend
2. **Authentication**: You may need to login again when switching backends
3. **Data**: Live backend has real production data, local has development data
4. **Network**: Live backend requires internet connection

## 🔄 Updating Other JavaScript Files

To update other JS files to use the new system, replace:

```javascript
// Old way
let response = await fetch('/api/endpoint', options);

// New way  
let response = await apiCall('api/endpoint', options);
```

The `apiCall` function automatically:
- Adds the correct base URL
- Includes default headers
- Handles errors consistently

## 🛡️ Safety Features

- **Easy switching**: One command to change all API calls
- **No code changes**: Switch without modifying your application code
- **Status checking**: Always know which backend you're using
- **Fallback**: If something goes wrong, you can manually edit config.js

## 🐛 Troubleshooting

### "Cannot connect to backend"
- Check if the backend is running (local mode)
- Check internet connection (live mode)
- Verify the backend URL is correct

### "CORS errors"
- The live backend needs to allow your local frontend domain
- Contact backend administrator to whitelist your local domain

### "Authentication issues"
- Clear browser storage/cookies
- Login again after switching backends
- Check if authentication tokens are valid for the target backend
