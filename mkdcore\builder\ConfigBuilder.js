/*Powered By: Manaknightdigital Inc. https://manaknightdigital.com/ Year: 2020*/
/**
 * Config builder
 * @copyright 2020 Manaknightdigital Inc.
 * @link https://manaknightdigital.com
 * @license Proprietary Software licensing
 * <AUTHOR>
 *
 */

const fs = require('fs');
const path = require('path');
const Builder = require('./Builder');

module.exports = function (config) {
  this._config = config;
  this._config = {};
  this._database = {};
  this._render_list = [];
  Builder.call(this);

  this.set_config = function (config) {
    this._config = config;
  };

  this.set_database = function (database) {
    this._database = database;
  };

  this.isObject = function (val) {
    return typeof val === 'object';
  };

  this.buildKeyValuePair = function (namespace, obj) {
    let env = '';
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        let value = obj[key];
        if (Array.isArray(value)) {
          env += key.toUpperCase() + "=['" + value.join("','") + "']\n";
        } else if (this.isObject(value)) {
          env += this.buildKeyValuePair(namespace.toUpperCase() + key.toUpperCase(), obj);
        } else {
          env += namespace.toUpperCase() + key.toUpperCase() + '=' + value + '\n';
        }
      }
    }
    return env;
  };

  this.build = function () {
    let env = '';
    const fileName = '../../release/.env';
    for (const key in this._config) {
      if (this._config.hasOwnProperty(key)) {
        let value = this._config[key];
        if (Array.isArray(value)) {
          env += key.toUpperCase() + '=["' + value.join('","') + '"]\n';
        } else if (this.isObject(value)) {
          env += this.buildKeyValuePair(key.toUpperCase() + '_', value);
        } else {
          env += key.toUpperCase() + '=' + value + '\n';
        }
      }
    }
    for (const dbKey in this._database) {
      if (this._database.hasOwnProperty(dbKey)) {
        let value = this._database[dbKey];
        env += 'DB_' + dbKey.toUpperCase() + '=' + value + '\n';
      }
    }
    this.createDirectoriesRecursive(fileName);
    this.createDirectoriesRecursive('../../releaseGraphql/.env');
    fs.writeFileSync(path.join(__dirname, fileName), env, { mode: 0775 });
    fs.writeFileSync(path.join(__dirname, '../../releaseGraphql/.env'), env, {
      mode: 0775,
    });
  };

  this.destroy = function () {
    try {
      const fileName = path.join(__dirname, '../../release/.env');
      if (fs.existsSync(fileName)) {
        fs.unlinkSync(fileName);
      }
    } catch (err) {
      console.error('Config Builder Destroy Error', err);
    }
  };
};
