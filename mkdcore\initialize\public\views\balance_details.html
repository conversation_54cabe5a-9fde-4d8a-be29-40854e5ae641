<!DOCTYPE html>
<html>

<head>
  <!-- Required meta tags-->
  <meta charset="utf-8" />
  <meta name="viewport"
    content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no, viewport-fit=cover" />
  <meta name="apple-mobile-web-app-capable" content="yes" />
  <!-- Color theme for statusbar (Android only) -->
  <meta name="theme-color" content="#2196f3" />
  <!-- Your app title -->
  <title>Co-Parent</title>
</head>

<body>
  <!-- App root element -->
  <div id="app">
    <div class="page" data-name="balance_details">
      <!-- Top Navbar -->
      <div class="navbar">
        <div class="navbar-bg"></div>
        <div class="navbar-inner">
          <div class="left">
            <a class="link back">
              <i class="icon icon-back color-black"></i>
              <span class="if-not-md">Back</span>
            </a>
          </div>
          <div class="title text-align-center">Co-Swap</div>
          <div class="right">
            <!-- <a href="/swapRequest/"><i class="f7-icons color-black">plus_circle</i></a> -->
          </div>
        </div>
      </div>

      <!-- Scrollable page content -->
      <div class="page-content">
        <div class="list no-hairlines no-margin-vertical">
          <ul class="page-start-padding">
            <div class="tabs-animated-wrap" id="swaps-details-list">
            </div>
          </ul>
        </div>
      </div>
</body>

</html>