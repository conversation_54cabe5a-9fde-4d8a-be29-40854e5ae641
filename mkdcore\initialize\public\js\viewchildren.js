var $$ = Dom7;

async function childrenViewActiveCohort() {
    try {
        app.preloader.show();

        let response = await fetch('/api/cohort/', {
            method: 'GET',
            headers: {
                Accept: 'application/json',
                'Content-Type': 'application/json',
            },
        });
        let resJSON = await response.json();
        if (resJSON.data.length == 0) {
            throw "You don't have any active Co-Hort"
        }
        let optionSeq = 0;
        // var smartSelect = app.smartSelect.get('.childSelector');
        resJSON.data.forEach((cohort) => {
            optionSeq += 1;
            if (optionSeq == 1) {
                html = `
                <option selected value="${cohort.name}" data-cohortid="${cohort.id}">${cohort.name}</option>
                `;
                $$('#childrenChoseCohortSelect .item-after').html("");
                $$('#childrenChoseCohortSelect .item-after').html(cohort.name);
            }
            else {
                html = `
                <option value="${cohort.name}" data-cohortid="${cohort.id}">${cohort.name}</option>
                `;
            }
            $$('#children-cohortselector').append(html);
        });
        app.preloader.hide();
        getChildOfCohort();
    } catch (error) {
        app.preloader.hide();
        app.dialog.alert(error);
    }
}

async function getChildOfCohort() {
    try {
        app.preloader.show();
        var mainview = app.view.main;
        document.getElementById("childrenCardContainer").textContent = "";
        var selectedOption = document.getElementById('children-cohortselector');
        var id = selectedOption.options[selectedOption.selectedIndex].getAttribute('data-cohortid');

        let response = await fetch(`/api/user/children/cohort/${id}`, {
            method: 'GET',
            headers: {
                Accept: 'application/json',
                'Content-Type': 'application/json',
            },
        });
        let resJSON = await response.json();
        console.log(resJSON);
        if (response.status == 200) {
            resJSON.children.forEach(child => {
                html = `
                <div class="card childCard">
                    <div class="card-header childCardHeader">${child.name}</div>
                    <div class="card-content card-content-padding viewChildren-cardItem">
                            <div class="row no-gap no-margin-vertical">

                            </div>
                            <a class="editChildButton" target="_blank">
                                <button class="button button-raised button-fill editChildButton" data-childid="${child.id}">
                                    Edit Child Information
                                </button>   
                            </a>
                    </div>
                </div>
                `
                $$('#childrenCardContainer').append(html);
            });
            $$('.editChildButton').on('click', function (e) {
                e.stopPropagation();
                var childInfo = resJSON.children.find((child) => (child.id == this.dataset.childid));
                localStorage.setItem('ChildInfo', JSON.stringify(childInfo));
                store.dispatch('setActiveCohort', id)
                store.dispatch('setActiveChild', childInfo.id);
                mainview.router.navigate({ name: 'editChild' });
            });
            app.preloader.hide();
        }
        else {
            app.preloader.hide();
            throw resJSON.message;
        }

    } catch (error) {
        app.preloader.hide();
        app.dialog.alert(error);
    }
}

async function updateChildInformation() {
    try {
        app.preloader.show();
        var mainview = app.view.main;
        let childName = document.getElementById("editchildsecondary-childname").value;
        // let childAge = document.getElementById("editchildsecondary-childage").value;
        let cohortID = store.getters.activeCohort.value;
        let childID = store.getters.activeChild.value;

        if (addChildValidateFields(childName) && document.querySelectorAll('.input-invalid').length == 0) {
            let body = {
                name: childName,
                age: 10
            }

            let response = await fetch(`/api/user/cohort/${cohortID}/child/${childID}`, {
                method: 'PATCH',
                headers: {
                    Accept: 'application/json',
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(body),
            });

            // let resJSON = await response.json();

            if (response.status == 200) {
                app.preloader.hide();
                app.dialog.alert("Child information updated");
                mainview.router.navigate({ name: 'viewchildren' })
            }
            else {
                app.preloader.hide();
                throw "Couldn't update Child information, Please try again"
            }
        }
        else {
            app.preloader.hide();
            throw "Please enter valid information"
        }
    } catch (error) {
        app.preloader.hide();
        app.dialog.alert(error);
    }
}