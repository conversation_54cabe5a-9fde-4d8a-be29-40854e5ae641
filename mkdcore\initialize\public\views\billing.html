<!DOCTYPE html>
<html>

<head>
  <!-- Required meta tags-->
  <meta charset="utf-8" />
  <meta name="viewport"
    content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no, viewport-fit=cover" />
  <meta name="apple-mobile-web-app-capable" content="yes" />
  <!-- Color theme for statusbar (Android only) -->
  <meta name="theme-color" content="#2196f3" />
  <!-- Your app title -->
  <title>Co-Parent</title>
</head>

<body>
  <!-- App root element -->
  <div id="app">
    <div class="page" data-name="billing">
      <!-- Top Navbar -->
      <div class="navbar">
        <div class="navbar-bg"></div>
        <div class="navbar-inner">
          <div class="left">
            <a href="#" class="panel-toggle color-black">&#9776;</a>
          </div>
          <div class="title text-align-center">Billing</div>
          <div class="right">
          </div>
        </div>
      </div>

      <!-- Scrollable page content -->
      <div class="page-content">
        <div class="list no-hairlines no-margin-vertical">
          <div class="segmented segmented-raised block swapSuperOptionSelector">
            <a href="#card-tab" class="tab-link button button-outline tab-link-active">Card</a>
            <a href="#invoice-tab" class="tab-link button button-outline">Invoice</a>
          </div>
          <div class="tabs">
            <div class="tab page-content tab-active" id="card-tab">
              <div id="cards-container">
                
              </div>
              <div class="card">
                <a href="/add-card/" class="button button-raised button-fill">
                  Change credit card
                </a> 
              </div>
              <div style="text-align: center; margin-top: 40px;">
                <span onclick="cancel_sub()" style="width: max-content; color: black; opacity: .75; background-color: none; outline: none; border: none; margin-top: 40px; cursor: pointer;">
                  Cancel Subscription
                </span> 
              </div>
            </div>
            <div class="tab page-content" id="invoice-tab">
             
            </div>
          </div>
        </div>
      </div>
</body>

</html>