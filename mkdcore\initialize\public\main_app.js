var app = new Framework7({
  // App root element
  el: '#app',
  // App Name
  name: 'Co-Parent Hub',
  // App id
  id: 'com.coparent',
  // Enable swipe panel
  panel: {
    swipe: true,
  },
  // Add default routes
  routes: [
    {
      name: '/',
      path: '/',
      url: './index.html',
    },
    {
      name: 'register',
      path: '/register/',
      url: './views/register.html',
      reloadCurrent: true,
    },
    // {
    //   name: 'login',
    //   path: '/login/',
    //   url: './login.html',
    //   reloadCurrent: true,
    // },
    {
      name: 'newOr<PERSON>oin',
      path: '/neworjoin/',
      url: './views/newOrJoin.html',
    },
    {
      name: 'join',
      path: '/join/',
      url: './views/join.html',
    },
    {
      name: 'sharedPhone',
      path: '/sharedphone/',
      url: './views/sharedPhone.html',
    },
    {
      name: 'sharedEmail',
      path: '/sharedEmail/',
      url: './views/sharedEmail.html',
    },
    {
      name: 'inviteCoParent',
      path: '/invitecoparent/',
      url: './views/inviteCoParent.html',
    },
    {
      name: 'completeSetup',
      path: '/completeSetup/',
      url: './views/complete.html',
    },
    {
      name: 'home',
      path: '/home/',
      url: './views/home.html',
    },
    {
      name: 'expenses',
      path: '/expenses/',
      url: './views/expenses.html',
    },
    {
      name: 'addExpense',
      path: '/addExpense/',
      url: './views/addExpense.html',
    },
    {
      name: 'settleUp',
      path: '/settleUp/',
      url: './views/settleUp.html',
    },
    {
      name: 'viewcohort',
      path: '/viewcohort/',
      url: './views/viewcohort.html',
    },
    {
      name: 'files',
      path: '/files/',
      url: './views/files.html',
    },
    {
      name: 'editcohort',
      path: '/editcohort/',
      url: './views/editcohort.html',
    },
    {
      name: 'cohortedit1',
      path: '/cohortedit1/',
      url: './views/cohortedit1.html',
    },
    {
      name: 'cohartedit2',
      path: '/cohartedit2/',
      url: './views/cohartedit2.html',
    },
    {
      name: 'addfiles',
      path: '/addfiles/',
      url: './views/addfiles.html',
    },
    {
      name: 'createcohort',
      path: '/createcohort/',
      url: './views/createcohort.html',
    },
    {
      name: 'addchild',
      path: '/addchild/',
      url: './views/addchild.html',
    },
    {
      name: 'profile',
      path: '/profile/',
      url: './views/profile.html',
    },
    {
      name: 'swap',
      path: '/swap/',
      url: './views/swap.html',
    },
    {
      name: 'swap-request',
      path: '/swapRequest/',
      url: './views/swapRequest.html',
    },
    {
      name: 'viewchildren',
      path: '/viewchildren/',
      url: './views/viewchildren.html',
    },
    {
      name: 'addChildSecondary',
      path: '/addChildSecondary/',
      url: './views/addChildSecondary.html',
    },
    {
      name: 'editChild',
      path: '/editChild/',
      url: './views/editChild.html',
    },
    {
      name: 'extraMinutes',
      path: '/extra-minutes/',
      url: './views/extra_minutes_plans.html',
    },
    {
      name: 'subscriptionPage',
      path: '/subscription/',
      url: './views/subscription_page.html',
    },
    {
      name: 'billing',
      path: '/billing/',
      url: './views/billing.html',
    },
    {
      name: 'add-card',
      path: '/add-card/',
      url: './views/addCard.html',
    },
    {
      name: 'add-card',
      path: '/add-card/',
      url: './views/addCard.html',
    },
    {
      name: 'forgot',
      path: '/forgot/',
      url: './views/forgot.html',
    },
    {
      name: 'verify',
      path: '/verify/',
      url: './views/verify.html',
    },
    {
      name: 'reset',
      path: '/reset/',
      url: './views/reset.html',
    },
    {
      name: 'balance_details',
      path: '/balance_details/',
      url: './views/balance_details.html',
    },
  ],
  on: {
    init: () => {
      console.log('App initialized');
    },
    // pageInit: function () {
    //   console.log('Page initialized');
    // },
    pageInit: function (page) {
      // if (page.name === 'index') {
      //   redirectToLogin();
      // }
      if (page.name === 'createcohort') {
        watchCohortSplit();
      }
      if (page.name === 'viewcohort') {
        redirectToSubscribe(page.name);
        getCohortView();
      }
      if (page.name === 'files') {
        redirectToSubscribe(page.name);

        fillSwapDropdown('#files-cohort-drop-filter', '.filesChoseCohortSelect');
        getCohortFiles();
      }
      if (page.name === 'add-files') {
        fillSwapDropdown('#files-cohort-drop-add', '.addFilesChoseCohortSelect');
      }
      if (page.name === 'sharedPhone') {
        redirectToSubscribe(page.name);

        getAvailableNumbers();
        searchbyAreaCode();
        checkEmail();
      }
      if (page.name === 'sharedEmail') {
        redirectToSubscribe(page.name);

        setDefaultEmail();
      }
      if (page.name === 'expenses') {
        redirectToSubscribe(page.name);

        getActiveCohorts();
        // monitorChangeOnButton();
      }
      if (page.name === 'addExpense') {
        redirectToSubscribe(page.name);

        init();
        // initalGetCohort();
      }
      if (page.name === 'profile') {
        getProfile();
      }
      if (page.name === 'swap') {
        redirectToSubscribe(page.name);

        renameTabs();
        initTabs();
        fillSwapDropdown('#swap-cohort-drop-filter', '.swapChoseCohortSelect');
        getSwaps();
      }
      if (page.name === 'swap-request') {
        redirectToSubscribe(page.name);

        initializeDateCalender();
        fillSwapDropdown('#swap-cohort-drop', '.swapRequestChoseCohortSelect');
        // var today = new Date();
        // var dd = today.getDate();
        // var mm = today.getMonth() + 1;
        // var yyyy = today.getFullYear();
        // if (dd < 10) {
        //   dd = '0' + dd;
        // }
        // if (mm < 10) {
        //   mm = '0' + mm;
        // }
        // today = yyyy + '-' + mm + '-' + dd;
        // $$(`#requestswap-date`)[0].setAttribute('min', today);
      }
      if (page.name === 'settleUp') {
        redirectToSubscribe(page.name);

        populateSettleUp();
      }
      if (page.name === 'cohortedit1') {
        let resCo = JSON.parse(localStorage.getItem('cohortInfo'));
        let loginUser = JSON.parse(localStorage.getItem('user'));
        if (loginUser.id == resCo.parent_1) {
          document.getElementById('editCohortParent1').textContent = 'For Expense: Default Split for You';
          document.getElementById('editCohortParent2').textContent = 'For Expense: Default Split for Your Co-Parent';
        } else {
          document.getElementById('editCohortParent1').textContent = 'For Expense: Default Split for Your Co-Parent';
          document.getElementById('editCohortParent2').textContent = 'For Expense: Default Split for You';
        }
        document.getElementById('edit-cohort-cohort-name').value = resCo.name;
        document.getElementById('edit-cohort-parent-one-split').value = resCo.parent_1_default_split;
        document.getElementById('edit-cohort-parent-two-split').value = resCo.parent_2_default_split;
      }
      if (page.name === 'viewchildren') {
        redirectToSubscribe(page.name);

        childrenViewActiveCohort();
      }
      if (page.name === 'addChildSecondary') {
        redirectToSubscribe(page.name);

        addchildrenViewActiveCohort();
      }
      if (page.name === 'editChild') {
        redirectToSubscribe(page.name);

        let childInfo = JSON.parse(localStorage.getItem('ChildInfo'));
        document.getElementById('editchildsecondary-childname').value = childInfo.name;
        // document.getElementById('editchildsecondary-childage').value = childInfo.age;
      }
      if (page.name === 'extra-minutes-plan') {
        redirectToSubscribe(page.name);

        setButtonsForCheckout(page);
      }
      if (page.name === 'subscription-page') {
        preventSubscribe();
        setSubscriptionForm(page);
      }
      if (page.name === 'join') {
        redirectToSubscribe(page.name);
      }
      if (page.name === 'newOrJoin') {
        redirectToSubscribe(page.name);
      }
      if (page.name === 'billing') {
        billingInitial();
      }
      if (page.name === 'add-card') {
        changeCard();
      }
      if (page.name === 'balance_details') {
        fetchSwaps(page);
      }
    },
  },
  // ...other parameters
});

// app.on('pageInit', function (page) {
//   if (page.name === 'dashboard') {
//     console.log("ViewScrip Loaded!")
//   }
// });

var mainView = app.views.create('.view-main', { dynamicNavbar: true, url: '/' });

// const login = async () => {
//   mainView.router.navigate({ name: 'newOrJoin' }); Hello World
// }

// var $$ = Dom7;
// $$(document).on('page:init', '.page[data-name="viewcohart"]', function (e) {
//   console.log('This has been initializied!!');
// });

// app.on('pageInit', function(page){

// });
