<% if(it.layout_clean_mode) {%> <% layout("../layouts/admin/Clean") %> <% } else {%> <% layout("../layouts/admin/Main") %> <%}%> <%~ includeFile("../partials/admin/Breadcrumb",
it)%>

<div class="tab-content mx-4 my-4">
  <div class="row">
    <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
      <%~ includeFile("../partials/admin/GlobalResponse.eta", it) %>
      <div class="card" style="border-bottom: 1px solid #ccc">
        <div class="card-body">
          <h4 class="primaryHeading2 text-md-left"><%= it.heading %></h4>
          <table class="table table-striped">
            <tr>
              <th>ID:</th>
              <td><%= it.detail_fields["id"] %></td>
            </tr>

            <tr>
              <th>To:</th>
              <td><%= it.detail_fields["to"] %></td>
            </tr>

            <tr>
              <th>Status:</th>
              <td><%= it.detail_fields["status"] %></td>
            </tr>

            <tr>
              <th>Name:</th>
              <td><%= it.detail_fields["name"] %></td>
            </tr>

            <tr>
              <th>Description:</th>
              <td><%= it.detail_fields["description"] %></td>
            </tr>
            <tr>
              <th>Created At:</th>
              <td><%= new Date(it.detail_fields["created_at"]).toLocaleString("en-us",{month:"short",day:"numeric",year:"numeric"}) %></td>
            </tr>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>
