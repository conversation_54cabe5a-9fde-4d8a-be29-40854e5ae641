var $$ = Dom7;

async function billingInitial() {
  try {
    app.preloader.show();
    let customer = await fetch('/api/customer-card');
    const { default_source } = (await customer.json()).customer;
    const res = await fetch('/api/invoices');
    const {
      invoices: { data },
    } = await res.json();
    const paymentMethod = await fetch('/api/payment-methods');
    app.preloader.hide();
    const { cards } = await paymentMethod.json();
    let html = '';
    data.forEach(({ total, period_end, invoice_pdf }) => {
      html += `
    <div class="card">
    <div class="card-content card-content-padding row">
      <div class="col-60">
        <div>
          <strong>Invoice $${total / 100}</strong> 
        </div>
        <div>
          ${new Date(period_end * 1000).toLocaleDateString('en-us', { day: '2-digit', month: 'short', year: 'numeric' })}
        </div>
      </div>
      <a href="${invoice_pdf}" download class="external button button-raised button-fill col-40">
        Download
      </a> 
    </div>
  </div>
    `;
    });
    $$('#invoice-tab').append(html);
    html = '';
    cards.data
      .filter(({ id }) => default_source == id || cards.data.length === 1)
      .forEach(({ card: { brand, last4, exp_month, exp_year } }) => {
        html += `
    <div class="card">
        <div class="card-content card-content-padding row">
          <div class="col-60">
            <div>
              <strong>${brand.toUpperCase()} Card</strong> 
            </div>
            <div>
              Exp Date ${exp_month}/${exp_year}
            </div>
          </div>
          <div class="col-40">
            ****${last4}
          </div>
        </div>
        
      </div>
    `;
        $$('#cards-container')[0].innerHTML = html;
      });
  } catch (error) {}
}

async function changeCard() {
  try {
    app.preloader.show();
    const res = await fetch('api/create-intent', {
      method: 'POST',
    });
    const resJSON = await res.json();
    const options = {
      clientSecret: resJSON.clientSecret,
    };
    const elements = stripe.elements(options);
    const cardElement = elements.create('card');
    cardElement.mount('#card-container');
    app.preloader.hide();

    document.getElementById('card-submit').addEventListener('click', async (e) => {
      e.preventDefault();
      app.preloader.show();

      const token = await stripe.createToken(cardElement);
      if (token.error) {
        app.dialog.alert(token.error.message);
        app.preloader.hide();
        return;
      }
      const res = await fetch('/api/create-source', {
        method: 'POST',
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token: token?.token?.id }),
      });
      const resJSON = await res.json();

      app.preloader.hide();
      if (res.ok) {
        app.view.main.router.navigate({ name: 'billing' });
      } else {
        app.dialog.alert(resJSON.message);
      }
    });
  } catch (error) {
    app.preloader.hide();
  }
}

async function cancel_sub() {
  try {
    app.preloader.show();
    let cohorts = await fetch('/api/cohort');
    cohorts = await cohorts.json();
    app.preloader.hide();
    // if (cohorts.data.length >= 0) {
    //   app.dialog.alert('You still have active Co-Horts. You must delete all Co-Horts before you can cancel your billing subscription.', 'Warning');
    // } else {
    app.dialog.confirm(
      'You are about to cancel your Co-Parent Hub subscription. To proceed with your cancellation, click "OK." To keep your subscription, click "Cancel."',
      'Warning',
      handleSubCancellingConfirm,
    );
    // }
  } catch (error) {
    app.preloader.hide();
  }
}

async function handleSubCancellingConfirm() {
  try {
    app.preloader.show();
    const res = await fetch('/api/cancel-subscription', {
      method: 'POST',
    });
    const resJson = await res.json();
    app.preloader.hide();
    if (res.ok) {
      app.dialog.alert(
        `Your subscription will be cancelled at ${new Date(resJson.cancelledSub.cancel_at * 1000).toLocaleDateString('en-us', {
          day: 'numeric',
          month: 'short',
          year: 'numeric',
        })}`,
      );
    }
  } catch (error) {}
}
