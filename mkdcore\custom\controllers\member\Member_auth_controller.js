const AuthService = require('../../services/AuthService.js');
const JwtService = require('../../services/JwtService.js');
const app = require('express').Router();
const db = require('../../models');
const { protect } = require('../../middlewares/auth_middleware.js');
const UploadService = require('../../services/UploadService');
const upload = UploadService.upload('profile/pic');
const StripeService = require('../../services/StripeApi');
const sgMail = require('@sendgrid/mail');
const mailService = require('../../services/MailService');

app.post('/api/user/login', async function (req, res, next) {
  try {
    const { email, password } = req.body;
    const { user } = await AuthService.login(email, password, 2);
    const token = JwtService.createAccessToken(user);

    const userObj = await db.user.get_user_credential(user, db);

    res.cookie('jwt', token, {
      expires: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000),
      secured: false,
    });
    res.status(200).json({
      status: 'success',
      user: userObj,
    });
  } catch (error) {
    return res.status(400).json({
      error: true,
      message: error.message,
    });
  }
});

app.post('/api/user/register', async function (req, res, next) {
  try {
    const { first_name, last_name, email, password, phone_number } = req.body;
    const phoneExist = await db.user.findOne({ where: { phone: phone_number } });
    if (phoneExist) {
      return res.status(400).json({
        status: 'fail',
        message: 'This phone number already exists',
      });
    }
    const stripeCustomer = await StripeService.stripeType('customer_create', { email });
    const { user } = await AuthService.register(email, password, 2, { first_name, last_name, phone: phone_number, stripe_uid: stripeCustomer.id });
    const token = JwtService.createAccessToken(user.id);

    res.cookie('jwt', token, {
      expires: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000),
      secured: false,
    });
    const userObj = { id: user.id, role: user.role_id, status: user.status, image: user.image, first_name, last_name, email, phone_number };
    res.status(200).json({
      status: 'success',
      user: userObj,
    });
  } catch (error) {
    return res.status(400).json({
      error: true,
      message: error.message,
    });
  }
});

app.post('/api/user/forgot', async function (req, res, next) {
  try {
    const { email } = req.body;
    console.log(req.body);
    if (!email) {
      return res.status(400).json({
        status: 'error',
        message: 'Please enter your email',
      });
    }
    const code = await AuthService.forgotPassword(email);
    const { user } = await db.credential.findOne({ where: { email }, include: [{ model: db.user, as: 'user' }] });
    console.log(user);
    sgMail.setApiKey('*********************************************************************');

    const mailTemplate = await mailService.template('reset-password');
    mailService.initialize({
      from: '<EMAIL>',
      to: email,
    });
    const injectedMailTemplate = mailService.inject(
      {
        body: mailTemplate.html,
        subject: mailTemplate.subject,
      },
      {
        username: `${user.first_name} ${user.last_name}`,
        verification_code: code,
      },
    );
    await sgMail.send(injectedMailTemplate);
    // await sendSMS({
    //   to: phone,
    //   text: `${user.first_name} has invited you to join Co-Parent Hub to better manage communication about your child. Check your email for details. `,
    // });

    res.status(200).json({
      status: 'success',
    });
  } catch (error) {
    return res.status(400).json({
      error: true,
      message: error.message,
    });
  }
});
app.post('/api/user/verify', async function (req, res, next) {
  try {
    const { code } = req.body;
    if (!code) {
      return res.status(400).json({
        status: 'error',
        message: 'Please enter the reset code',
      });
    }
    const { credential } = await AuthService.verifyForgotPassword(code);

    res.status(200).json({
      status: 'success',
      message: 'code verified',
    });
  } catch (error) {
    console.log(error);
    return res.status(400).json({
      error: true,
      message: error.message,
    });
  }
});
app.post('/api/user/reset', async function (req, res, next) {
  try {
    const { code, password } = req.body;
    if (!code) {
      return res.status(400).json({
        status: 'error',
        message: 'Please enter the reset code',
      });
    }
    if (!password) {
      return res.status(400).json({
        status: 'error',
        message: 'Please enter your password',
      });
    }
    const { credential } = await AuthService.verifyForgotPassword(code);

    await AuthService.resetPassword(password, credential);

    res.status(200).json({
      status: 'success',
      message: 'Password updated',
    });
  } catch (error) {
    return res.status(400).json({
      error: true,
      message: error.message,
    });
  }
});

app.post('/api/user/profile', protect, upload.single('image'), async function (req, res, next) {
  try {
    const { id } = req.user;

    await db.user.edit({ ...req.body, image: req?.file?.filename }, id);

    if (req.body.email) {
      await db.credential.edit({ email: req.body.email }, id);
    }
    if (req.body.password) {
      await AuthService.resetPassword(req.body.password, id);
    }
    res.status(200).json({
      status: 'success',
      message: 'Profile has been updated with success',
    });
  } catch (error) {
    return res.status(400).json({
      error: true,
      message: error.message,
    });
  }
});

app.post('/api/user/logout', async function (req, res, next) {
  res.cookie('jwt', '', {
    expires: new Date(Date.now()),
    secured: false,
  });
  res.status(200).json({
    status: 'success',
    message: 'Logged out',
  });
});
app.get('/api/user/profile', protect, async function (req, res, next) {
  try {
    res.status(200).json({
      status: 'success',
      user: req.user,
    });
  } catch (error) {}
});

app.get('/api/plans', protect, async function (req, res, next) {
  try {
    const plans = await StripeService.stripeType('product_list_all');
    res.status(200).json({
      plans,
    });
  } catch (error) {
    console.log(error);
    res.status(400).json({
      status: 'fail',
      message: error.message,
    });
  }
});

app.get('/api/invoices', protect, async function (req, res, next) {
  try {
    const invoices = await StripeService.stripeType('invoice_list_all', { customer: req.user.stripe_uid });
    res.status(200).json({
      invoices,
    });
  } catch (error) {
    console.log(error);
    res.status(400).json({
      status: 'fail',
      message: error.message,
    });
  }
});

app.post('/api/payment/session/extra-minutes', protect, async function (req, res, next) {
  try {
    const { quantity, cohort_id } = req.body;
    const session = await StripeService.stripeType('session_create', {
      mode: 'payment',
      payment_method_types: ['card'],
      line_items: [
        {
          price: 'price_1KO7rrAQLMONiHkQ9okTEVCt',
          quantity,
        },
      ],
      metadata: {
        cohort_id,
        quantity,
        product_name: 'extra_minutes',
      },
      success_url: 'http://localhost:3001',
      cancel_url: 'http://localhost:3001',
      customer: req.user.stripe_uid,
    });
    res.status(200).json({
      status: 'success',
      session,
    });
  } catch (error) {
    res.status(400).json({
      status: 'fail',
      message: error.message,
    });
  }
});
app.post('/api/subscription/session', protect, async function (req, res, next) {
  try {
    const session = await StripeService.stripeType('session_create', {
      mode: 'subscription',
      payment_method_types: ['card'],
      line_items: [
        {
          price: req.body.priceId,
          quantity: 1,
        },
      ],
      success_url: 'http://localhost:3001',
      cancel_url: 'http://localhost:3001',
      customer: req.user.stripe_uid,
    });
    res.status(200).json({
      status: 'success',
      session,
    });
  } catch (error) {
    res.status(400).json({
      status: 'fail',
      message: error.message,
    });
  }
});

app.patch('/api/subscription', protect, async function (req, res, next) {
  try {
    const { quantity } = req.body;
    const { subscription: subObj } = req.user;
    const { itemId, id } = subObj;
    const subscription = await StripeService.stripeType('subscription_update', {
      subscriptionId: req.user.subscription.id,
      params: {
        items: [
          {
            id: itemId,
            quantity,
          },
        ],
      },
    });
    res.status(200).json({
      status: 'success',
      subscription,
    });
  } catch (error) {
    res.status(400).json({
      status: 'fail',
      message: error.message,
    });
  }
});
app.delete('/api/subscription/remove-cohort', protect, async function (req, res, next) {
  try {
    const { cohort_id } = req.body;
    const { subscription: subObj } = req.user;
    const { itemId, id, quantity } = subObj;
    const cohortP1 = await db.cohort.update({ parent_1: null }, { where: { id: cohort_id, parent_1: req.user.id } });
    const cohortP2 = await db.cohort.update({ parent_2: null }, { where: { id: cohort_id, parent_2: req.user.id } });
    if (!cohortP1[0] && !cohortP2[0]) {
      return res.status(400).json({
        status: 'fail',
        message: "Couldn't find this cohort",
      });
    }
    const subscription = await StripeService.stripeType('subscription_update', {
      subscriptionId: req.user.subscription.id,
      params: {
        items: [
          {
            id: itemId,
            quantity: quantity - 1,
          },
        ],
      },
    });
    res.status(200).json({
      status: 'success',
      message: 'Removed from cohort',
    });
  } catch (error) {
    res.status(400).json({
      status: 'fail',
      message: error.message,
    });
  }
});

app.post('/api/cancel-subscription', protect, async function (req, res, next) {
  if (!req?.user?.subscription?.id) {
    return res.status(400).json({
      status: 'fail',
      message: "There's not active subscription",
    });
  }

  const cancelledSub = await await StripeService.stripeType('subscription_update', { subscriptionId: req.user.subscription.id, params: { cancel_at_period_end: true } });
  try {
    res.status(200).json({
      status: 'success',
      cancelledSub,
    });
  } catch (error) {
    res.status(400).json({
      status: 'fail',
      message: error.message,
    });
  }
});

app.post('/api/checkout', async function (req, res, next) {
  try {
    const event = req.body;
    if (event.type === 'checkout.session.completed') {
      const { metadata } = event.data.object;
      if (metadata) {
        const { product_name, quantity, cohort_id } = metadata;
        if (product_name === 'extra_minutes') {
          const amount = 50 * quantity;
          // await db.extra_minutes.insert({ cohort_id, amount });
        }
      }
    }
    if (event.type === 'invoice.created') {
      let data = event.data.object;
      const stripe_id = data.id;
      delete data.id;

      const payment_attempted = {
        no: 0,
        yes: 1,
      };
      const refunded = {
        no: 0,
        yes: 1,
      };

      const status = {
        draft: 0,
        open: 1,
        paid: 2,
        uncollectible: 3,
        void: 4,
      };
      data.status = status[data.status];
      data.refunded = refunded[data.refunded];
      data.payment_attempted = payment_attempted[data.payment_attempted];
      const user = await db.user.findOne({ where: { stripe_uid: data.customer } });
      await db.stripe_invoice.insert({
        user_id: user.id,
        invoice_url: data.hosted_invoice_url,
        customer_stripe_id: data.customer,
        subscription_stripe_id: data.subscription,
        invoice_pdf_url: data.invoice_pdf,
        amount_total: data.total,
        stripe_id,
        ...data,
      });
    }
    res.status(200).json({
      status: 'success',
    });
  } catch (error) {
    console.log(error);
    res.status(400).json({
      status: 'fail',
      message: error.message,
    });
  }
});

app.post('/api/create-subscription', protect, async function (req, res, next) {
  try {
    const { customer, price } = req.body;
    console.log(customer, price);
    const createSub = await StripeService.stripeType('subscription_create', {
      customer,
      items: [
        {
          price: 'price_1KMyBfAQLMONiHkQI4FznQoB',
        },
      ],
      payment_behavior: 'default_incomplete',
      expand: ['latest_invoice.payment_intent'],
    });
    res.status(200).json({
      status: 'success',
      subscriptionId: createSub.id,
      clientSecret: createSub.latest_invoice.payment_intent.client_secret,
    });
  } catch (error) {
    res.status(400).json({
      status: 'fail',
      message: error.message,
    });
  }
});
app.post('/api/update-subscription', protect, async function (req, res, next) {
  try {
    const { customer, price } = req.body;
    const updateSub = await StripeService.stripeType('subscription_update', {
      subscriptionId: 'sub_1KR0rpAQLMONiHkQ7G1hWzWi',
      params: {
        matadata: {
          hello: 'hello',
        },
      },
    });
    res.status(200).json({
      status: 'success',
      subscriptionId: createSub.id,
      clientSecret: createSub.latest_invoice.payment_intent.client_secret,
    });
  } catch (error) {
    res.status(400).json({
      status: 'fail',
      message: error.message,
    });
  }
});
app.get('/api/payment-methods', protect, async function (req, res, next) {
  try {
    const cards = await StripeService.stripeType('paymentMethod_list_all', {
      customer: req.user.stripe_uid,
      type: 'card',
    });

    res.status(200).json({
      status: 'success',
      cards,
    });
  } catch (error) {
    res.status(400).json({
      status: 'fail',
      message: error.message,
    });
  }
});
app.post('/api/create-intent', protect, async function (req, res, next) {
  try {
    const setupIntent = await StripeService.stripeType('setup_intent', { usage: 'off_session' });
    res.status(200).json({
      status: 'success',
      setupIntent,
    });
  } catch (error) {
    res.status(400).json({
      status: 'fail',
      message: error.message,
    });
  }
});
app.post('/api/create-source', protect, async function (req, res, next) {
  try {
    const card = await StripeService.stripeType('card_create', {
      customerId: req.user.stripe_uid,
      params: { source: req.body.token },
    });
    await StripeService.stripeType('customer_update', {
      customerId: req.user.stripe_uid,
      params: { default_source: card.id },
    });

    const pms = await StripeService.stripeType('paymentMethod_list_all', {
      customer: req.user.stripe_uid,
      type: 'card',
    });
    // console.log(pms);
    // pms.data.forEach(async (pm) => {
    //   if (pm.id !== card.id) {
    //     await StripeService.stripeType('paymentMethod_detach', pm.id);
    //   }
    //   console.log(pm.id);
    // });

    res.status(200).json({
      status: 'success',
      card,
    });
  } catch (error) {
    res.status(400).json({
      status: 'fail',
      message: error.message,
    });
  }
});
app.post('/api/update-customer', protect, async function (req, res, next) {
  try {
    const card = await StripeService.stripeType('customer_update', {
      customerId: req.user.stripe_uid,
      params: { default_source: req.body.card },
    });
    res.status(200).json({
      status: 'success',
      card,
    });
  } catch (error) {
    res.status(400).json({
      status: 'fail',
      message: error.message,
    });
  }
});
app.get('/api/customer-card', protect, async function (req, res, next) {
  try {
    const customer = await StripeService.stripeType('customer_retrieve', { customerId: req.user.stripe_uid });
    res.status(200).json({
      status: 'success',
      customer,
    });
  } catch (error) {
    res.status(400).json({
      status: 'fail',
      message: error.message,
    });
  }
});
app.post('/api/detach-method', protect, async function (req, res, next) {
  try {
    await StripeService.stripeType('paymentMethod_detach', req.body.pm);
    res.status(200).json({
      status: 'success',
      message: 'payment method detached',
    });
  } catch (error) {
    res.status(400).json({
      status: 'fail',
      message: error.message,
    });
  }
});

module.exports = app;
