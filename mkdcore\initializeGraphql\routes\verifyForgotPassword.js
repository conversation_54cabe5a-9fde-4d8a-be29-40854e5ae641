const { request, response } = require('express');

const authService = require('../services/AuthService');
const jwtService = require('../services/JwtService');

module.exports = {
  /**
   * Verify forgot password router
   * @param {request} req
   * @param {response} res
   */
  get: async function (req, res) {
    try {
      const payload = await authService.verifyForgotPassword(req.body.code);

      const response = {
        access_token: jwtService.createAccessToken(payload),
        refresh_token: jwtService.createRefreshToken(payload),
      };

      res.status(200).json({ success: true, payload: response });
    } catch (error) {
      res.status(500).json({ success: false, message: error.message });
    }
  },
};
