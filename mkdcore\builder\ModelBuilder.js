/*Powered By: Manaknightdigital Inc. https://manaknightdigital.com/ Year: 2020*/
/**
 * Model builder
 * @copyright 2020 Manaknightdigital Inc.
 * @link https://manaknightdigital.com
 * @license Proprietary Software licensing
 * <AUTHOR>
 *
 */

const fs = require('fs');
const path = require('path');
const Builder = require('./Builder');
const bcrypt = require('bcryptjs');

const Validation = require('../source/utils/models/validations/db_validation');

const associationType = {
  '1:1': 'belongsTo',
  '1:N': 'belongsTo',
  'N:1': 'belongsTo',
  'N:M': 'belongsToMany', // TODO
  'M:N': 'belongsToMany', // TODO
};

const inverseAssociation = {
  '1:1': 'hasOne',
  '1:N': 'hasMany',
  'N:1': 'hasMany',
  'N:M': 'belongsToMany',
  'M:N': 'belongsToMany',
};

module.exports = function (config) {
  this._config = config;
  this._models = [];
  this._model_template = [];
  this._migration_template = [];
  this._seed_template = [];
  this._seed_template_window = [];
  this._template = [];
  this._translations = {};
  this._render_list = [];
  this._association_reference = [];

  Builder.call(this);

  this.set_model = function (model) {
    this._models = model;
  };

  this.build = function () {
    try {
      const templateName = '../mkdcore/source/model/Model.js';
      const seedTemplate = fs.readFileSync('../mkdcore/source/model/seed.js', 'utf8');
      let seedList = [];
      if (fs.existsSync(templateName)) {
        this._template = fs.readFileSync(templateName, 'utf8');
        const today = new Date();
        const year = today.getFullYear();
        this._models.forEach((model) => {
          const modelJoins = model.join;
          if (modelJoins.length > 0) {
            modelJoins.forEach((association) => {
              // Create a reference to do inverse association from child table
              this._association_reference.push({
                parentTable: model.name,
                association,
              });
            });
          }
        });
        for (let i = 0; i < this._models.length; i++) {
          const model = this._models[i];

          let modelTemplate = this._template;
          // let upper_case_model = this.toCamelCaseString(model.name);
          let ucName = this.ucFirst(model.name);
          modelTemplate = this.getFields(model, modelTemplate);
          // modelTemplate = this.inject_substitute(modelTemplate, 'associate', this.getJoinFields(model.join));
          modelTemplate = this.inject_substitute(modelTemplate, 'uc_name', ucName);
          modelTemplate = this.inject_substitute(modelTemplate, 'year', year);
          modelTemplate = this.inject_substitute(modelTemplate, 'method', model.method);
          modelTemplate = this.inject_substitute(modelTemplate, 'pre', model.pre.join('\n'));
          modelTemplate = this.inject_substitute(modelTemplate, 'post', model.post.join('\n'));
          modelTemplate = this.inject_substitute(modelTemplate, 'count', model.count);

          modelTemplate = this.inject_substitute(modelTemplate, 'name', model.name);

          // Associations
          let associationTemplate = '';
          let paginateTemplate = '';
          let joined_row = '';
          const associations = model.join;
          if (associations.length > 0) {
            for (let association of associations) {
              const childTable = association.name;
              const joinField = association.field;
              const _associationType = association.type || '1:N';

              associationTemplate += `\t\n${ucName}.${associationType[_associationType] || 'belongsTo'}(models.${childTable}, {
                foreignKey: "${joinField}",
                as: "${association.as || childTable}",
                constraints: false,
              })`;

              // generate paginate
              paginateTemplate += this.generatePaginateTemplate(ucName, association);
              // generate get_joined_row
              joined_row += this.generate_join_row(model.name, association.name);
            }
          }
          // Inverse Relationship
          const reference_child_table = this._association_reference.filter((item) => item.association.name === model.name);

          if (reference_child_table.length > 0) {
            reference_child_table.forEach((reference) => {
              const parentTable = reference.parentTable;
              const forwardAssociation = reference.association;
              associationTemplate += `\t\n${ucName}.${inverseAssociation[forwardAssociation.type] || 'hasMany'}(models.${parentTable}, {
                foreignKey: "${forwardAssociation.field}",
                constraints: false,
                as: "${parentTable}"
              })`;

              // as: "${forwardAssociation.as ? forwardAssociation.as : parentTable}"
              paginateTemplate += this.generatePaginateTemplate(ucName, {
                name: parentTable,
              });
              joined_row += this.generate_join_row(model.name, parentTable);
            });
          }

          modelTemplate = this.inject_substitute(modelTemplate, 'associate', associationTemplate);

          modelTemplate = this.inject_substitute(modelTemplate, 'get_paginated', paginateTemplate);
          modelTemplate = this.inject_substitute(modelTemplate, 'get_join_row', joined_row);

          if (model.timestamp) {
            modelTemplate = this.inject_substitute(modelTemplate, 'timestamp', 'true');
            modelTemplate = this.inject_substitute(modelTemplate, 'timestamp_field', 'created_at: DataTypes.DATEONLY,\n' + '      updated_at: DataTypes.DATE,');
          } else {
            modelTemplate = this.inject_substitute(modelTemplate, 'timestamp_field', '');
            modelTemplate = this.inject_substitute(modelTemplate, 'timestamp', 'false');
          }

          modelTemplate = this.inject_substitute(modelTemplate, 'mapping', this.getMapping(model.mapping, ucName));

          const fileName = '../../release/models/' + model.name + '.js';
          const fileNameGraphql = '../../releaseGraphql/models/' + model.name + '.js';

          this.createDirectoriesRecursive(fileName);
          this.createDirectoriesRecursive(fileNameGraphql);
          fs.writeFileSync(path.join(__dirname, fileName), modelTemplate, {
            mode: 0775,
          });
          fs.writeFileSync(path.join(__dirname, fileNameGraphql), modelTemplate, { mode: 0775 });
          this._model_template.push({ [fileName]: modelTemplate });

          seedList = this.addSeed(model, seedList);
        }

        const seedTemplateFilled = this.inject_substitute(seedTemplate, 'seeds', seedList.join('\n'));
        this.createDirectoriesRecursive(`../../release/seed.js`);
        fs.writeFileSync(path.join(__dirname, '../../release/seed.js'), seedTemplateFilled, {
          mode: 0775,
        });
        console.log('Model build success');
      } else {
        console.error('Model BUILDER FILE NOT EXIST ' + fileName);
      }
    } catch (err) {
      console.error('Model Builder Build Error', err);
    }
  };

  this.destroy = function () {
    // for (const key in this._copy) {
    //   if (this._copy.hasOwnProperty(key)) {
    //     let value = this._copy[key];
    //     try {
    //       if (fs.existsSync(key)) {
    //         fs.unlinkSync(value);
    //       }
    //     } catch (err) {
    //       console.error("Copy Builder Destroy Error", err);
    //     }
    //   }
    // }
  };

  this.addSeed = function (model, seedList) {
    if (model.seed.length < 1) {
      return seedList;
    }

    seedList.push(this.seedRow(model));
    return seedList;
  };

  this.seedRow = function (model) {
    let seeds = [];
    for (let i = 0; i < model.seed.length; i++) {
      const seedElement = model.seed[i];
      if (seedElement.password) {
        seedElement.password = eval(seedElement.password);
      }

      const data = JSON.stringify(seedElement);
      const createStr = `await db.${model.name}.insert(${data});`;
      seeds.push(createStr);
    }
    return seeds.join('\n');
  };

  this.getFields = function (model, template) {
    const fields = model.field;
    let name_list = '';
    let label_list = '';
    let intersection = '';
    let field_list = '\n';
    let validation_rule_list = '';
    let validation_edit_rule_list = '';
    let migration_list = [];

    // insert status field if not exist
    // isStatusFieldExist = fields
    //   .map((field) => {
    //     return field[0] === 'status';
    //   })
    //   .includes(true);

    // if (!isStatusFieldExist) {
    //   fields.push(['status', 'integer', [], 'xyzStatus', 'required|integer', 'required|integer']);
    // }

    for (let i = 0; i < fields.length; i++) {
      const field = fields[i];
      if (field.length == 6) {
        let name = field[0];
        let type = field[1];
        const validation = field[2][0];

        if (type == 'password') {
          type = 'string';
        }
        if (type == 'time') {
          type = 'TIME';
        }

        if (type.indexOf('image') == 0) {
          type = 'text';
        }

        if (type.indexOf('file') == 0) {
          type = 'text';
        }

        let fieldType = type;

        if (fieldType == 'date') {
          fieldType = 'DATEONLY';
        }
        if (fieldType == 'datetime') {
          fieldType = 'DATE';
        }

        if (name != 'id') {
          if (validation) {
            let field = '';
            field += `type: DataTypes.${fieldType.toUpperCase()},`;

            const { constraints, validations } = Validation(validation);

            if (Object.keys(constraints)) {
              Object.keys(constraints).map((constraint) => {
                if (typeof constraints[constraint] === 'string') {
                  field += `${constraint}: "${constraints[constraint]}",`;
                } else {
                  field += `${constraint}: ${constraints[constraint]},`;
                }
              });
            }

            if (Object.keys(validations)) {
              let field_validation = '';
              Object.keys(validations).map((validation) => {
                field_validation += `${validation}: ${validations[validation]},`;
              });

              field += `validate: {${field_validation}}`;
            }

            field_list += `\t\t\t${name}: {${field}},\n`;
          } else {
            field_list += `\t\t\t${name}: DataTypes.${fieldType.toUpperCase()},\n`;
          }
        } else {
          field_list += `
            id: {
              type: DataTypes.INTEGER,
              primaryKey: true,
              autoIncrement: true,
            },
          `;
        }

        let condition = field[2].length > 0 ? field[2][0] : [];
        let label = field[3];
        let validation_rule = field[4];
        let validation_edit_rule = field[5];

        name_list = name_list + `'${name}',`;

        intersection = intersection + `'${field[0]}',`;

        label_list = label_list + `'${label}',`;

        validation_rule_list = validation_rule_list + `\t['${name}', '${label}', '${validation_rule}'],\n\t\t`;
        validation_edit_rule_list = validation_edit_rule_list + `\t['${name}', '${label}', '${validation_edit_rule}'],\n\t\t`;
        migration_list.push([name, type]);
      }
    }

    intersection = intersection + `'created_at',`;
    intersection = intersection + `'updated_at',`;

    const join_name_list = [];
    if (model.join.length > 0) {
      model.join.forEach((item) => {
        if (!name_list.includes(item.field)) {
          join_name_list.push(item.field);
        }
      });
    }
    const allowFields = (join_name_list.length > 0 ? join_name_list.map((item) => `"${item}"`).join(',') + ',' : '') + name_list;
    template = this.inject_substitute(template, 'allowed_fields', allowFields);
    template = this.inject_substitute(template, 'intersection_fields', intersection);
    template = this.inject_substitute(template, 'labels', label_list);
    template = this.inject_substitute(template, 'validation_rules', validation_rule_list);
    template = this.inject_substitute(template, 'validation_edit_rules', validation_edit_rule_list);
    template = this.inject_substitute(template, 'fields', field_list);

    return template;
  };

  // this.getMapping = function (mapping, model_name) {
  //   let result = '';

  //   for (const key in mapping) {
  //     if (mapping.hasOwnProperty(key)) {
  //       const element = mapping[key];
  //       result += `\t${model_name}.${key}_mapping = function () {\n\t\treturn {\n`;

  //       if (Object.keys(element).length > 0) {
  //         for (const key2 in element) {
  //           if (element.hasOwnProperty(key2)) {
  //             const element2 = element[key2];
  //             result += `\t\t\t'${key2}': '${element2}',\n`;
  //           }
  //         }
  //         result += `\t\t};\n\t}\n`;
  //       } else {
  //         result += '\t\t};\n\t\t};\n';
  //       }
  //     }
  //   }

  //   return result;
  // };
  this.getJoinFields = function (joinArray) {
    let associate = '';
    if (joinArray.length > 0) {
      //it is object of objects
      //rotate over every object and find name of table and field join
      joinArray.forEach((joinObject) => {
        let joinTable = joinObject.name;
        let joinField = joinObject.field;
        if (joinTable && joinField) {
          let joinNickname = joinField.replace('_id', '');
          associate += `
          {{{uc_name}}}.belongsTo(models.${joinTable}, {
            foreignKey: "${joinField}",
            as: "${joinNickname}",
            constraints: false,
            });\n 
          `;
        }
      });
    }
    return associate;
  };
  this.getMapping = function (mapping, model_name) {
    let code = [];

    // if (!mapping['status']) {
    //   mapping['status'] = { 0: 'xyzInactive', 1: 'xyzActive', 2: 'xyzSuspend' };
    // }

    for (const key in mapping) {
      if (Object.hasOwnProperty.call(mapping, key)) {
        const element = mapping[key];

        code.push(`
        ${model_name}.${key}_mapping = function (${key}) {
          const mapping = ${JSON.stringify(element)}
        
          if (arguments.length === 0) return mapping;
          else return mapping[${key}];
        };
        `);
      }
    }

    return code.join('\n');
  };

  this.generatePaginateTemplate = (ucModelName, association) => {
    return `\t\n${ucModelName}.get_${association.name}_paginated = function(db, ...rest) {
        return ${ucModelName}.getPaginated(...rest, [{
          model: db.${association.name},
          as: "${association.as ? association.as : association.name}",          
        }])
      }\t\n`;
  };

  this.generate_join_row = (table, joinTable) => {
    const ucModelName = this.ucFirst(table);
    return `${this.ucFirst(table)}.get_${table}_${joinTable} = (id, db) => {
      return ${ucModelName}.findByPk(id, { include: [{ model: db.${joinTable}, as: "${joinTable}" }] });
    };`;
  };
};
