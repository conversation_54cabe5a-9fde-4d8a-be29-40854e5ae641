const aws = require('aws-sdk');
const multer = require('multer');
const multerS3 = require('multer-s3');

module.exports = {
  /**
   * S3 file uploader
   * @param {string} meta S3 file meta data eg: { user_id: some_radom_user_id }
   * @param {string} filename uploading filename
   * @param {('image'|'file')} filetype uploading filetype
   * @example
   * app.post('/upload', upload.array('photos', 3), function(req, res, next) {
   * res.send('Successfully uploaded ' + req.files.length + ' files!')
   * )
   */
  s3_upload: function (meta, filename, filetype) {
    try {
      aws.config.update({
        accessKeyId: process.env.DYNAMIC_CONFIG_AWS_SECRET,
        secretAccessKey: process.env.DYNAMIC_CONFIG_AWS_KEY,
        region: process.env.DYNAMIC_CONFIG_AWS_REGION,
      });

      const s3 = new aws.S3();

      const fileFilter = (req, file, cb) => {
        if (filetype === 'image') {
          if (
            file.mimetype === 'image/png' ||
            file.mimetype === 'image/jpg' ||
            file.mimetype === 'image/jpeg'
          ) {
            cb(null, true);
          } else {
            cb(new Error('File format should be PNG,JPG,JPEG'), false);
          }
        } else {
          cb(null, true);
        }
      };

      const upload = multer({
        fileFilter,
        storage: multerS3({
          s3: s3,
          bucket: process.env.DYNAMIC_CONFIG_AWS_BUCKET,
          metadata: function (_, _, cb) {
            cb(null, meta);
          },
          key: function (_, _, cb) {
            cb(null, filename);
          },
        }),
      });

      return upload;
    } catch (error) {
      console.log('s3_upload => ', error);
    }
  },
  /**
   * Local file uploader
   * @param {String} location path to store files eg: '/uploads/images'
   * @param {string} filename uploading filename
   * @example
   * app.post('/profile', upload.single('avatar'), function (req, res, next) {
   *  // req.file is the `avatar` file
   *  // req.body will hold the text fields, if there were any
   * });
   */
  local_upload: function (location, filename) {
    try {
      const storage = multer.diskStorage({
        destination: function (req, file, cb) {
          cb(null, location);
        },
        filename: function (req, file, cb) {
          cb(null, filename);
        },
      });

      const upload = multer({ storage: storage });

      return upload;
    } catch (error) {
      console.log('local_upload => ', error);
    }
  },
};
