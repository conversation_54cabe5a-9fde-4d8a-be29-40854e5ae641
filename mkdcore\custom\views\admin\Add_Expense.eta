<% if(it.layout_clean_mode) {%> <% layout("../layouts/admin/Clean") %> <% } else {%> <% layout("../layouts/admin/Main") %> <%}%> <%~ includeFile("../partials/admin/Breadcrumb",
it)%>

<div class="tab-content mx-4 my-4">
  <div class="row">
    <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
      <%~ includeFile("../partials/admin/GlobalResponse.eta", it) %>
      <div class="form-container card p-4">
        <h5 class="primaryHeading2 mb-4 text-md-left pl-3"><%= it.heading %></h5>

        <form action="/admin/expenses-add" method="POST" enctype="multipart/form-data">
          <div class="form-group required col-md-5 col-sm-12">
            <label for="cohort_id" class="control-label">Co-Hort</label>

            <input
              id="integer_cohort_id"
              required
              name="cohort_id"
              value="<%= it.form_fields['cohort_id'] %>"
              class="form-control"
              type="number"
              step="1"
              pattern="d+"
              inputmode="numeric"
            />
          </div>

          <div class="form-group required col-md-5 col-sm-12">
            <label for="expense_name" class="control-label">Co-Expense Name</label>

            <input type="text" required class="form-control data-input" id="text_expense_name" name="expense_name" value="<%= it.form_fields['expense_name'] %>" />
          </div>

          <div class="form-group required col-md-5 col-sm-12">
            <label for="date" class="control-label">Date</label>

            <input id="date_date" required name="date" type="date" class="form-control" value="<%= it.form_fields['date'] %>" />
          </div>

          <div class="form-group required col-md-5 col-sm-12">
            <label for="amount" class="control-label">Amount</label>

            <input type="text" required class="form-control data-input" id="text_amount" name="amount" value="<%= it.form_fields['amount'] %>" />
          </div>

          <div class="form-group required col-md-5 col-sm-12">
            <label for="paid_by" class="control-label">Paid By</label>

            <input type="text" required class="form-control data-input" id="text_paid_by" name="paid_by" value="<%= it.form_fields['paid_by'] %>" />
          </div>

          <div class="form-group required col-md-5 col-sm-12">
            <label for="parent_1_split" class="control-label">Parent 1 split</label>

            <input
              id="integer_parent_1_split"
              required
              name="parent_1_split"
              value="<%= it.form_fields['parent_1_split'] %>"
              class="form-control"
              type="number"
              step="1"
              pattern="d+"
              inputmode="numeric"
            />
          </div>

          <div class="form-group required col-md-5 col-sm-12">
            <label for="parent_2_split" class="control-label">Parent 2 split</label>

            <input
              id="integer_parent_2_split"
              required
              name="parent_2_split"
              value="<%= it.form_fields['parent_2_split'] %>"
              class="form-control"
              type="number"
              step="1"
              pattern="d+"
              inputmode="numeric"
            />
          </div>

          <div class="form-group required col-md-5 col-sm-12">
            <label for="status" class="control-label">Status</label>

            <select class="custom-select" id="select_mapping_status" name="status" required>
              <% Object.keys(it.status_mapping()).forEach(function(prop) { %> <% const same = it.form_fields["status"] == prop ? 'selected' : false %>
              <option class="select_mapping_status_option<%= same ? '_' + same : '' %>" value="<%= prop %>" <%="same" %>> <%= it.status_mapping()[prop] %></option>
              <% }) %>
            </select>
          </div>

          <div class="form-group required col-md-5 col-sm-12">
            <label for="receipt" class="control-label">Receipt</label>

            <input id="file_receipt" required name="receipt" type="file" class="form-control-file" />
          </div>

          <div class="form-group col-md-5 col-sm-12">
            <label for="settlement" class="control-label">Settlement</label>

            <input
              id="integer_settlement"
              name="settlement"
              value="<%= it.form_fields['settlement'] %>"
              class="form-control"
              type="number"
              step="1"
              pattern="d+"
              inputmode="numeric"
            />
          </div>

          <div class="form-group required col-md-5 col-sm-12">
            <label for="children" class="control-label">Name of Children</label>

            <input type="text" required class="form-control data-input" id="text_children" name="children" value="<%= it.form_fields['children'] %>" />
          </div>

          <div class="form-group required col-md-5 col-sm-12">
            <label for="notes" class="control-label">Notes</label>

            <input type="text" required class="form-control data-input" id="text_notes" name="notes" value="<%= it.form_fields['notes'] %>" />
          </div>

          <div class="form-group pl-3">
            <button type="submit" class="btn btn-primary">Submit</button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
