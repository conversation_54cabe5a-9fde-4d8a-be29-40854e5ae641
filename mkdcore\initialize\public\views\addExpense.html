<!DOCTYPE html>
<html>
  <head>
    <!-- Required meta tags-->
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no, viewport-fit=cover" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <!-- Color theme for statusbar (Android only) -->
    <meta name="theme-color" content="#2196f3" />
    <!-- Your app title -->
    <title>Co-Parent</title>
  </head>

  <body>
    <!-- App root element -->
    <div id="app">
      <div class="page" data-name="addExpense">
        <!-- Top Navbar -->
        <div class="navbar">
          <div class="navbar-bg"></div>
          <div class="navbar-inner">
            <div class="left">
              <a class="link back">
                <i class="icon icon-back color-black"></i>
                <span class="if-not-md">Back</span>
              </a>
            </div>
            <div class="title text-align-center">Add Co-Expense</div>
            <div class="right"></div>
          </div>
        </div>

        <!-- Scrollable page content -->
        <div class="page-content no-swipe-panel">
          <div class="card">
            <div class="card-content padding-vertical">
              <div class="list no-hairlines">
                <form action="" id="AddExpensesForm">
                  <ul>
                    <li class="item-content item-input item-input-outline">
                      <div class="item-inner">
                        <div class="item-title item-label">Co-Expense Name</div>
                        <div class="item-input-wrap">
                          <input type="text" name="expense_name" required validate validate-on-blur="true" id="addexpense-name" />
                          <span class="input-clear-button"></span>
                        </div>
                      </div>
                    </li>

                    <li>
                      <div class="item-content item-input">
                        <div class="item-inner">
                          <div class="item-title item-label custom-date-label">Co-Expense Date</div>
                          <div class="item-input-wrap addexpense-date">
                            <input type="text" placeholder="Select Date" readonly="readonly" id="addexpense-date" required validate validate-on-blur="true" />
                          </div>
                        </div>
                      </div>
                    </li>

                    <li class="smart-select-list">
                      <div class="item-title item-label smart-select-label">Choose Co-Hort</div>
                      <a class="item-link smart-select smart-select-init" data-open-in="sheet" data-close-on-select="true">
                        <select onchange="populateInputFields()" placeholder="Please choose..." id="addexpenses-cohortselector"></select>
                        <div class="item-content expensesChoseCohortSelect addexpensesChoseCohortSelect">
                          <div class="item-inner">
                            <div class="item-title">Choose Co-Hort</div>
                          </div>
                        </div>
                      </a>
                    </li>

                    <li class="smart-select-list">
                      <div class="item-title item-label smart-select-label">Co-Children</div>
                      <a class="item-link smart-select smart-select-init" data-open-in="sheet" data-close-on-select="true">
                        <select placeholder="Please choose..." id="addexpenses-cohortchildren"></select>
                        <div class="item-content expensesChoseCohortSelect addexpensesSelectChildSelect">
                          <div class="item-inner">
                            <div class="item-title">Select Co-Child</div>
                          </div>
                        </div>
                      </a>
                    </li>

                    <li class="item-content item-input item-input-outline">
                      <div class="item-inner">
                        <div class="item-title item-label">Amount</div>
                        <div class="item-input-wrap">
                          <input type="number" name="amount" required validate validate-on-blur="true" id="addExpense-amount" step="0.01" />
                          <span class="input-clear-button"></span>
                        </div>
                      </div>
                    </li>

                    <li class="smart-select-list">
                      <div class="item-title item-label smart-select-label">Paid By</div>
                      <a class="item-link smart-select smart-select-init" data-open-in="sheet" data-close-on-select="true">
                        <select placeholder="Please choose..." id="addexpenses-parentselector"></select>
                        <div class="item-content expensesChoseCohortSelect addexpensesPaidBySelect">
                          <div class="item-inner">
                            <div class="item-title">Paid By</div>
                          </div>
                        </div>
                      </a>
                    </li>

                    <li class="item-content item-input item-input-outline">
                      <div class="item-inner">
                        <div class="item-title item-label" id="addExpensesParentOneSplit">Parent 1 Split</div>
                        <div class="item-input-wrap">
                          <input type="number" name="parent_1_split" required validate validate-on-blur="true" id="addExpense-parent1-split" />
                          <span class="input-clear-button"></span>
                        </div>
                      </div>
                    </li>

                    <li class="item-content item-input item-input-outline">
                      <div class="item-inner">
                        <div class="item-title item-label" id="addExpensesParentTwoSplit">Parent 2 Split</div>
                        <div class="item-input-wrap">
                          <input type="number" name="parent_2_split" required validate validate-on-blur="true" id="addExpense-parent2-split" />
                          <span class="input-clear-button"></span>
                        </div>
                      </div>
                    </li>

                    <li class="item-content item-input item-input-outline">
                      <div class="item-inner">
                        <div class="item-title item-label">Notes</div>
                        <div class="item-input-wrap">
                          <textarea name="notes" required validate validate-on-blur="true" id="addExpense-notes"></textarea>
                        </div>
                      </div>
                    </li>

                    <li class="item-content item-input item-input-outline">
                      <div class="item-inner">
                        <div class="item-title item-label">Upload Receipt</div>
                        <div class="item-input-wrap addExpensesUpload">
                          <input id="upload-reciepts" type="file" name="reciepts" />
                        </div>
                      </div>
                    </li>

                    <a onclick="addExpenses()">
                      <button class="button button-raised button-fill margin-vertical customButton">Add Co-Expense</button>
                    </a>
                  </ul>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Path to Framework7 Library Bundle JS-->
    <script type=" text/javascript" src="../js/framework7-bundle.min.js"></script>
    <!-- Path to your app js-->
    <script type="text/javascript" src="../main_app.js"></script>
    <!--Custome CSS Move to File Later-->
  </body>
</html>
