const { request, response } = require('express');

const authService = require('../services/AuthService');
const jwtService = require('../services/JwtService');

module.exports = {
  /**
   * Register router
   * @param {request} req
   * @param {response} res
   */
  get: async function (req, res) {
    try {
      const payload = await authService.register(
        req.body.email,
        req.body.password,
      );

      const response = {
        access_token: jwtService.createAccessToken(payload),
        refresh_token: jwtService.createRefreshToken(payload),
      };

      res.status(201).json({ success: true, payload: response });
    } catch (error) {
      res.status(500).json({ success: false, message: error.message });
    }
  },
};
