const login = require('./login');
const register = require('./register');
const forgotPassword = require('./forgotPassword');
const verifyForgotPassword = require('./verifyForgotPassword');
const resetPassword = require('./resetPassword');

const withInputValidation = require('../tools/validation/withInputValidation');

// const google = require('./google');
// const facebook = require('./facebook');

module.exports = {
  initializeApi: function (app) {
    app.get('/auth', async function (req, res, next) {
      res.json({
        message: 'API Server',
      });
    });

    app.post(
      '/login',
      withInputValidation.validate({
        email: 'required|email',
        password: 'required',
      }),
      async function (req, res) {
        res.send(await login.get(req, res));
      },
    );

    app.post(
      '/register',
      withInputValidation.validate({
        email: 'required|email',
        password: 'required',
      }),
      async function (req, res) {
        res.send(await register.get(req, res));
      },
    );

    app.post(
      '/password/forgot',
      withInputValidation.validate({ email: 'required|email' }),
      async function (req, res) {
        res.send(await forgotPassword.get(req, res));
      },
    );

    app.post(
      '/password/verify',
      withInputValidation.validate({ code: 'required' }),
      async function (req, res) {
        res.send(await verifyForgotPassword.get(req, res));
      },
    );

    app.post(
      '/password/reset',
      withInputValidation.validate({ password: 'required' }),
      async function (req, res) {
        res.send(await resetPassword.get(req, res));
      },
    );

    // // Google authentication
    // app.get('/auth/google', async function (req, res, next) {
    //   res.send(await google.initialize(req, res));
    // });

    // app.post('/auth/google/login', async function (req, res, next) {
    //   res.send(await google.login(req, res));
    // });

    // app.post('/auth/google/register', async function (req, res, next) {
    //   res.send(await google.register(req, res));
    // });

    // // Facebook authentication
    // app.get('/auth/facebook', async function (req, res, next) {
    //   res.send(await facebook.initialize(req, res));
    // });

    // app.post('/auth/facebook/login', async function (req, res, next) {
    //   res.send(await facebook.login(req, res));
    // });

    // app.post('/auth/facebook/register', async function (req, res, next) {
    //   res.send(await facebook.register(req, res));
    // });

    return app;
  },
};
