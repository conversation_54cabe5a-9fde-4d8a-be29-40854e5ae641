<!DOCTYPE html>
<html>

<head>
    <!-- Required meta tags-->
    <meta charset="utf-8">
    <meta name="viewport"
        content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no, viewport-fit=cover">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <!-- Color theme for statusbar (Android only) -->
    <meta name="theme-color" content="#2196f3">
    <!-- Your app title -->
    <title>Co-Parent</title>

</head>

<body>
    <!-- App root element -->
    <div id="app">
        <div class="page" data-name="sharedPhone">

            <!-- Top Navbar -->
            <div class="navbar">
                <div class="navbar-bg"></div>
                <div class="navbar-inner">
                    <div class="left">

                    </div>
                    <div class="title text-align-center">
                        Select Co-Phone
                    </div>
                    <div class="right">

                    </div>
                </div>
            </div>

            <!-- Scrollable page content -->
            <div class="page-content no-swipe-panel">
                <div class="list no-hairlines">
                    <ul>
                        <li class="item-content item-input item-input-outline">
                            <div class="item-inner">
                                <div class="item-title item-label">Area Code</div>
                                <div class="item-input-wrap">
                                    <input maxlength="3" placeholder="Enter The Area Code" type="text" name="area-code"
                                        id="sharedphoneareacode" required validate pattern="\d*" />
                                    <span class="input-clear-button"></span>
                                </div>
                            </div>
                        </li>

                        <li>
                            <button class="search-button button button-raised button-fill margin-vertical"
                                onclick="searchbyAreaCode()">
                                Search
                            </button>
                        </li>
                    </ul>

                    <div class="number-container" id="number-container">
                        <!--It is skeleton, Does not render, Renders Data through JS-->
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>

</html>