<% if(it.layout_clean_mode) {%> <% layout("../layouts/admin/Clean") %> <% } else {%> <% layout("../layouts/admin/Main") %> <%}%> <%~ includeFile("../partials/admin/Breadcrumb",
it)%>

<div class="tab-content mx-4 my-4">
  <div class="row">
    <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
      <%~ includeFile("../partials/admin/GlobalResponse.eta", it) %>
      <div class="form-container card p-4">
        <h5 class="primaryHeading2 mb-4 text-md-left pl-3"><%= it.heading %></h5>

        <form action="/admin/files-add" method="POST" enctype="multipart/form-data">
          <div class="form-group required col-md-5 col-sm-12">
            <label for="upload_by" class="control-label">Upload By</label>

            <input
              id="integer_upload_by"
              required
              name="upload_by"
              value="<%= it.form_fields['upload_by'] %>"
              class="form-control"
              type="number"
              step="1"
              pattern="d+"
              inputmode="numeric"
            />
          </div>

          <div class="form-group required col-md-5 col-sm-12">
            <label for="cohort_id" class="control-label">Co-Hort</label>

            <input
              id="integer_cohort_id"
              required
              name="cohort_id"
              value="<%= it.form_fields['cohort_id'] %>"
              class="form-control"
              type="number"
              step="1"
              pattern="d+"
              inputmode="numeric"
            />
          </div>
          <div class="form-group required col-md-5 col-sm-12">
            <label for="label" class="control-label">Label</label>
            <input id="label" required name="label" type="text" class="form-control" />
          </div>
          <div class="form-group required col-md-5 col-sm-12">
            <label for="file_url" class="control-label">File Url</label>

            <input id="file_file_url" required name="file_url" type="file" class="form-control-file" />
          </div>

          <div class="form-group pl-3">
            <button type="submit" class="btn btn-primary">Submit</button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
