'use strict';

const app = require('express').Router();
const Sequelize = require('sequelize');
const logger = require('../../services/LoggingService');
let pagination = require('../../services/PaginationService');
let SessionService = require('../../services/SessionService');
let JwtService = require('../../services/JwtService');
const ValidationService = require('../../services/ValidationService');
const PermissionService = require('../../services/PermissionService');
const UploadService = require('../../services/UploadService');
const AuthService = require('../../services/AuthService');
const db = require('../../models');
const helpers = require('../../core/helpers');
const upload = UploadService.upload('expenses/file');

const role = 1;

app.get('/admin/expenses/:num', SessionService.verifySessionMiddleware(role, 'admin'), async function (req, res, next) {
  try {
    let session = req.session;
    let paginateListViewModel = require('../../view_models/expense_admin_list_paginate_view_model');

    var viewModel = new paginateListViewModel(db.expense, 'Co-Expenses', session.success, session.error, '/admin/expenses');

    const format = req.query.format ? req.query.format : 'view';
    const direction = req.query.direction ? req.query.direction : 'ASC';
    const per_page = req.query.per_page ? req.query.per_page : 10;
    let order_by = req.query.order_by ? req.query.order_by : viewModel.get_field_column()[0];
    let orderAssociations = [];
    viewModel.set_order_by(order_by);
    let joins = order_by.includes('.') ? order_by.split('.') : [];
    order_by = order_by.includes('.') ? joins[joins.length - 1] : order_by;
    if (joins.length > 0) {
      for (let i = joins.length - 1; i > 0; i--) {
        orderAssociations.push(`${joins[i - 1]}`);
      }
    }
    // Check for flash messages
    const flashMessageSuccess = req.flash('success');
    if (flashMessageSuccess && flashMessageSuccess.length > 0) {
      viewModel.success = flashMessageSuccess[0];
    }
    const flashMessageError = req.flash('error');
    if (flashMessageError && flashMessageError.length > 0) {
      viewModel.error = flashMessageError[0];
    }

    viewModel.set_id(req.query.id ? req.query.id : '');
    viewModel.set_cohort_id(req.query.cohort_id ? req.query.cohort_id : '');
    viewModel.set_date(req.query.date ? req.query.date : '');
    viewModel.set_expense_name(req.query.expense_name ? req.query.expense_name : '');
    viewModel.set_paid_by(req.query.paid_by ? req.query.paid_by : '');
    viewModel.set_amount(req.query.amount ? req.query.amount : '');
    viewModel.set_children(req.query.children ? req.query.children : '');
    viewModel.set_status(req.query.status ? req.query.status : '');

    let where = helpers.filterEmptyFields({
      id: viewModel.get_id(),
      cohort_id: viewModel.get_cohort_id(),
      date: viewModel.get_date(),
      expense_name: viewModel.get_expense_name(),
      paid_by: viewModel.get_paid_by(),
      amount: viewModel.get_amount(),
      children: viewModel.get_children(),
      status: viewModel.get_status(),
    });

    const count = await db.expense._count(where, []);

    let sort_url = '';

    if (req.originalUrl.includes('?')) {
      if (req.originalUrl.includes('order_by')) {
        let url_query = req.originalUrl.split('?')[1];
        sort_url = `${url_query.split('order_by')[0]}`;
      } else {
        sort_url = `${req.originalUrl.split('?')[1]}`;
      }
    }
    viewModel.set_total_rows(count);
    viewModel.set_per_page(+per_page);
    viewModel.set_page(+req.params.num);
    viewModel.set_query(req.query);
    viewModel.set_sort_base_url(`/admin/expenses/${+req.params.num}?${sort_url}`);
    viewModel.set_sort(direction);

    const list = await db.expense.getPaginated(viewModel.get_page() - 1 < 0 ? 0 : viewModel.get_page(), viewModel.get_per_page(), where, order_by, direction, orderAssociations);

    viewModel.set_list(list);

    if (format == 'csv') {
      const csv = viewModel.to_csv();
      return res
        .set({
          'Content-Type': 'text/csv',
          'Content-Disposition': 'attachment; filename="export.csv"',
        })
        .send(csv);
    }

    // if (format != 'view') {
    //   res.json(viewModel.to_json());
    // } else {
    // }

    return res.render('admin/Expense', viewModel);
  } catch (error) {
    console.error(error);
    viewModel.error = error.message || 'Something went wrong';
    return res.render('admin/Expense', viewModel);
  }
});

app.get('/admin/expenses-add', SessionService.verifySessionMiddleware(role, 'admin'), async function (req, res, next) {
  if (req.session.csrf === undefined) {
    req.session.csrf = SessionService.randomString(100);
  }

  const expenseAdminAddViewModel = require('../../view_models/expense_admin_add_view_model');

  const viewModel = new expenseAdminAddViewModel(db.expense, 'Add Co-Expense', '', '', '/admin/expenses');
  viewModel.heading = 'Add Co-Expense';
  res.render('admin/Add_Expense', viewModel);
});

app.post(
  '/admin/expenses-add',
  SessionService.verifySessionMiddleware(role, 'admin'),
  upload.single('receipt'),
  ValidationService.validateInput(
    {
      cohort_id: 'required',
      expense_name: 'required',
      date: 'required',
      amount: 'required',
      paid_by: 'required',
      parent_1_split: 'required',
      parent_2_split: 'required',
      status: 'required',
      children: 'required',
      notes: 'required',
    },
    {
      'cohort_id.required': 'CohortId is required',
      'expense_name.required': 'ExpenseName is required',
      'date.required': 'Date is required',
      'amount.required': 'Amount is required',
      'paid_by.required': 'PaidBy is required',
      'parent_1_split.required': 'Parent1Split is required',
      'parent_2_split.required': 'Parent2Split is required',
      'status.required': 'Status is required',
      'children.required': 'Children is required',
      'notes.required': 'Notes is required',
    },
  ),
  async function (req, res, next) {
    if (req.session.csrf === undefined) {
      req.session.csrf = SessionService.randomString(100);
    }
    const expenseAdminAddViewModel = require('../../view_models/expense_admin_add_view_model');

    const viewModel = new expenseAdminAddViewModel(db.expense, 'Add expense', '', '', '/admin/expenses');

    // TODO use separate controller for image upload
    //  {{{upload_field_setter}}}

    let { cohort_id, expense_name, date, amount, paid_by, parent_1_split, parent_2_split, status, receipt, settlement, children, notes } = req.body;
    if (req.file) receipt = req.file.filename;
    viewModel.form_fields = {
      ...viewModel.form_fields,
      cohort_id,
      expense_name,
      date,
      amount,
      paid_by,
      parent_1_split,
      parent_2_split,
      status,
      receipt,
      settlement,
      children,
      notes,
    };

    try {
      if (req.validationError) {
        viewModel.error = req.validationError;
        return res.render('admin/Add_Expense', viewModel);
      }

      viewModel.session = req.session;

      const data = await db.expense.insert({ cohort_id, expense_name, date, amount, paid_by, parent_1_split, parent_2_split, status, receipt, settlement, children, notes });

      if (!data) {
        viewModel.error = 'Something went wrong';
        return res.render('admin/Add_Expense', viewModel);
      }

      await db.activity_log.insert({
        action: 'ADD',
        name: 'Admin_expense_controller.js',
        portal: 'admin',
        data: JSON.stringify({ cohort_id, expense_name, date, amount, paid_by, parent_1_split, parent_2_split, status, receipt, settlement, children, notes }),
      });

      req.flash('success', 'Expense created successfully');
      return res.redirect('/admin/expenses/0');
    } catch (error) {
      console.error(error);
      viewModel.error = error.message || 'Something went wrong';
      return res.render('admin/Add_Expense', viewModel);
    }
  },
);

app.get('/admin/expenses-edit/:id', SessionService.verifySessionMiddleware(role, 'admin'), async function (req, res, next) {
  let id = req.params.id;
  if (req.session.csrf === undefined) {
    req.session.csrf = SessionService.randomString(100);
  }
  const expenseAdminEditViewModel = require('../../view_models/expense_admin_edit_view_model');

  const viewModel = new expenseAdminEditViewModel(db.expense, 'Edit Co-Expense', '', '', '/admin/expenses');
  viewModel.heading = 'Edit Co-Expense';

  try {
    const exists = await db.expense.getByPK(id);

    if (!exists) {
      req.flash('error', 'Expense not found');
      return res.redirect('/admin/expenses/0');
    }
    const values = exists;
    Object.keys(viewModel.form_fields).forEach((field) => {
      viewModel.form_fields[field] = values[field] || '';
    });

    return res.render('admin/Edit_Expense', viewModel);
  } catch (error) {
    console.error(error);
    viewModel.error = error.message || 'Something went wrong';
    return res.render('admin/Edit_Expense', viewModel);
  }
});

app.post(
  '/admin/expenses-edit/:id',
  SessionService.verifySessionMiddleware(role, 'admin'),
  upload.single('receipt'),
  ValidationService.validateInput(
    {
      expense_name: 'required',
      date: 'required',
      amount: 'required',
      notes: 'required',
      paid_by: 'required',
      parent_1_split: 'required',
      parent_2_split: 'required',
      status: 'required',
      children: 'required',
    },
    {
      'expense_name.required': 'ExpenseName is required',
      'date.required': 'Date is required',
      'amount.required': 'Amount is required',
      'notes.required': 'Notes is required',
      'paid_by.required': 'PaidBy is required',
      'parent_1_split.required': 'Parent1Split is required',
      'parent_2_split.required': 'Parent2Split is required',
      'status.required': 'Status is required',
      'children.required': 'Children is required',
    },
  ),
  async function (req, res, next) {
    let id = req.params.id;
    if (req.session.csrf === undefined) {
      req.session.csrf = SessionService.randomString(100);
    }

    const expenseAdminEditViewModel = require('../../view_models/expense_admin_edit_view_model');

    const viewModel = new expenseAdminEditViewModel(db.expense, 'Edit expense', '', '', '/admin/expenses');

    let { expense_name, date, amount, notes, paid_by, parent_1_split, parent_2_split, status, receipt, settlement, children } = req.body;
    if (req.file) receipt = req.file.filename;
    viewModel.form_fields = {
      ...viewModel.form_fields,
      expense_name,
      date,
      amount,
      notes,
      paid_by,
      parent_1_split,
      parent_2_split,
      status,
      receipt,
      settlement,
      children,
    };

    delete viewModel.form_fields.id;

    try {
      if (req.validationError) {
        viewModel.error = req.validationError;
        return res.render('admin/Edit_Expense', viewModel);
      }

      const resourceExists = await db.expense.getByPK(id);
      if (!resourceExists) {
        req.flash('error', 'Expense not found');
        return res.redirect('/admin/expenses/0');
      }

      viewModel.session = req.session;

      let data = await db.expense.edit({ expense_name, date, amount, notes, paid_by, parent_1_split, parent_2_split, status, receipt, settlement, children }, id);
      if (!data) {
        viewModel.error = 'Something went wrong';
        return res.render('admin/Edit_Expense', viewModel);
      }

      await db.activity_log.insert({
        action: 'EDIT',
        name: 'Admin_expense_controller.js',
        portal: 'admin',
        data: JSON.stringify({ expense_name, date, amount, notes, paid_by, parent_1_split, parent_2_split, status, receipt, settlement, children }),
      });

      req.flash('success', 'Expense edited successfully');

      return res.redirect('/admin/expenses/0');
    } catch (error) {
      console.error(error);
      viewModel.error = error.message || 'Something went wrong';
      return res.render('admin/Edit_Expense', viewModel);
    }
  },
);

app.get(
  '/admin/expenses-view/:id',
  SessionService.verifySessionMiddleware(role, 'admin'),

  async function (req, res, next) {
    try {
      let id = req.params.id;

      const expenseAdminDetailViewModel = require('../../view_models/expense_admin_detail_view_model');

      var viewModel = new expenseAdminDetailViewModel(db.expense, 'Co-Expense details', '', '', '/admin/expenses');
      viewModel.heading = 'Co-Expense details';

      const data = await db.expense.getByPK(id);
      data.status = db.expense.status_mapping()[data.status];

      if (!data) {
        viewModel.error = 'Expense not found';
        viewModel.detail_fields = {
          ...viewModel.detail_fields,
          id: 'N/A',
          cohort_id: 'N/A',
          expense_name: 'N/A',
          date: 'N/A',
          amount: 'N/A',
          paid_by: 'N/A',
          parent_1_split: 'N/A',
          parent_2_split: 'N/A',
          status: 'N/A',
          receipt: 'N/A',
          settlement: 'N/A',
          children: 'N/A',
          notes: 'N/A',
        };
      } else {
        viewModel.detail_fields = {
          ...viewModel.detail_fields,
          id: data['id'] || 'N/A',
          cohort_id: data['cohort_id'] || 'N/A',
          expense_name: data['expense_name'] || 'N/A',
          date: data['date'] || 'N/A',
          amount: data['amount'] || 'N/A',
          paid_by: data['paid_by'] || 'N/A',
          parent_1_split: data['parent_1_split'] || 'N/A',
          parent_2_split: data['parent_2_split'] || 'N/A',
          status: data['status'] || 'N/A',
          receipt: data['receipt'] || 'N/A',
          settlement: data['settlement'] || 'N/A',
          children: data['children'] || 'N/A',
          notes: data['notes'] || 'N/A',
        };
      }

      res.render('admin/View_Expense', viewModel);
    } catch (error) {
      console.error(error);
      viewModel.error = error.message || 'Something went wrong';
      viewModel.detail_fields = {
        ...viewModel.detail_fields,
        id: 'N/A',
        cohort_id: 'N/A',
        expense_name: 'N/A',
        date: 'N/A',
        amount: 'N/A',
        paid_by: 'N/A',
        parent_1_split: 'N/A',
        parent_2_split: 'N/A',
        status: 'N/A',
        receipt: 'N/A',
        settlement: 'N/A',
        children: 'N/A',
        notes: 'N/A',
      };
      res.render('admin/View_Expense', viewModel);
    }
  },
);

app.get('/admin/expenses-delete/:id', SessionService.verifySessionMiddleware(role, 'admin'), async function (req, res, next) {
  let id = req.params.id;

  const expenseAdminDeleteViewModel = require('../../view_models/expense_admin_delete_view_model');

  const viewModel = new expenseAdminDeleteViewModel(db.expense);

  try {
    const exists = await db.expense.getByPK(id);

    if (!exists) {
      req.flash('error', 'Expense not found');
      return res.redirect('/admin/expenses/0');
    }

    viewModel.session = req.session;

    await db.expense.realDelete(id);

    await db.activity_log.insert({
      action: 'DELETE',
      name: 'Admin_expense_controller.js',
      portal: 'admin',
      data: JSON.stringify(exists),
    });

    req.flash('success', 'Expense deleted successfully');

    return res.redirect('/admin/expenses/0');
  } catch (error) {
    console.error(error);
    req.flash('error', error.message || 'Something went wrong');
    return res.redirect('/admin/expenses/0');
  }
});

// APIS

app.get('/admin/api/expenses', JwtService.verifyTokenMiddleware(role), async function (req, res, next) {
  try {
    const user_id = req.user_id;
    const session = req.session;
    let listViewModel = require('../../view_models/expense_admin_list_paginate_view_model');
    let viewModel = new listViewModel(db.expense, 'Expenses', session.success, session.error, '/admin/expenses');
    const direction = req.query.direction ? req.query.direction : 'ASC';
    const page = req.query.page ? parseInt(req.query.page) : 1;
    const limit = req.query.limit ? parseInt(req.query.limit) : 10;
    const offset = (page - 1) * limit;
    let order_by = req.query.order_by ? req.query.order_by : viewModel.get_field_column()[0];
    let orderAssociations = [];
    viewModel.set_order_by(order_by);
    let joins = order_by.includes('.') ? order_by.split('.') : [];
    order_by = order_by.includes('.') ? joins[joins.length - 1] : order_by;
    if (joins.length > 0) {
      for (let i = joins.length - 1; i > 0; i--) {
        orderAssociations.push(`${joins[i - 1]}`);
      }
    }

    viewModel.set_id(req.query.id ? req.query.id : '');
    viewModel.set_cohort_id(req.query.cohort_id ? req.query.cohort_id : '');
    viewModel.set_date(req.query.date ? req.query.date : '');
    viewModel.set_expense_name(req.query.expense_name ? req.query.expense_name : '');
    viewModel.set_paid_by(req.query.paid_by ? req.query.paid_by : '');
    viewModel.set_amount(req.query.amount ? req.query.amount : '');
    viewModel.set_children(req.query.children ? req.query.children : '');
    viewModel.set_status(req.query.status ? req.query.status : '');

    let where = helpers.filterEmptyFields({
      id: viewModel.get_id(),
      cohort_id: viewModel.get_cohort_id(),
      date: viewModel.get_date(),
      expense_name: viewModel.get_expense_name(),
      paid_by: viewModel.get_paid_by(),
      amount: viewModel.get_amount(),
      children: viewModel.get_children(),
      status: viewModel.get_status(),
    });

    let include = [];

    const { rows: allItems, count } = await db.expense.findAndCountAll({
      where: where,
      limit: limit == 0 ? null : limit,
      offset: offset,
      include: include,
      distinct: true,
    });

    const response = {
      items: allItems,
      page,
      nextPage: count > offset + limit ? page + 1 : false,
      retrievedCount: allItems.length,
      fullCount: count,
    };

    return res.status(201).json({ success: true, data: response });
  } catch (error) {
    console.error(error);
    return res.status(500).json({ success: false, message: error.message || 'Something went wrong' });
  }
});

app.post(
  '/admin/api/expenses-add',
  JwtService.verifyTokenMiddleware(role),
  ValidationService.validateInput(
    {
      cohort_id: 'required',
      expense_name: 'required',
      date: 'required',
      amount: 'required',
      paid_by: 'required',
      parent_1_split: 'required',
      parent_2_split: 'required',
      status: 'required',
      receipt: 'required',
      children: 'required',
      notes: 'required',
    },
    {
      'cohort_id.required': 'CohortId is required',
      'expense_name.required': 'ExpenseName is required',
      'date.required': 'Date is required',
      'amount.required': 'Amount is required',
      'paid_by.required': 'PaidBy is required',
      'parent_1_split.required': 'Parent1Split is required',
      'parent_2_split.required': 'Parent2Split is required',
      'status.required': 'Status is required',
      'receipt.required': 'Receipt is required',
      'children.required': 'Children is required',
      'notes.required': 'Notes is required',
    },
  ),
  async function (req, res, next) {
    const expenseAdminAddViewModel = require('../../view_models/expense_admin_add_view_model');

    const viewModel = new expenseAdminAddViewModel(db.expense);

    const { cohort_id, expense_name, date, amount, paid_by, parent_1_split, parent_2_split, status, receipt, settlement, children, notes } = req.body;
    try {
      if (req.validationError) {
        return res.status(500).json({ success: false, message: req.validationError });
      }

      const data = await db.expense.insert({ cohort_id, expense_name, date, amount, paid_by, parent_1_split, parent_2_split, status, receipt, settlement, children, notes });

      if (!data) {
        return res.status(500).json({ success: false, message: 'Something went wrong' });
      }

      await db.activity_log.insert({
        action: 'ADD',
        name: 'Admin_expense_controller.js',
        portal: 'admin',
        data: JSON.stringify({ cohort_id, expense_name, date, amount, paid_by, parent_1_split, parent_2_split, status, receipt, settlement, children, notes }),
      });

      return res.status(201).json({ success: true, message: 'Expense created successfully' });
    } catch (error) {
      return res.status(500).json({ success: false, message: 'Something went wrong' });
    }
  },
);

app.put(
  '/admin/api/expenses-edit/:id',
  JwtService.verifyTokenMiddleware(role),
  ValidationService.validateInput(
    {
      expense_name: 'required',
      date: 'required',
      amount: 'required',
      notes: 'required',
      paid_by: 'required',
      parent_1_split: 'required',
      parent_2_split: 'required',
      status: 'required',
      receipt: 'required',
      children: 'required',
    },
    {
      'expense_name.required': 'ExpenseName is required',
      'date.required': 'Date is required',
      'amount.required': 'Amount is required',
      'notes.required': 'Notes is required',
      'paid_by.required': 'PaidBy is required',
      'parent_1_split.required': 'Parent1Split is required',
      'parent_2_split.required': 'Parent2Split is required',
      'status.required': 'Status is required',
      'receipt.required': 'Receipt is required',
      'children.required': 'Children is required',
    },
  ),
  async function (req, res, next) {
    let id = req.params.id;

    const expenseAdminEditViewModel = require('../../view_models/expense_admin_edit_view_model');

    const viewModel = new expenseAdminEditViewModel(db.expense);

    const { expense_name, date, amount, notes, paid_by, parent_1_split, parent_2_split, status, receipt, settlement, children } = req.body;

    try {
      if (req.validationError) {
        return res.status(500).json({ success: false, message: req.validationError });
      }

      const resourceExists = await db.expense.getByPK(id);
      if (!resourceExists) {
        return res.status(404).json({ success: false, message: 'Expense not found' });
      }

      const data = await db.expense.edit({ expense_name, date, amount, notes, paid_by, parent_1_split, parent_2_split, status, receipt, settlement, children }, id);

      if (!data) {
        return res.status(500).json({ success: false, message: 'Something went wrong' });
      }

      await db.activity_log.insert({
        action: 'EDIT',
        name: 'Admin_expense_controller.js',
        portal: 'admin',
        data: JSON.stringify({ expense_name, date, amount, notes, paid_by, parent_1_split, parent_2_split, status, receipt, settlement, children }),
      });

      return res.json({ success: true, message: 'Expense edited successfully' });
    } catch (error) {
      return res.status(500).json({ success: false, message: 'Something went wrong' });
    }
  },
);

app.get('/admin/api/expenses-view/:id', JwtService.verifyTokenMiddleware(role), async function (req, res, next) {
  let id = req.params.id;

  const expenseAdminDetailViewModel = require('../../view_models/expense_admin_detail_view_model');

  const viewModel = new expenseAdminDetailViewModel(db.expense);

  try {
    const data = await db.expense.getByPK(id);

    if (!data) {
      return res.status(404).json({ message: 'Expense not found', data: null });
    } else {
      const fields = {
        ...viewModel.detail_fields,
        id: data['id'] || '',
        cohort_id: data['cohort_id'] || '',
        expense_name: data['expense_name'] || '',
        date: data['date'] || '',
        amount: data['amount'] || '',
        paid_by: data['paid_by'] || '',
        parent_1_split: data['parent_1_split'] || '',
        parent_2_split: data['parent_2_split'] || '',
        status: data['status'] || '',
        receipt: data['receipt'] || '',
        settlement: data['settlement'] || '',
        children: data['children'] || '',
        notes: data['notes'] || '',
      };
      return res.status(200).json({ data: fields });
    }
  } catch (error) {
    return res.status(404).json({ message: 'Something went wrong', data: null });
  }
});

app.delete('/admin/api/expenses-delete/:id', JwtService.verifyTokenMiddleware(role), async function (req, res, next) {
  let id = req.params.id;

  const expenseAdminDeleteViewModel = require('../../view_models/expense_admin_delete_view_model');

  const viewModel = new expenseAdminDeleteViewModel(db.expense);

  try {
    const exists = await db.expense.getByPK(id);

    if (!exists) {
      return res.status(404).json({ success: false, message: 'Expense not found' });
    }

    await db.expense.realDelete(id);

    await db.activity_log.insert({
      action: 'DELETE',
      name: 'Admin_expense_controller.js',
      portal: 'admin',
      data: JSON.stringify(exists),
    });

    return res.status(200).json({ success: true, message: 'Expense deleted successfully' });
  } catch (error) {
    return res.status(500).json({ success: false, message: 'Something went wrong' });
  }
});

module.exports = app;
