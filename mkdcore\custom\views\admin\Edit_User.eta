

<% if(it.layout_clean_mode) {%>
<% layout("../layouts/admin/Clean") %>
<% } else {%>
<% layout("../layouts/admin/Main") %>
<%}%>

<%~ includeFile("../partials/admin/Breadcrumb", it)%>

<div class="tab-content mx-4 my-4">
    <div class="row">
        <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
        <%~ includeFile("../partials/admin/GlobalResponse.eta", it) %>
            <div class="form-container card p-4">
                <h5 class="primaryHeading2 mb-4 text-md-left pl-3">
                    <%= it.heading %>
                </h5>
                
              <form action="/admin/users-edit/<%= it.form_fields['id'] %>" method="POST"  >
              
   
<div class="form-group required col-md-5 col-sm-12 ">
            <label for="email" class="control-label">Email</label>
            
        
        <input type="email" required class="form-control data-input"
        id="text_email" 
        name="credential_email" value="<%= it.form_fields['credential.email'] %>"/>
      
        
          </div>

<div class="form-group  col-md-5 col-sm-12 ">
            <label for="first_name" class="control-label">First Name</label>
            
        
        <input type="text"  class="form-control data-input"
        id="text_first_name" 
        name="first_name" value="<%= it.form_fields['first_name'] %>"/>
      
      
          </div>

<div class="form-group  col-md-5 col-sm-12 ">
            <label for="last_name" class="control-label">Last Name</label>
            
        
        <input type="text"  class="form-control data-input"
        id="text_last_name" 
        name="last_name" value="<%= it.form_fields['last_name'] %>"/>
      
      
          </div>

<div class="form-group  col-md-5 col-sm-12 ">
            <label for="role_id" class="control-label">Role ID</label>
            
        
          <select class="custom-select" id="select_mapping_role_id" name="role_id" >
            <% Object.keys(it.role_id_mapping()).forEach(function(prop) { %>
                <% const same = it.form_fields["role_id"] == prop ? 'selected' : false %>
                <option class="select_mapping_role_id_option<%= same ? '_' + same : '' %>" value="<%= prop %>" <%= same %> >
                  <%= it.role_id_mapping()[prop] %>
                </option>
            <% }) %>
          </select>
        
      
          </div>

<div class="form-group  col-md-5 col-sm-12 ">
            <label for="image" class="control-label">Image</label>
            
        
          <img id="media_image" src="<%= it.form_fields['image'] %>" class="mb-3" style="width:auto; height:200px; object-fit:contain; background:#00000010; border-radius:8px" />
                        
        <button id="button_file_image" type="button" class="btn btn-outline-primary btn-block mkd-uppload-image-button data-input" data-upload-for="image">Upload</button>
        <input id="file_image" name="image" type="text" hidden value="<%= it.form_fields['image'] %>" class="form-control-file">
      
      
          </div>

<div class="form-group  col-md-5 col-sm-12 ">
            <label for="phone" class="control-label">Phone </label>
            
        
        <input type="text"  class="form-control data-input"
        id="text_phone" 
        name="phone" value="<%= it.form_fields['phone'] %>"/>
      
      
          </div>

<div class="form-group  col-md-5 col-sm-12 ">
            <label for="status" class="control-label">Status</label>
            
        
          <select class="custom-select" id="select_mapping_status" name="status" >
            <% Object.keys(it.status_mapping()).forEach(function(prop) { %>
                <% const same = it.form_fields["status"] == prop ? 'selected' : false %>
                <option class="select_mapping_status_option<%= same ? '_' + same : '' %>" value="<%= prop %>" <%= same %> >
                  <%= it.status_mapping()[prop] %>
                </option>
            <% }) %>
          </select>
        
      
          </div>

  
               <div class="form-group pl-3">
                   <button type="submit" class="btn btn-primary">Submit</button>
                 </div>
               </form>
              
            </div>
        </div>
    </div>
</div>