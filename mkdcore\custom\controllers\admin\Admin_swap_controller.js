'use strict';

const app = require('express').Router();
const Sequelize = require('sequelize');
const logger = require('../../services/LoggingService');
let pagination = require('../../services/PaginationService');
let SessionService = require('../../services/SessionService');
let JwtService = require('../../services/JwtService');
const ValidationService = require('../../services/ValidationService');
const PermissionService = require('../../services/PermissionService');
const UploadService = require('../../services/UploadService');
const AuthService = require('../../services/AuthService');
const db = require('../../models');
const helpers = require('../../core/helpers');

const role = 1;

app.get('/admin/swaps/:num', SessionService.verifySessionMiddleware(role, 'admin'), async function (req, res, next) {
  try {
    let session = req.session;
    let paginateListViewModel = require('../../view_models/swap_admin_list_paginate_view_model');

    var viewModel = new paginateListViewModel(db.swap, 'Co-Swaps', session.success, session.error, '/admin/swaps');

    const format = req.query.format ? req.query.format : 'view';
    const direction = req.query.direction ? req.query.direction : 'ASC';
    const per_page = req.query.per_page ? req.query.per_page : 10;
    let order_by = req.query.order_by ? req.query.order_by : viewModel.get_field_column()[0];
    let orderAssociations = [];
    viewModel.set_order_by(order_by);
    let joins = order_by.includes('.') ? order_by.split('.') : [];
    order_by = order_by.includes('.') ? joins[joins.length - 1] : order_by;
    if (joins.length > 0) {
      for (let i = joins.length - 1; i > 0; i--) {
        orderAssociations.push(`${joins[i - 1]}`);
      }
    }
    // Check for flash messages
    const flashMessageSuccess = req.flash('success');
    if (flashMessageSuccess && flashMessageSuccess.length > 0) {
      viewModel.success = flashMessageSuccess[0];
    }
    const flashMessageError = req.flash('error');
    if (flashMessageError && flashMessageError.length > 0) {
      viewModel.error = flashMessageError[0];
    }

    viewModel.set_id(req.query.id ? req.query.id : '');
    viewModel.set_cohort_id(req.query.cohort_id ? req.query.cohort_id : '');
    viewModel.set_time_type(req.query.time_type ? req.query.time_type : '');
    viewModel.set_request_from(req.query.request_from ? req.query.request_from : '');
    viewModel.set_date(req.query.date ? req.query.date : '');
    viewModel.set_quantity(req.query.quantity ? req.query.quantity : '');
    viewModel.set_status(req.query.status ? req.query.status : '');

    let where = helpers.filterEmptyFields({
      id: viewModel.get_id(),
      cohort_id: viewModel.get_cohort_id(),
      time_type: viewModel.get_time_type(),
      request_from: viewModel.get_request_from(),
      date: viewModel.get_date(),
      quantity: viewModel.get_quantity(),
      status: viewModel.get_status(),
    });

    let associatedWhere = helpers.filterEmptyFields({});
    const isAssociationRequired = Object.keys(associatedWhere).length > 0 ? true : false;

    const count = await db.swap._count(where, [{ model: db.user, where: associatedWhere, required: isAssociationRequired, as: 'user' }]);

    let sort_url = '';

    if (req.originalUrl.includes('?')) {
      if (req.originalUrl.includes('order_by')) {
        let url_query = req.originalUrl.split('?')[1];
        sort_url = `${url_query.split('order_by')[0]}`;
      } else {
        sort_url = `${req.originalUrl.split('?')[1]}`;
      }
    }
    viewModel.set_total_rows(count);
    viewModel.set_per_page(+per_page);
    viewModel.set_page(+req.params.num);
    viewModel.set_query(req.query);
    viewModel.set_sort_base_url(`/admin/swaps/${+req.params.num}?${sort_url}`);
    viewModel.set_sort(direction);

    const list = await db.swap.get_user_paginated(
      db,
      associatedWhere,
      viewModel.get_page() - 1 < 0 ? 0 : viewModel.get_page(),
      viewModel.get_per_page(),
      where,
      order_by,
      direction,
      orderAssociations,
    );

    viewModel.set_list(list);

    viewModel.user = await db.user;

    if (format == 'csv') {
      const csv = viewModel.to_csv();
      return res
        .set({
          'Content-Type': 'text/csv',
          'Content-Disposition': 'attachment; filename="export.csv"',
        })
        .send(csv);
    }

    // if (format != 'view') {
    //   res.json(viewModel.to_json());
    // } else {
    // }

    return res.render('admin/Swap', viewModel);
  } catch (error) {
    console.error(error);
    viewModel.error = error.message || 'Something went wrong';
    return res.render('admin/Swap', viewModel);
  }
});

app.get('/admin/swaps-add', SessionService.verifySessionMiddleware(role, 'admin'), async function (req, res, next) {
  if (req.session.csrf === undefined) {
    req.session.csrf = SessionService.randomString(100);
  }

  const swapAdminAddViewModel = require('../../view_models/swap_admin_add_view_model');

  const viewModel = new swapAdminAddViewModel(db.swap, 'Add swap', '', '', '/admin/swaps');
  viewModel.heading = 'Add Co-Swap';
  res.render('admin/Add_Swap', viewModel);
});

app.post(
  '/admin/swaps-add',
  SessionService.verifySessionMiddleware(role, 'admin'),
  ValidationService.validateInput(
    { cohort_id: 'required', time_type: 'required', request_from: 'required', date: 'required', quantity: 'required', notes: 'required', status: 'required' },
    {
      'cohort_id.required': 'CohortId is required',
      'time_type.required': 'TimeType is required',
      'request_from.required': 'RequestFrom is required',
      'date.required': 'Date is required',
      'quantity.required': 'Quantity is required',
      'notes.required': 'Notes is required',
      'status.required': 'Status is required',
    },
  ),
  async function (req, res, next) {
    if (req.session.csrf === undefined) {
      req.session.csrf = SessionService.randomString(100);
    }
    const swapAdminAddViewModel = require('../../view_models/swap_admin_add_view_model');

    const viewModel = new swapAdminAddViewModel(db.swap, 'Add swap', '', '', '/admin/swaps');

    // TODO use separate controller for image upload
    //  {{{upload_field_setter}}}

    const { cohort_id, time_type, request_from, date, quantity, notes, status } = req.body;

    viewModel.form_fields = {
      ...viewModel.form_fields,
      cohort_id,
      time_type,
      request_from,
      date,
      quantity,
      notes,
      status,
    };

    try {
      if (req.validationError) {
        viewModel.error = req.validationError;
        return res.render('admin/Add_Swap', viewModel);
      }

      viewModel.session = req.session;

      const data = await db.swap.insert({ cohort_id, time_type, request_from, date, quantity, notes, status });

      if (!data) {
        viewModel.error = 'Something went wrong';
        return res.render('admin/Add_Swap', viewModel);
      }

      await db.activity_log.insert({
        action: 'ADD',
        name: 'Admin_swap_controller.js',
        portal: 'admin',
        data: JSON.stringify({ cohort_id, time_type, request_from, date, quantity, notes, status }),
      });

      req.flash('success', 'Swap created successfully');
      return res.redirect('/admin/swaps/0');
    } catch (error) {
      console.error(error);
      viewModel.error = error.message || 'Something went wrong';
      return res.render('admin/Add_Swap', viewModel);
    }
  },
);

app.get('/admin/swaps-edit/:id', SessionService.verifySessionMiddleware(role, 'admin'), async function (req, res, next) {
  let id = req.params.id;
  if (req.session.csrf === undefined) {
    req.session.csrf = SessionService.randomString(100);
  }
  const swapAdminEditViewModel = require('../../view_models/swap_admin_edit_view_model');

  const viewModel = new swapAdminEditViewModel(db.swap, 'Edit swap', '', '', '/admin/swaps');
  viewModel.heading = 'Edit Co-Swap';

  try {
    const exists = await db.swap.getByPK(id);

    if (!exists) {
      req.flash('error', 'Swap not found');
      return res.redirect('/admin/swaps/0');
    }
    const values = exists;
    Object.keys(viewModel.form_fields).forEach((field) => {
      viewModel.form_fields[field] = values[field] || '';
    });
    viewModel.user = db.user;
    return res.render('admin/Edit_Swap', viewModel);
  } catch (error) {
    console.error(error);
    viewModel.error = error.message || 'Something went wrong';
    return res.render('admin/Edit_Swap', viewModel);
  }
});

app.post(
  '/admin/swaps-edit/:id',
  SessionService.verifySessionMiddleware(role, 'admin'),
  ValidationService.validateInput(
    { time_type: 'required', request_from: 'required', date: 'required', quantity: 'required', notes: 'required', status: 'required' },
    {
      'time_type.required': 'TimeType is required',
      'request_from.required': 'RequestFrom is required',
      'date.required': 'Date is required',
      'quantity.required': 'Quantity is required',
      'notes.required': 'Notes is required',
      'status.required': 'Status is required',
    },
  ),
  async function (req, res, next) {
    let id = req.params.id;
    if (req.session.csrf === undefined) {
      req.session.csrf = SessionService.randomString(100);
    }

    const swapAdminEditViewModel = require('../../view_models/swap_admin_edit_view_model');

    const viewModel = new swapAdminEditViewModel(db.swap, 'Edit swap', '', '', '/admin/swaps');

    const { time_type, request_from, date, quantity, notes, status } = req.body;

    viewModel.form_fields = {
      ...viewModel.form_fields,
      time_type,
      request_from,
      date,
      quantity,
      notes,
      status,
    };

    delete viewModel.form_fields.id;

    try {
      if (req.validationError) {
        viewModel.error = req.validationError;
        return res.render('admin/Edit_Swap', viewModel);
      }

      const resourceExists = await db.swap.getByPK(id);
      if (!resourceExists) {
        req.flash('error', 'Swap not found');
        return res.redirect('/admin/swaps/0');
      }

      viewModel.session = req.session;

      let data = await db.swap.edit({ time_type, request_from, date, quantity, notes, status }, id);
      if (!data) {
        viewModel.error = 'Something went wrong';
        return res.render('admin/Edit_Swap', viewModel);
      }

      if (resourceExists.user) {
        data = await db.user.edit(helpers.filterEmptyFields({}), resourceExists.user.id);
        if (!data) {
          viewModel.error = 'Something went wrong';
          return res.render('admin/Edit_Swap', viewModel);
        }
      }

      await db.activity_log.insert({
        action: 'EDIT',
        name: 'Admin_swap_controller.js',
        portal: 'admin',
        data: JSON.stringify({ time_type, request_from, date, quantity, notes, status }),
      });

      req.flash('success', 'Swap edited successfully');

      return res.redirect('/admin/swaps/0');
    } catch (error) {
      console.error(error);
      viewModel.error = error.message || 'Something went wrong';
      return res.render('admin/Edit_Swap', viewModel);
    }
  },
);

app.get(
  '/admin/swaps-view/:id',
  SessionService.verifySessionMiddleware(role, 'admin'),

  async function (req, res, next) {
    try {
      let id = req.params.id;

      const swapAdminDetailViewModel = require('../../view_models/swap_admin_detail_view_model');

      var viewModel = new swapAdminDetailViewModel(db.swap, 'Swap details', '', '', '/admin/swaps');
      viewModel.heading = 'Co-Swap details';

      const data = await db.swap.getByPK(id);
      data.time_type = db.swap.time_type_mapping()[data.time_type];
      data.status = db.swap.status_mapping()[data.status];

      if (!data) {
        viewModel.error = 'Swap not found';
        viewModel.detail_fields = {
          ...viewModel.detail_fields,
          id: 'N/A',
          cohort_id: 'N/A',
          time_type: 'N/A',
          request_from: 'N/A',
          date: 'N/A',
          quantity: 'N/A',
          notes: 'N/A',
          status: 'N/A',
        };
      } else {
        viewModel.detail_fields = {
          ...viewModel.detail_fields,
          id: data['id'] || 'N/A',
          cohort_id: data['cohort_id'] || 'N/A',
          time_type: data['time_type'] || 'N/A',
          request_from: data['request_from'] || 'N/A',
          date: data['date'] || 'N/A',
          quantity: data['quantity'] || 'N/A',
          notes: data['notes'] || 'N/A',
          status: data['status'] || 'N/A',
        };
      }

      res.render('admin/View_Swap', viewModel);
    } catch (error) {
      console.error(error);
      viewModel.error = error.message || 'Something went wrong';
      viewModel.detail_fields = {
        ...viewModel.detail_fields,
        id: 'N/A',
        cohort_id: 'N/A',
        time_type: 'N/A',
        request_from: 'N/A',
        date: 'N/A',
        quantity: 'N/A',
        notes: 'N/A',
        status: 'N/A',
      };
      res.render('admin/View_Swap', viewModel);
    }
  },
);

app.get('/admin/swaps-delete/:id', SessionService.verifySessionMiddleware(role, 'admin'), async function (req, res, next) {
  let id = req.params.id;

  const swapAdminDeleteViewModel = require('../../view_models/swap_admin_delete_view_model');

  const viewModel = new swapAdminDeleteViewModel(db.swap);

  try {
    const exists = await db.swap.getByPK(id);

    if (!exists) {
      req.flash('error', 'Swap not found');
      return res.redirect('/admin/swaps/0');
    }

    viewModel.session = req.session;

    await db.swap.realDelete(id);

    await db.activity_log.insert({
      action: 'DELETE',
      name: 'Admin_swap_controller.js',
      portal: 'admin',
      data: JSON.stringify(exists),
    });

    req.flash('success', 'Swap was deleted successfully');

    return res.redirect('/admin/swaps/0');
  } catch (error) {
    console.error(error);
    req.flash('error', error.message || 'Something went wrong');
    return res.redirect('/admin/swaps/0');
  }
});

// APIS

app.get('/admin/api/swaps', JwtService.verifyTokenMiddleware(role), async function (req, res, next) {
  try {
    const user_id = req.user_id;
    const session = req.session;
    let listViewModel = require('../../view_models/swap_admin_list_paginate_view_model');
    let viewModel = new listViewModel(db.swap, 'Co-Swaps', session.success, session.error, '/admin/swaps');
    const direction = req.query.direction ? req.query.direction : 'ASC';
    const page = req.query.page ? parseInt(req.query.page) : 1;
    const limit = req.query.limit ? parseInt(req.query.limit) : 10;
    const offset = (page - 1) * limit;
    let order_by = req.query.order_by ? req.query.order_by : viewModel.get_field_column()[0];
    let orderAssociations = [];
    viewModel.set_order_by(order_by);
    let joins = order_by.includes('.') ? order_by.split('.') : [];
    order_by = order_by.includes('.') ? joins[joins.length - 1] : order_by;
    if (joins.length > 0) {
      for (let i = joins.length - 1; i > 0; i--) {
        orderAssociations.push(`${joins[i - 1]}`);
      }
    }

    viewModel.set_id(req.query.id ? req.query.id : '');
    viewModel.set_cohort_id(req.query.cohort_id ? req.query.cohort_id : '');
    viewModel.set_time_type(req.query.time_type ? req.query.time_type : '');
    viewModel.set_request_from(req.query.request_from ? req.query.request_from : '');
    viewModel.set_date(req.query.date ? req.query.date : '');
    viewModel.set_quantity(req.query.quantity ? req.query.quantity : '');
    viewModel.set_status(req.query.status ? req.query.status : '');

    let where = helpers.filterEmptyFields({
      id: viewModel.get_id(),
      cohort_id: viewModel.get_cohort_id(),
      time_type: viewModel.get_time_type(),
      request_from: viewModel.get_request_from(),
      date: viewModel.get_date(),
      quantity: viewModel.get_quantity(),
      status: viewModel.get_status(),
    });

    let associatedWhere = helpers.filterEmptyFields({});
    const isAssociationRequired = Object.keys(associatedWhere).length > 0 ? true : false;

    let include = [{ model: db.user, where: associatedWhere, required: isAssociationRequired, as: 'user' }];

    const { rows: allItems, count } = await db.swap.findAndCountAll({
      where: where,
      limit: limit == 0 ? null : limit,
      offset: offset,
      include: include,
      distinct: true,
    });

    const response = {
      items: allItems,
      page,
      nextPage: count > offset + limit ? page + 1 : false,
      retrievedCount: allItems.length,
      fullCount: count,
    };

    return res.status(201).json({ success: true, data: response });
  } catch (error) {
    console.error(error);
    return res.status(500).json({ success: false, message: error.message || 'Something went wrong' });
  }
});

app.post(
  '/admin/api/swaps-add',
  JwtService.verifyTokenMiddleware(role),
  ValidationService.validateInput(
    { cohort_id: 'required', time_type: 'required', request_from: 'required', date: 'required', quantity: 'required', notes: 'required', status: 'required' },
    {
      'cohort_id.required': 'CohortId is required',
      'time_type.required': 'TimeType is required',
      'request_from.required': 'RequestFrom is required',
      'date.required': 'Date is required',
      'quantity.required': 'Quantity is required',
      'notes.required': 'Notes is required',
      'status.required': 'Status is required',
    },
  ),
  async function (req, res, next) {
    const swapAdminAddViewModel = require('../../view_models/swap_admin_add_view_model');

    const viewModel = new swapAdminAddViewModel(db.swap);

    const { cohort_id, time_type, request_from, date, quantity, notes, status } = req.body;
    try {
      if (req.validationError) {
        return res.status(500).json({ success: false, message: req.validationError });
      }

      const data = await db.swap.insert({ cohort_id, time_type, request_from, date, quantity, notes, status });

      if (!data) {
        return res.status(500).json({ success: false, message: 'Something went wrong' });
      }

      await db.activity_log.insert({
        action: 'ADD',
        name: 'Admin_swap_controller.js',
        portal: 'admin',
        data: JSON.stringify({ cohort_id, time_type, request_from, date, quantity, notes, status }),
      });

      return res.status(201).json({ success: true, message: 'Swap created successfully' });
    } catch (error) {
      return res.status(500).json({ success: false, message: 'Something went wrong' });
    }
  },
);

app.put(
  '/admin/api/swaps-edit/:id',
  JwtService.verifyTokenMiddleware(role),
  ValidationService.validateInput(
    { time_type: 'required', request_from: 'required', date: 'required', quantity: 'required', notes: 'required', status: 'required' },
    {
      'time_type.required': 'TimeType is required',
      'request_from.required': 'RequestFrom is required',
      'date.required': 'Date is required',
      'quantity.required': 'Quantity is required',
      'notes.required': 'Notes is required',
      'status.required': 'Status is required',
    },
  ),
  async function (req, res, next) {
    let id = req.params.id;

    const swapAdminEditViewModel = require('../../view_models/swap_admin_edit_view_model');

    const viewModel = new swapAdminEditViewModel(db.swap);

    const { time_type, request_from, date, quantity, notes, status } = req.body;

    try {
      if (req.validationError) {
        return res.status(500).json({ success: false, message: req.validationError });
      }

      const resourceExists = await db.swap.getByPK(id);
      if (!resourceExists) {
        return res.status(404).json({ success: false, message: 'Swap not found' });
      }

      const data = await db.swap.edit({ time_type, request_from, date, quantity, notes, status }, id);

      if (!data) {
        return res.status(500).json({ success: false, message: 'Something went wrong' });
      }

      await db.activity_log.insert({
        action: 'EDIT',
        name: 'Admin_swap_controller.js',
        portal: 'admin',
        data: JSON.stringify({ time_type, request_from, date, quantity, notes, status }),
      });

      return res.json({ success: true, message: 'Swap edited successfully' });
    } catch (error) {
      return res.status(500).json({ success: false, message: 'Something went wrong' });
    }
  },
);

app.get('/admin/api/swaps-view/:id', JwtService.verifyTokenMiddleware(role), async function (req, res, next) {
  let id = req.params.id;

  const swapAdminDetailViewModel = require('../../view_models/swap_admin_detail_view_model');

  const viewModel = new swapAdminDetailViewModel(db.swap);

  try {
    const data = await db.swap.getByPK(id);

    if (!data) {
      return res.status(404).json({ message: 'Swap not found', data: null });
    } else {
      const fields = {
        ...viewModel.detail_fields,
        id: data['id'] || '',
        cohort_id: data['cohort_id'] || '',
        time_type: data['time_type'] || '',
        request_from: data['request_from'] || '',
        date: data['date'] || '',
        quantity: data['quantity'] || '',
        notes: data['notes'] || '',
        status: data['status'] || '',
      };
      return res.status(200).json({ data: fields });
    }
  } catch (error) {
    return res.status(404).json({ message: 'Something went wrong', data: null });
  }
});

app.delete('/admin/api/swaps-delete/:id', JwtService.verifyTokenMiddleware(role), async function (req, res, next) {
  let id = req.params.id;

  const swapAdminDeleteViewModel = require('../../view_models/swap_admin_delete_view_model');

  const viewModel = new swapAdminDeleteViewModel(db.swap);

  try {
    const exists = await db.swap.getByPK(id);

    if (!exists) {
      return res.status(404).json({ success: false, message: 'Swap not found' });
    }

    await db.swap.realDelete(id);

    await db.activity_log.insert({
      action: 'DELETE',
      name: 'Admin_swap_controller.js',
      portal: 'admin',
      data: JSON.stringify(exists),
    });

    return res.status(200).json({ success: true, message: 'Swap deleted successfully' });
  } catch (error) {
    return res.status(500).json({ success: false, message: 'Something went wrong' });
  }
});

module.exports = app;
