<!DOCTYPE html>
<html>

<head>
  <!-- Required meta tags-->
  <meta charset="utf-8" />
  <meta name="viewport"
    content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no, viewport-fit=cover" />
  <meta name="apple-mobile-web-app-capable" content="yes" />
  <!-- Color theme for statusbar (Android only) -->
  <meta name="theme-color" content="#2196f3" />
  <!-- Your app title -->
  <title>Co-Parent</title>
</head>

<body>
  <!-- App root element -->
  <div id="app">
    <div class="page" data-name="files">
      <!-- Top Navbar -->
      <div class="navbar">
        <div class="navbar-bg"></div>
        <div class="navbar-inner">
          <div class="left">
            <a href="#" class="panel-toggle color-black">&#9776;</a>
          </div>
          <div class="title text-align-center">Co-Files</div>
          <div class="right">
            <a href="/addfiles/"><i class="f7-icons color-black">plus_circle</i></a>
          </div>
        </div>
      </div>

      <!-- Scrollable page content -->
      <div class="page-content">
        <div class="list no-hairlines no-margin-vertical">
          <ul class="files-list page-start-padding">
            <li class="smart-select-list justify-content-flex-end">
              <div class="item-title item-label smart-select-label">Choose Co-Hort </div>
              <a class="item-link smart-select smart-select-init" data-open-in="sheet" data-close-on-select="true">
                <select name="selected-cohort" onchange="getCohortFiles()" id="files-cohort-drop-filter" required
                  validate data-validate-on-blur="true">

                </select>
                <div class="item-content expensesChoseCohortSelect filesChoseCohortSelect">
                  <div class="item-inner">
                    <div class="item-title">Choose Co-Hort</div>
                  </div>
                </div>
              </a>
            </li>

          </ul>

          <div class="expenses-tabs" id="expenses-tabs">

          </div>
        </div>
      </div>
    </div>
  </div>
</body>

</html>