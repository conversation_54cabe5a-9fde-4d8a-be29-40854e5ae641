<!DOCTYPE html>
<html>

<head>
    <!-- Required meta tags-->
    <meta charset="utf-8">
    <meta name="viewport"
        content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no, viewport-fit=cover">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <!-- Color theme for statusbar (Android only) -->
    <meta name="theme-color" content="#2196f3">
    <!-- Your app title -->
    <title>Card</title>
</head>

<body>
    <!-- App root element -->
    <div id="app">
        <div class="page" data-name="add-card">

            <!-- Top Navbar -->
            <div class="navbar">
                <div class="navbar-bg"></div>
                <div class="navbar-inner">
                    <div class="left">
                     
                    </div>
                    <div class="title text-align-center">
                        Card
                    </div>
                    <div class="right">
                    </div>
                </div>
            </div>

            <!-- Scrollable page content -->
            <div class="page-content no-swipe-panel">
                <div class="card">
                    <div class="card-content padding-vertical">
                        <div class="block list no-hairlines">
                            <form id="subscribe-form">
                              <div id="card-container">

                              </div>
                                <button type="submit" style="margin-top: 30px;" id="card-submit" class="button button-fill">
                                  Update Card
                                </button>  
                            </form>
                        </div>
                    </div>
                </div>
            </div>

        </div>
</body>

</html>