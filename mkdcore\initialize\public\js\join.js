async function validateJoinCode() {
  var mainview = app.view.main;
  let code = document.getElementById('joincode').value;
  try {
    app.preloader.show();

    if (code == '') {
      throw "Please enter a valid code";
    }
    else {
      let body = {
        invite_code: code,
      };
      let response = await fetch('/api/user/join', {
        method: 'POST',
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
      });
      resJSON = await response.json();
      if (response.status == 200) {
        app.preloader.hide();
        app.dialog.alert('You have joined the Co-Hort');
        mainview.router.navigate({ name: 'viewcohort' });
      }
      if (response.status == 400) {
        app.preloader.hide();
        app.dialog.alert('Cannot join Co-Hort');
      }
      if (response.status == 404) {
        app.preloader.hide();
        app.dialog.alert('Invalid invitation code');
      }
    }
  } catch (error) {
    app.preloader.hide();
    app.dialog.alert(error);
  }
}
