const { request, response } = require('express');

const authService = require('../services/AuthService');
const jwtService = require('../services/JwtService');

module.exports = {
  /**
   * Forgot password router
   * @param {request} req
   * @param {response} res
   */
  get: async function (req, res) {
    try {
      await authService.forgotPassword(req.body.email);

      res.status(200).json({ success: true });
    } catch (error) {
      res.status(500).json({ success: false, message: error.message });
    }
  },
};
