'use strict';

const app = require('express').Router();
const Sequelize = require('sequelize');
const logger = require('../../services/LoggingService');
let pagination = require('../../services/PaginationService');
let SessionService = require('../../services/SessionService');
let JwtService = require('../../services/JwtService');
const ValidationService = require('../../services/ValidationService');
const PermissionService = require('../../services/PermissionService');
const UploadService = require('../../services/UploadService');
const AuthService = require('../../services/AuthService');
const db = require('../../models');
const helpers = require('../../core/helpers');

const role = 1;

app.get('/admin/notification_logs/:num', SessionService.verifySessionMiddleware(role, 'admin'), async function (req, res, next) {
  try {
    let session = req.session;
    let paginateListViewModel = require('../../view_models/notification_log_admin_list_paginate_view_model');

    var viewModel = new paginateListViewModel(db.notification_log, 'Notification Log', session.success, session.error, '/admin/notification_logs');

    const format = req.query.format ? req.query.format : 'view';
    const direction = req.query.direction ? req.query.direction : 'ASC';
    const per_page = req.query.per_page ? req.query.per_page : 10;
    let order_by = req.query.order_by ? req.query.order_by : viewModel.get_field_column()[0];
    let orderAssociations = [];
    viewModel.set_order_by(order_by);
    let joins = order_by.includes('.') ? order_by.split('.') : [];
    order_by = order_by.includes('.') ? joins[joins.length - 1] : order_by;
    if (joins.length > 0) {
      for (let i = joins.length - 1; i > 0; i--) {
        orderAssociations.push(`${joins[i - 1]}`);
      }
    }
    // Check for flash messages
    const flashMessageSuccess = req.flash('success');
    if (flashMessageSuccess && flashMessageSuccess.length > 0) {
      viewModel.success = flashMessageSuccess[0];
    }
    const flashMessageError = req.flash('error');
    if (flashMessageError && flashMessageError.length > 0) {
      viewModel.error = flashMessageError[0];
    }

    viewModel.set_id(req.query.id ? req.query.id : '');
    viewModel.set_to(req.query.to ? req.query.to : '');
    viewModel.set_status(req.query.status ? req.query.status : '');
    viewModel.set_name(req.query.name ? req.query.name : '');

    let where = helpers.filterEmptyFields({
      id: viewModel.get_id(),
      to: viewModel.get_to(),
      status: viewModel.get_status(),
      name: viewModel.get_name(),
    });

    const count = await db.notification_log._count(where, []);

    let sort_url = '';

    if (req.originalUrl.includes('?')) {
      if (req.originalUrl.includes('order_by')) {
        let url_query = req.originalUrl.split('?')[1];
        sort_url = `${url_query.split('order_by')[0]}`;
      } else {
        sort_url = `${req.originalUrl.split('?')[1]}`;
      }
    }
    viewModel.set_total_rows(count);
    viewModel.set_per_page(+per_page);
    viewModel.set_page(+req.params.num);
    viewModel.set_query(req.query);
    viewModel.set_sort_base_url(`/admin/notification_logs/${+req.params.num}?${sort_url}`);
    viewModel.set_sort(direction);

    const list = await db.notification_log.getPaginated(
      viewModel.get_page() - 1 < 0 ? 0 : viewModel.get_page(),
      viewModel.get_per_page(),
      where,
      order_by,
      direction,
      orderAssociations,
    );

    viewModel.set_list(list);

    if (format == 'csv') {
      const csv = viewModel.to_csv();
      return res
        .set({
          'Content-Type': 'text/csv',
          'Content-Disposition': 'attachment; filename="export.csv"',
        })
        .send(csv);
    }

    // if (format != 'view') {
    //   res.json(viewModel.to_json());
    // } else {
    // }

    return res.render('admin/Notification_log', viewModel);
  } catch (error) {
    console.error(error);
    viewModel.error = error.message || 'Something went wrong';
    return res.render('admin/Notification_log', viewModel);
  }
});

app.get('/admin/notification_logs-add', SessionService.verifySessionMiddleware(role, 'admin'), async function (req, res, next) {
  if (req.session.csrf === undefined) {
    req.session.csrf = SessionService.randomString(100);
  }

  const notificationLogAdminAddViewModel = require('../../view_models/notification_log_admin_add_view_model');

  const viewModel = new notificationLogAdminAddViewModel(db.notification_log, 'Add notification log', '', '', '/admin/notification_logs');

  res.render('admin/Add_Notification_log', viewModel);
});

app.post(
  '/admin/notification_logs-add',
  SessionService.verifySessionMiddleware(role, 'admin'),
  ValidationService.validateInput(
    { to: 'required', status: 'required', name: 'required', description: 'required' },
    { 'to.required': 'To is required', 'status.required': 'Status is required', 'name.required': 'Name is required', 'description.required': 'Description is required' },
  ),
  async function (req, res, next) {
    if (req.session.csrf === undefined) {
      req.session.csrf = SessionService.randomString(100);
    }
    const notificationLogAdminAddViewModel = require('../../view_models/notification_log_admin_add_view_model');

    const viewModel = new notificationLogAdminAddViewModel(db.notification_log, 'Add notification log', '', '', '/admin/notification_logs');

    // TODO use separate controller for image upload
    //  {{{upload_field_setter}}}

    const { to, status, name, description } = req.body;

    viewModel.form_fields = {
      ...viewModel.form_fields,
      to,
      status,
      name,
      description,
    };

    try {
      if (req.validationError) {
        viewModel.error = req.validationError;
        return res.render('admin/Add_Notification_log', viewModel);
      }

      viewModel.session = req.session;

      const data = await db.notification_log.insert({ to, status, name, description });

      if (!data) {
        viewModel.error = 'Something went wrong';
        return res.render('admin/Add_Notification_log', viewModel);
      }

      await db.activity_log.insert({
        action: 'ADD',
        name: 'Admin_notification_log_controller.js',
        portal: 'admin',
        data: JSON.stringify({ to, status, name, description }),
      });

      req.flash('success', 'Notification log created successfully');
      return res.redirect('/admin/notification_logs/0');
    } catch (error) {
      console.error(error);
      viewModel.error = error.message || 'Something went wrong';
      return res.render('admin/Add_Notification_log', viewModel);
    }
  },
);

app.get('/admin/notification_logs-edit/:id', SessionService.verifySessionMiddleware(role, 'admin'), async function (req, res, next) {
  let id = req.params.id;
  if (req.session.csrf === undefined) {
    req.session.csrf = SessionService.randomString(100);
  }
  const notificationLogAdminEditViewModel = require('../../view_models/notification_log_admin_edit_view_model');

  const viewModel = new notificationLogAdminEditViewModel(db.notification_log, 'Edit notification log', '', '', '/admin/notification_logs');

  try {
    const exists = await db.notification_log.getByPK(id);

    if (!exists) {
      req.flash('error', 'Notification log not found');
      return res.redirect('/admin/notification_logs/0');
    }
    const values = exists;
    Object.keys(viewModel.form_fields).forEach((field) => {
      viewModel.form_fields[field] = values[field] || '';
    });

    return res.render('admin/Edit_Notification_log', viewModel);
  } catch (error) {
    console.error(error);
    viewModel.error = error.message || 'Something went wrong';
    return res.render('admin/Edit_Notification_log', viewModel);
  }
});

app.post(
  '/admin/notification_logs-edit/:id',
  SessionService.verifySessionMiddleware(role, 'admin'),
  ValidationService.validateInput(
    { to: 'required', status: 'required', name: 'required', description: 'required' },
    { 'to.required': 'To is required', 'status.required': 'Status is required', 'name.required': 'Name is required', 'description.required': 'Description is required' },
  ),
  async function (req, res, next) {
    let id = req.params.id;
    if (req.session.csrf === undefined) {
      req.session.csrf = SessionService.randomString(100);
    }

    const notificationLogAdminEditViewModel = require('../../view_models/notification_log_admin_edit_view_model');

    const viewModel = new notificationLogAdminEditViewModel(db.notification_log, 'Edit notification log', '', '', '/admin/notification_logs');

    const { to, status, name, description } = req.body;

    viewModel.form_fields = {
      ...viewModel.form_fields,
      to,
      status,
      name,
      description,
    };

    delete viewModel.form_fields.id;

    try {
      if (req.validationError) {
        viewModel.error = req.validationError;
        return res.render('admin/Edit_Notification_log', viewModel);
      }

      const resourceExists = await db.notification_log.getByPK(id);
      if (!resourceExists) {
        req.flash('error', 'Notification log not found');
        return res.redirect('/admin/notification_logs/0');
      }

      viewModel.session = req.session;

      let data = await db.notification_log.edit({ to, status, name, description }, id);
      if (!data) {
        viewModel.error = 'Something went wrong';
        return res.render('admin/Edit_Notification_log', viewModel);
      }

      await db.activity_log.insert({
        action: 'EDIT',
        name: 'Admin_notification_log_controller.js',
        portal: 'admin',
        data: JSON.stringify({ to, status, name, description }),
      });

      req.flash('success', 'Notification log edited successfully');

      return res.redirect('/admin/notification_logs/0');
    } catch (error) {
      console.error(error);
      viewModel.error = error.message || 'Something went wrong';
      return res.render('admin/Edit_Notification_log', viewModel);
    }
  },
);

app.get(
  '/admin/notification_logs-view/:id',
  SessionService.verifySessionMiddleware(role, 'admin'),

  async function (req, res, next) {
    try {
      let id = req.params.id;

      const notificationLogAdminDetailViewModel = require('../../view_models/notification_log_admin_detail_view_model');

      var viewModel = new notificationLogAdminDetailViewModel(db.notification_log, 'Notification log details', '', '', '/admin/notification_logs');

      const data = await db.notification_log.getByPK(id);
      data.status = db.notification_log.status_mapping()[data.status];

      if (!data) {
        viewModel.error = 'Notification log not found';
        viewModel.detail_fields = { ...viewModel.detail_fields, id: 'N/A', to: 'N/A', status: 'N/A', name: 'N/A', description: 'N/A', created_at: 'N/A' };
      } else {
        viewModel.detail_fields = {
          ...viewModel.detail_fields,
          id: data['id'] || 'N/A',
          to: data['to'] || 'N/A',
          status: data['status'] || 'N/A',
          name: data['name'] || 'N/A',
          description: data['description'] || 'N/A',
          created_at: data['created_at'] || 'N/A',
        };
      }

      res.render('admin/View_Notification_log', viewModel);
    } catch (error) {
      console.error(error);
      viewModel.error = error.message || 'Something went wrong';
      viewModel.detail_fields = { ...viewModel.detail_fields, id: 'N/A', to: 'N/A', status: 'N/A', name: 'N/A', description: 'N/A' };
      res.render('admin/View_Notification_log', viewModel);
    }
  },
);

app.get('/admin/notification_logs-delete/:id', SessionService.verifySessionMiddleware(role, 'admin'), async function (req, res, next) {
  let id = req.params.id;

  const notificationLogAdminDeleteViewModel = require('../../view_models/notification_log_admin_delete_view_model');

  const viewModel = new notificationLogAdminDeleteViewModel(db.notification_log);

  try {
    const exists = await db.notification_log.getByPK(id);

    if (!exists) {
      req.flash('error', 'Notification log not found');
      return res.redirect('/admin/notification_logs/0');
    }

    viewModel.session = req.session;

    await db.notification_log.realDelete(id);

    await db.activity_log.insert({
      action: 'DELETE',
      name: 'Admin_notification_log_controller.js',
      portal: 'admin',
      data: JSON.stringify(exists),
    });

    req.flash('success', 'Notification log was deleted successfully');

    return res.redirect('/admin/notification_logs/0');
  } catch (error) {
    console.error(error);
    req.flash('error', error.message || 'Something went wrong');
    return res.redirect('/admin/notification_logs/0');
  }
});

// APIS

app.get('/admin/api/notification_logs', JwtService.verifyTokenMiddleware(role), async function (req, res, next) {
  try {
    const user_id = req.user_id;
    const session = req.session;
    let listViewModel = require('../../view_models/notification_log_admin_list_paginate_view_model');
    let viewModel = new listViewModel(db.notification_log, 'Notification Log', session.success, session.error, '/admin/notification_logs');
    const direction = req.query.direction ? req.query.direction : 'ASC';
    const page = req.query.page ? parseInt(req.query.page) : 1;
    const limit = req.query.limit ? parseInt(req.query.limit) : 10;
    const offset = (page - 1) * limit;
    let order_by = req.query.order_by ? req.query.order_by : viewModel.get_field_column()[0];
    let orderAssociations = [];
    viewModel.set_order_by(order_by);
    let joins = order_by.includes('.') ? order_by.split('.') : [];
    order_by = order_by.includes('.') ? joins[joins.length - 1] : order_by;
    if (joins.length > 0) {
      for (let i = joins.length - 1; i > 0; i--) {
        orderAssociations.push(`${joins[i - 1]}`);
      }
    }

    viewModel.set_id(req.query.id ? req.query.id : '');
    viewModel.set_to(req.query.to ? req.query.to : '');
    viewModel.set_status(req.query.status ? req.query.status : '');
    viewModel.set_name(req.query.name ? req.query.name : '');

    let where = helpers.filterEmptyFields({
      id: viewModel.get_id(),
      to: viewModel.get_to(),
      status: viewModel.get_status(),
      name: viewModel.get_name(),
    });

    let include = [];

    const { rows: allItems, count } = await db.notification_log.findAndCountAll({
      where: where,
      limit: limit == 0 ? null : limit,
      offset: offset,
      include: include,
      distinct: true,
    });

    const response = {
      items: allItems,
      page,
      nextPage: count > offset + limit ? page + 1 : false,
      retrievedCount: allItems.length,
      fullCount: count,
    };

    return res.status(201).json({ success: true, data: response });
  } catch (error) {
    console.error(error);
    return res.status(500).json({ success: false, message: error.message || 'Something went wrong' });
  }
});

app.post(
  '/admin/api/notification_logs-add',
  JwtService.verifyTokenMiddleware(role),
  ValidationService.validateInput(
    { to: 'required', status: 'required', name: 'required', description: 'required' },
    { 'to.required': 'To is required', 'status.required': 'Status is required', 'name.required': 'Name is required', 'description.required': 'Description is required' },
  ),
  async function (req, res, next) {
    const notificationLogAdminAddViewModel = require('../../view_models/notification_log_admin_add_view_model');

    const viewModel = new notificationLogAdminAddViewModel(db.notification_log);

    const { to, status, name, description } = req.body;
    try {
      if (req.validationError) {
        return res.status(500).json({ success: false, message: req.validationError });
      }

      const data = await db.notification_log.insert({ to, status, name, description });

      if (!data) {
        return res.status(500).json({ success: false, message: 'Something went wrong' });
      }

      await db.activity_log.insert({
        action: 'ADD',
        name: 'Admin_notification_log_controller.js',
        portal: 'admin',
        data: JSON.stringify({ to, status, name, description }),
      });

      return res.status(201).json({ success: true, message: 'Notification log created successfully' });
    } catch (error) {
      return res.status(500).json({ success: false, message: 'Something went wrong' });
    }
  },
);

app.put(
  '/admin/api/notification_logs-edit/:id',
  JwtService.verifyTokenMiddleware(role),
  ValidationService.validateInput(
    { to: 'required', status: 'required', name: 'required', description: 'required' },
    { 'to.required': 'To is required', 'status.required': 'Status is required', 'name.required': 'Name is required', 'description.required': 'Description is required' },
  ),
  async function (req, res, next) {
    let id = req.params.id;

    const notificationLogAdminEditViewModel = require('../../view_models/notification_log_admin_edit_view_model');

    const viewModel = new notificationLogAdminEditViewModel(db.notification_log);

    const { to, status, name, description } = req.body;

    try {
      if (req.validationError) {
        return res.status(500).json({ success: false, message: req.validationError });
      }

      const resourceExists = await db.notification_log.getByPK(id);
      if (!resourceExists) {
        return res.status(404).json({ success: false, message: 'Notification log not found' });
      }

      const data = await db.notification_log.edit({ to, status, name, description }, id);

      if (!data) {
        return res.status(500).json({ success: false, message: 'Something went wrong' });
      }

      await db.activity_log.insert({
        action: 'EDIT',
        name: 'Admin_notification_log_controller.js',
        portal: 'admin',
        data: JSON.stringify({ to, status, name, description }),
      });

      return res.json({ success: true, message: 'Notification log edited successfully' });
    } catch (error) {
      return res.status(500).json({ success: false, message: 'Something went wrong' });
    }
  },
);

app.get('/admin/api/notification_logs-view/:id', JwtService.verifyTokenMiddleware(role), async function (req, res, next) {
  let id = req.params.id;

  const notificationLogAdminDetailViewModel = require('../../view_models/notification_log_admin_detail_view_model');

  const viewModel = new notificationLogAdminDetailViewModel(db.notification_log);

  try {
    const data = await db.notification_log.getByPK(id);

    if (!data) {
      return res.status(404).json({ message: 'Notification log not found', data: null });
    } else {
      const fields = {
        ...viewModel.detail_fields,
        id: data['id'] || '',
        to: data['to'] || '',
        status: data['status'] || '',
        name: data['name'] || '',
        description: data['description'] || '',
      };
      return res.status(200).json({ data: fields });
    }
  } catch (error) {
    return res.status(404).json({ message: 'Something went wrong', data: null });
  }
});

app.delete('/admin/api/notification_logs-delete/:id', JwtService.verifyTokenMiddleware(role), async function (req, res, next) {
  let id = req.params.id;

  const notificationLogAdminDeleteViewModel = require('../../view_models/notification_log_admin_delete_view_model');

  const viewModel = new notificationLogAdminDeleteViewModel(db.notification_log);

  try {
    const exists = await db.notification_log.getByPK(id);

    if (!exists) {
      return res.status(404).json({ success: false, message: 'Notification log not found' });
    }

    await db.notification_log.realDelete(id);

    await db.activity_log.insert({
      action: 'DELETE',
      name: 'Admin_notification_log_controller.js',
      portal: 'admin',
      data: JSON.stringify(exists),
    });

    return res.status(200).json({ success: true, message: 'Notification log deleted successfully' });
  } catch (error) {
    return res.status(500).json({ success: false, message: 'Something went wrong' });
  }
});

module.exports = app;
