const CsvService = require('./../../services/CsvService');

module.exports = {
  initializeApi: function (app) {
    app.post('/v1/api/file/import/:model', async function (req, res, next) {
      try {
        console.log(req.params.model);
        await CsvService.csv_import(req, res);
        return res.status(201).json({ saved: true });
      } catch (error) {
        console.log(error);
        return res.status(500).json({ message: 'error' });
      }
    });
    app.post('/v1/api/preview_csv/', async function (req, res, next) {
      try {
        let data = await CsvService.csv_preview(req, res);
        return res.status(201).json({ preview: true, data: data });
      } catch (error) {
        console.log(error);
        return res.status(500);
      }
    });
  },
};
