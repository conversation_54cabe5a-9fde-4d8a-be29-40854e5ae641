var $$ = Dom7;

/*************EXPENSES API ********************/

async function getActiveCohorts() {
  try {
    app.preloader.show();
    let response = await fetch('/api/cohort/', {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
    });
    let resJSON = await response.json();
    if (resJSON.data.length == 0) {
      throw "You don't have any active Co-Hort";
    }
    var dataCount = 0;
    resJSON.data.forEach((cohort) => {
      dataCount += 1;
      if (dataCount == 1) {
        html = `
                <option selected value="${cohort.name}" data-cohortid="${cohort.id}">${cohort.name}</option>
                `;
        $$('#expensesChoseCohortSelect .item-inner .item-after').html('');
        $$('#expensesChoseCohortSelect .item-inner .item-after').html(cohort.name);
      } else {
        html = `
                <option value="${cohort.name}" data-cohortid="${cohort.id}">${cohort.name}</option>
                `;
      }
      $$('#expenses-cohortselector').append(html);
    });
    FirstTimeGetCohortExpenses();
  } catch (error) {
    app.preloader.hide();
    console.log(error);
  }
}

async function getSettlements(cohort_id) {
  try {
    app.preloader.show();
    let res = await fetch(`/api/settlement/${cohort_id}`);
    console.log(res)
    const { settlements } = await res.json();
    let html = ``;
    settlements.forEach((settlement) => {
      html += `
      <div class="card singleExpense">
      <div class="card-header singleExpenseCardHeader">Settled at ${new Date(settlement.date).toLocaleString('en-us', {
        timeZone: 'UTC',
        month: 'short',
        day: 'numeric',
        year: 'numeric',
      })}
      </div>

      <div class="card-content card-content-padding expenseInfoContainer">
          <div class="row no-margin">
              <div class="col-60 viewFiles-infoCol">
                  <div class="block-title text-align-left info">
                  Paid by: ${settlement.parentSend.first_name}
                  </div>
             
                  <div class="block-title text-align-left info">
                  Receiver: ${settlement.parentReceive.first_name}
                  </div>
             
                  <div class="block-title text-align-left info">
                      Amount: $${parseInt(settlement.amount).toFixed(2)}
                  </div>
              </div>
          </div>
          </div>
      </div>
      `;
    });
    console.log(settlements);

    $$('#settlements-tab').append(html);

    app.preloader.hide();
  } catch (error) {
    app.preloader.hide();
    console.log(error);
  }
}

async function updateExp(status, id, cohort_id) {
  try {
    app.preloader.show();
    let res = await fetch(`/api/cohort/${cohort_id}/expense/${id}`, {
      method: 'PUT',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ status }),
    });
    FirstTimeGetCohortExpenses();
    app.preloader.hide();
  } catch (error) {
    app.preloader.hide();
    console.log(error);
  }
}

async function renderExps(resJSON, loggedinID, status, cohort_id) {
  if (status == 1) {
    app.preloader.show();
    resJSON = await fetch(`/api/expense/${cohort_id}/1`);
    resJSON = await resJSON.json();
    app.preloader.hide();
    console.log(resJSON);
  }

  resJSON.expensesObj.forEach((expense) => {
    html = `
    <div class="card singleExpense">
        <div class="card-header singleExpenseCardHeader">${expense.expense.expense_name}</div>
        <div class="card-content card-content-padding expenseInfoContainer">
            <div class="row no-margin">
                <div class="col-60 viewFiles-infoCol">
                    <div class="block-title text-align-left info">
                        Date: ${new Date(expense.expense.date).toLocaleString('en-us', { timeZone: 'UTC', month: 'short', day: 'numeric', year: 'numeric' })}
                    </div>
                    ${
                      expense.expense.paidBy.id == loggedinID.id
                        ? `
                      <div class="block-title text-align-left info">
                          You paid: $${parseInt(expense.expense.amount).toFixed(2)} 
                      </div>
                      `
                        : `
                      <div class="block-title text-align-left info">
                        ${expense.expense.paidBy.first_name} paid: $${parseInt(expense.expense.amount).toFixed(2)} 
                      </div>
                      `
                    }
                  
                    ${
                      expense.expense.paidBy.id == loggedinID.id
                        ? `
                    
                    <div class="block-title text-align-left info">
                        You lent: $${
                          expense.expense.paidBy.id != expense.parent2.id ? parseInt(expense.parent2.amountToPay).toFixed(2) : parseInt(expense.parent1.amountToPay).toFixed(2)
                        } 
                    </div>
                    
                    `
                        : `
                      <div class="block-title text-align-left info">
                      You owe: $${
                        expense.expense.paidBy.id != expense.parent2.id ? parseInt(expense.parent2.amountToPay).toFixed(2) : parseInt(expense.parent1.amountToPay).toFixed(2)
                      }
                      </div>
                      `
                    }
                </div>
                <div class="col-40 expenseInfoCallToAction">
                    <button class="button button-raised button-fill viewExpenseButton popup-open" data-expenseid="${expense.expense.id}" data-popup=".viewExpense-popup">
                            View
                    </button>
                    ${
                      expense.expense.receipt == null
                        ? ``
                        : `<a href="/uploads/${expense.expense.receipt}" class="external" download>
            <button class="button button-raised button-fill">
                Download receipt
            </button>
        </a>`
                    }
                    ${
                      expense.expense.paid_by != loggedinID.id
                        ? `${
                            expense.expense.status == 0
                              ? `
                      <button onclick="updateExp(1,${expense.expense.id},${cohort_id});" class="button button-raised button-fill color-red">
                        Move to dispute
                      </button>
                      `
                              : `
                      <button onclick="updateExp(0,${expense.expense.id},${cohort_id});" class="button button-raised button-fill">
                        Remove from dispute
                      </button>
                      `
                          }`
                        : ''
                    }
                    
                </div>
            </div>
        </div>
    </div>
`;
    console.log(status);
    if (status == 0) {
      $$('#expenses-tabs-supercontainer').append(html);
    } else {
      $$('#disputed-expenses').append(html);
    }
  });
}

async function FirstTimeGetCohortExpenses() {
  try {
    document.getElementById('settle-up-button').setAttribute('disabled', true);
    document.getElementById('settle-up-button').classList.add('disabled');
    document.getElementById('amount-payablecontainer').textContent = '';
    document.getElementById('expenses-tabs-supercontainer').textContent = '';
    document.getElementById('settlements-tab').textContent = '';
    document.getElementById('disputed-expenses').textContent = '';
    var selectedOption = document.getElementById('expenses-cohortselector');
    var id = selectedOption.options[selectedOption.selectedIndex].getAttribute('data-cohortid');

    let response = await fetch(`/api/expense/${id}/0`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
    });

    if (response.status == 200) {
      let loggedinID = JSON.parse(localStorage.getItem('user'));
      await getSettlements(id, loggedinID);
      let loggedParentID;
      let resJSON = await response.json();
      console.log(resJSON);
      if (resJSON.parent1.id == loggedinID.id) {
        loggedParentID = loggedinID.id;
      } else {
        loggedParentID = loggedinID.id;
      }
      renderExps(resJSON, loggedinID, 1, id);
      renderExps(resJSON, loggedinID, 0, id);
      ExpensesgetNewRemainingAmount();
      $$('.viewExpenseButton').on('click', function (e) {
        console.log(this);
        expenseDetails(this.dataset.expenseid, resJSON.expensesObj);
      });
      app.preloader.hide();
    } else if (response.status == 404) {
      html = `
                    <div class="text-align-center amount-container-text">
                        N/A
                    </div>
                `;
      $$('#amount-payablecontainer').append(html);

      html = `
                    <div class="card">
                        <div class="block-title text-align-center padding-vertical">You don't have any Co-Expenses to show, Add Co-Expenses to your Co-Hort to see them here.</div>
                    </div>
                `;
      $$('#expenses-tabs-supercontainer').append(html);
      app.preloader.hide();
    }
    watchChangesOnCohort();
  } catch (error) {
    app.preloader.hide();
    console.log(error);
  }
}

function watchChangesOnCohort() {
  try {
    $$('#expenses-cohortselector').on('change', async (e) => {
      app.preloader.show();
      document.getElementById('amount-payablecontainer').textContent = '';
      document.getElementById('expenses-tabs-supercontainer').textContent = '';
      document.getElementById('settlements-tab').textContent = '';
      document.getElementById('disputed-expenses').textContent = '';
      var selectedOption = document.getElementById('expenses-cohortselector');
      var id = selectedOption.options[selectedOption.selectedIndex].getAttribute('data-cohortid');

      let response = await fetch(`/api/expense/${id}/0`, {
        method: 'GET',
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
        },
      });

      if (response.status == 200) {
        let loggedinID = JSON.parse(localStorage.getItem('user'));
        await getSettlements(id);
        let loggedParentID;
        let resJSON = await response.json();
        console.log(resJSON);

        if (resJSON.parent1.id == loggedinID.id) {
          loggedParentID = loggedinID.id;
        } else {
          loggedParentID = loggedinID.id;
        }

        renderExps(resJSON, loggedinID, 1, id);
        renderExps(resJSON, loggedinID, 0, id);

        ExpensesgetNewRemainingAmount();

        $$('.viewExpenseButton').on('click', function (e) {
          console.log(this);
          expenseDetails(this.dataset.expenseid, resJSON.expensesObj);
        });
        app.preloader.hide();
      } else if (response.status == 404) {
        html = `
                    <div class="text-align-center amount-container-text">
                        N/A
                    </div>
                `;
        $$('#amount-payablecontainer').append(html);

        html = `
                    <div class="card">
                        <div class="block-title text-align-center padding-vertical">You don't have any Co-Expenses to show, Add Co-Expenses to your Co-Hort to see them here.</div>
                    </div>
                `;
        $$('#expenses-tabs-supercontainer').append(html);
        app.preloader.hide();
      }
    });
  } catch (error) {
    app.preloader.hide();
    console.log(error);
  }
}
let newAmount = 0;

async function ExpensesgetNewRemainingAmount() {
  try {
    var selectedOption = document.getElementById('expenses-cohortselector');
    var id = selectedOption.options[selectedOption.selectedIndex].getAttribute('data-cohortid');

    let response = await fetch(`/api/expense/${id}/0`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
    });

    let resJSON = await response.json();

    let cohort = await fetch(`/api/cohort/${id}`);
    cohort = await cohort.json();
    console.log(cohort);
    if (response.status == 200) {
      let loggedinID = JSON.parse(localStorage.getItem('user'));
      let loggedParentID;

      if (resJSON.parent1.id == loggedinID.id) {
        loggedParentID = loggedinID.id;
      } else {
        loggedParentID = loggedinID.id;
      }
      let owes = 0;
      let p2Name = '';
      if (loggedinID.id == resJSON.parent1?.id) {
        newAmount = parseInt(resJSON.parent1?.amountToSettleP1);
        owes = parseInt(resJSON.parent2?.amountToSettleP2);
        p2Name = cohort.cohort?.parent2?.first_name;
      } else {
        newAmount = parseInt(resJSON.parent2.amountToSettleP2);
        owes = parseInt(resJSON.parent1?.amountToSettleP1);
        p2Name = cohort.cohort?.parent1?.first_name;
      }

      if (newAmount > 0) {
        document.getElementById('settle-up-button').removeAttribute('disabled');
        document.getElementById('settle-up-button').classList.remove('disabled');
      } else {
        document.getElementById('settle-up-button').setAttribute('disabled', true);
        document.getElementById('settle-up-button').classList.remove('disabled');
      }

      document.getElementById('amount-payablecontainer').textContent = '';
      html = `
                <div class="text-align-center amount-container-text">
                    <div>
                    You owe : <b> $${newAmount.toFixed(2)} </b>
                    </div>
                    
                    ${p2Name ? `<div>${p2Name} owes you : <b> $${owes.toFixed(2)} </b> </div>` : ''}
                    
                </div>  
                `;
      $$('#amount-payablecontainer').append(html);
    }
  } catch (error) {
    console.log(error);
  }
}

function intializeInput() {
  app.preloader.show();
  document.getElementById('settleup-popup-amountpaid').value = '';
  var today = new Date();
  var dd = today.getDate() + 1;
  var mm = today.getMonth() + 1;
  var yyyy = today.getFullYear();
  if (dd < 10) {
    dd = '0' + dd;
  }
  if (mm < 10) {
    mm = '0' + mm;
  }
  today = yyyy + '-' + mm + '-' + dd;
  var calendar = app.calendar.create({
    inputEl: '#settleup-popup-datepaid',
    disabled: {
      from: today,
    },
    dateFormat: {
      month: 'long',
      day: 'numeric',
      year: 'numeric',
    },
  });
  var totalAmountToBePaid = 0;

  document.getElementById('settleup-popup-amountpaid').value = newAmount.toFixed(2);

  app.preloader.hide();
}

async function settleUpExpense() {
  try {
    app.preloader.show();
    var mainview = app.view.main;
    let expense = [];
    let date = document.getElementById('settleup-popup-datepaid').value;
    let amount = document.getElementById('settleup-popup-amountpaid').value;
    var coID = document.getElementById('expenses-cohortselector').options[document.getElementById('expenses-cohortselector').selectedIndex].getAttribute('data-cohortid');
    let paymentmethood = document.getElementById('settleup-popup-amountpaid').value;
      document.getElementById('settleup-popup-paymentmethod')
      .options[document.getElementById('settleup-popup-paymentmethod').selectedIndex].getAttribute('data-paymentmethod');
    let paid_by = document.getElementById('settleup-popup-paid-by').value;
    var settleUpDateForServer = new Date(date);
    settleUpDateForServer.setHours(0);
    settleUpDateForServer.setMinutes(0);
    settleUpDateForServer.setSeconds(0);
    settleUpDateForServer.toString();
    // console.log(date);
    // console.log(settleUpDateForServer);

    let body = {
      amount: amount,
      payment_method: paymentmethood,
      cohort_id: coID,
      date: settleUpDateForServer,
      user_id: paid_by
    };
    console.log(body);
    let response = await fetch('/api/settlement/add/', {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });
    resJSON = await response.json();
    if (response.status == 200) {
      app.preloader.hide();
      app.dialog.alert('Settlement created');
      app.popup.close('.settleup-popup', true);
      FirstTimeGetCohortExpenses();
      mainview.router.navigate({ name: 'expenses' });
      // populateSettleUp();
      // getNewRemainingAmount();
    } else {
      app.preloader.hide();
      throw "Couldn't add Settlement, Please try again";
    }
  } catch (error) {
    app.preloader.hide();
    app.dialog.alert(error);
  }
}

//******* ADD EXPENSES API ************//

async function init() {
  try {
    app.preloader.show();
    var today = new Date();
    var dd = today.getDate() + 1;
    var mm = today.getMonth() + 1;
    var yyyy = today.getFullYear();
    if (dd < 10) {
      dd = '0' + dd;
    }
    if (mm < 10) {
      mm = '0' + mm;
    }
    today = yyyy + '-' + mm + '-' + dd;
    console.log(today);
    var calendar = app.calendar.create({
      inputEl: '#addexpense-date',
      disabled: {
        from: today,
      },
      dateFormat: {
        month: 'long',
        day: 'numeric',
        year: 'numeric',
      },
    });
    document.getElementById('addexpenses-cohortselector').textContent = '';
    let response = await fetch('/api/cohort/', {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
    });
    let resJSON = await response.json();
    if (response.status == 200) {
      var dataCount = 0;
      resJSON.data.forEach((cohort) => {
        dataCount += 1;
        if (dataCount == 1) {
          html = `
                <option selected value="${cohort.name}" data-cohortid="${cohort.id}">${cohort.name}</option>
                `;
          $$('.addexpensesChoseCohortSelect .item-inner .item-after').html('');
          $$('.addexpensesChoseCohortSelect .item-inner .item-after').html(cohort.name);
        } else {
          html = `
                    <option value="${cohort.name}" data-cohortid="${cohort.id}">${cohort.name}</option>
                    `;
        }
        $$('#addexpenses-cohortselector').append(html);
      });
      populateInputFields();
    } else {
      throw resJSON.message;
    }
  } catch (error) {
    // app.dialog.alert(error);
    console.log(error);
  }
}

async function populateInputFields() {
  try {
    app.preloader.show();
    document.getElementById('addexpenses-cohortchildren').textContent = '';
    document.getElementById('addexpenses-parentselector').textContent = '';
    let coid = document.getElementById('addexpenses-cohortselector').options[document.getElementById('addexpenses-cohortselector').selectedIndex].getAttribute('data-cohortid');
    let loginParent = JSON.parse(localStorage.getItem('user'));
    let response = await fetch(`/api/user/children/cohort/${coid}`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
    });
    let resJSON = await response.json();
    console.log(resJSON);
    var dataCount = 0;

    resJSON.children.forEach((children) => {
      dataCount += 1;
      if (dataCount == 1) {
        html = `
                    <option selected value="${children.name}" data-childid="${children.id}">${children.name}</option>
                `;
        $$('.addexpensesSelectChildSelect .item-inner .item-after').html(children.name);
      } else {
        html = `
                    <option value="${children.name}" data-childid="${children.id}">${children.name}</option>
                `;
      }
      $$('#addexpenses-cohortchildren').append(html);
    });

    let responseCohort = await fetch('/api/cohort/', {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
    });
    let resJSONCohort = await responseCohort.json();
    console.log(resJSONCohort);
    if (responseCohort.status == 200) {
      resJSONCohort.data.forEach((cohort) => {
        if (cohort.id == coid) {
          console.log(cohort);
          if (cohort.parent_2_info != undefined || cohort.parent_2_info != null) {
            htmlParent = `
                        <option selected value="${cohort.parent_1_info.first_name}" data-parentNumber="${cohort.parent_1}" data-coparentid="${cohort.parent_1_info.id}">${
              cohort.parent_1_info.first_name
            }</option>
                        <option value=" ${cohort?.parent_2_info?.first_name || 'TBD'}" data-parentNumber="${cohort.parent_2}" data-coparentid="${
              cohort?.parent_2_info?.id || 'TBD'
            }">${cohort?.parent_2_info?.first_name || 'TBD'}</option>
                        `;
            $$('.addexpensesPaidBySelect .item-inner .item-after').html(cohort.parent_1_info.first_name);
            document.getElementById('addExpense-parent1-split').value = cohort.parent_1_default_split;
            document.getElementById('addExpense-parent2-split').value = cohort.parent_2_default_split;
            $$('#addexpenses-parentselector').append(htmlParent);
            if (loginParent.id == cohort.parent_1) {
              document.getElementById('addExpensesParentOneSplit').textContent = 'For Expense: Default Split for You';
              document.getElementById('addExpensesParentTwoSplit').textContent = 'For Expense: Default Split for Your Co-Parent';
            } else {
              document.getElementById('addExpensesParentOneSplit').textContent = 'For Expense: Default Split for Your Co-Parent';
              document.getElementById('addExpensesParentTwoSplit').textContent = 'For Expense: Default Split for You';
            }
          } else {
            htmlParent = `
                        <option selected value="${cohort.parent_1_info.first_name}" data-parentNumber="${cohort.parent_1}" data-coparentid="${cohort.parent_1_info.id}">${cohort.parent_1_info.first_name}</option>
                        `;
            $$('.addexpensesPaidBySelect .item-inner .item-after').html(cohort.parent_1_info.first_name);
            document.getElementById('addExpense-parent1-split').value = cohort.parent_1_default_split;
            document.getElementById('addExpense-parent2-split').value = cohort.parent_2_default_split;
            $$('#addexpenses-parentselector').append(htmlParent);
            if (loginParent.id == cohort.parent_1) {
              document.getElementById('addExpensesParentOneSplit').textContent = 'For Expense: Default Split for You';
              document.getElementById('addExpensesParentTwoSplit').textContent = 'For Expense: Default Split for Your Co-Parent';
            } else {
              document.getElementById('addExpensesParentOneSplit').textContent = 'For Expense: Default Split for Your Co-Parent';
              document.getElementById('addExpensesParentTwoSplit').textContent = 'For Expense: Default Split for You';
            }
          }
        }
      });
      app.preloader.hide();
    } else {
      app.preloader.hide();
      throw resJSONCohort.message;
    }
  } catch (error) {
    app.preloader.hide();
    // app.dialog.alert(error);
    console.log(error);
  }
}

async function addExpenses() {
  try {
    app.preloader.show();
    var mainview = app.view.main;
    var cohortID = document.getElementById('addexpenses-cohortselector').options[document.getElementById('addexpenses-cohortselector').selectedIndex].getAttribute('data-cohortid');
    var paidBy = document
      .getElementById('addexpenses-parentselector')
      .options[document.getElementById('addexpenses-parentselector').selectedIndex].getAttribute('data-parentNumber');
    var childName = document.getElementById('addexpenses-cohortchildren').options[document.getElementById('addexpenses-cohortchildren').selectedIndex].getAttribute('value');
    var expenseDate = document.getElementById('addexpense-date').value;
    var amount = document.getElementById('addExpense-amount').value;
    var parent1_split = document.getElementById('addExpense-parent1-split').value;
    var parent2_split = document.getElementById('addExpense-parent2-split').value;
    var notes = document.getElementById('addExpense-notes').value;
    var name = document.getElementById('addexpense-name').value;
    var reciept = document.getElementById('upload-reciepts');
    var formData = new FormData();
    var fileObject = reciept.files[0] || 'N/A';
    var expenseDateForServer = new Date(expenseDate);
    expenseDateForServer.setHours(0);
    expenseDateForServer.setMinutes(0);
    expenseDateForServer.setSeconds(0);
    console.log(expenseDateForServer);
    console.log(reciept);
    console.log(fileObject);
    formData.append('receipt', fileObject);
    formData.append('cohort_id', cohortID);
    formData.append('paid_by', paidBy);
    formData.append('amount', amount);
    formData.append('children', childName);
    formData.append('parent_1_split', parent1_split);
    formData.append('parent_2_split', parent2_split);
    formData.append('notes', notes);
    formData.append('expense_name', name);
    formData.append('date', expenseDateForServer);
    const dataObj = {
      cohort_id: cohortID,
      paid_by: paidBy,
      amount,
      children: childName,
      parent_1_split: parent1_split,
      parent_2_split: parent2_split,
      notes,
      expense_name: name,
      date: expenseDate,
    };
    if (document.querySelectorAll('.input-invalid').length == 0 && addExpenseValidateField(name, expenseDate, amount, parent1_split, parent2_split, notes)) {
      let response = await fetch(`/api/expenses-add`, {
        method: 'POST',
        body: formData,
      });
      let resJSON = await response.json();
      console.log(resJSON);
      if (response.status == 201) {
        app.preloader.hide();
        app.dialog.alert('Co-Expense added');
        mainview.router.navigate({ name: 'expenses' });
      } else {
        app.preloader.hide();
        throw resJSON.message;
      }
    } else {
      app.preloader.hide();
      throw 'Every field is required to create Co-Expense';
    }
  } catch (error) {
    app.preloader.hide();
    app.dialog.alert(error);
  }
}

function addExpenseValidateField(name, expenseDate, amount, parent1_split, parent2_split, notes) {
  let AllFieldsValidated = true;
  let errortoThrow = 'These field(s) are required: <br>';
  let parent1Split = parent1_split;
  let parent2Split = parent2_split;
  if (name == '') {
    AllFieldsValidated = false;
    errortoThrow += 'Co-Expense Name <br>';
  }
  if (expenseDate == '') {
    AllFieldsValidated = false;
    errortoThrow += 'Co-Expense Date <br>';
  }
  if (amount == '') {
    AllFieldsValidated = false;
    errortoThrow += 'Co-Expense Amount <br>';
  }
  if (parent1_split == '' || parent2_split == '') {
    AllFieldsValidated = false;
    errortoThrow += `Co-Parent(s) Split(s) <br>`;
  }
  if (notes == '') {
    AllFieldsValidated = false;
    errortoThrow += 'Co-Expense Notes <br>';
  }
  if (AllFieldsValidated == false) {
    throw errortoThrow;
  }
  if (parseInt(parent1Split) + parseInt(parent2Split) !== 100) {
    errortoThrow = 'The split should not be less than or greater than 100%';
    throw errortoThrow;
  }
  return AllFieldsValidated;
}

/***********EXPENSE DETAILS POPUP API ***************/

function expenseDetails(exID, resJSON) {
  console.log(resJSON);
  console.log(exID);
  document.getElementById('expenseContainer').textContent = '';
  let expense = resJSON.find((o) => o.expense.id == exID);
  let loggedinID = JSON.parse(localStorage.getItem('user'));

  html = `
    <div class="card no-margin">
        <div class="card-header justify-content-center expenseReport-header">${expense.expense.expense_name}</div>
        <div class="card-content padding">
        <div class="row">
                    <div class="col-50">
                        <p>
                            Co-Expense Date:
                        </p>
                    </div>
                    <div class="col-50">
                        <p class="text-align-right">
                            ${new Date(expense.expense.date).toLocaleString('en-us', { timeZone: 'UTC', month: 'short', day: 'numeric', year: 'numeric' })}
                        </p>
                    </div>
                </div>

                <div class="row">
                    <div class="col-50">
                        <p>
                            Child's Name:
                        </p>
                    </div>
                    <div class="col-50">
                        <p class="text-align-right">
                            ${expense.expense.children}
                        </p>
                    </div>
                </div>

                <div class="row">
                    <div class="col-50">
                        <p>
                            Co-Expense Total:
                        </p>
                    </div>
                    <div class="col-50">
                        <p class="text-align-right">
                            $${parseInt(expense.expense.amount).toFixed(2)}
                        </p>
                    </div>
                </div>

                <div class="row">
                    <div class="col-50">
                        <p>
                            Split Amount:
                        </p>
                    </div>
                    <div class="col-50">
                        <p class="text-align-right">
                            $${loggedinID.id == expense.parent2.id ? parseInt(expense.parent2.amountToPay).toFixed(2) : parseInt(expense.parent1.amountToPay).toFixed(2)}
                        </p>
                    </div>
                </div>

                <div class="row">
                    <div class="col-50">
                        <p>
                            Paid By:
                        </p>
                    </div>
                    <div class="col-50">
                        <p class="text-align-right">
                            ${expense.expense.paidBy != null ? expense.expense.paidBy.first_name : 'NA'}
                        </p>
                    </div>
                </div>

                <div class="row">
                    <div class="col-50">
                        <p>
                            Notes:
                        </p>
                    </div>
                    <div class="col-50">
                        <p class="text-align-right expense-details-pop-note">
                            ${expense.expense.notes}
                        </p>
                    </div>
                </div>
        </div>
    </div>       
        `;
  $$('#expenseContainer').append(html);
}
