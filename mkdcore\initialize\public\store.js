// save store as global object
window.store = Framework7.createStore({
    state: {
        cohortID: 0,
        sharedEmail: "",
        childName: [],
        parentID: 0,
        activeCohort: -1,
        activeChild: -1,
    },
    actions: {
        setcohortID({ state }, coID) {
            state.cohortID = coID;
        },
        setSharedEmail({ state }, mail) {
            state.sharedEmail = mail;
        },
        setChildName({ state }, name) {
            state.childName.push(name);
        },
        setParentID({ state }, parentID) {
            state.parentID = parentID;
        },
        setActiveCohort({ state }, cohortID) {
            state.activeCohort = cohortID;
        },
        setActiveChild({ state }, childID) {
            state.activeChild = childID;
        }
    },
    getters: {
        cohortID({ state }) {
            return state.cohortID;
        },
        sharedEmail({ state }) {
            return state.sharedEmail;
        },
        childName({ state }) {
            return state.childName;
        },
        parentID({ state }) {
            return state.parentID;
        },
        activeCohort({ state }) {
            return state.activeCohort;
        },
        activeChild({ state }) {
            return state.activeChild;
        }
    },
})


