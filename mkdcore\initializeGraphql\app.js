'use strict';
/*Powered By: Manaknightdigital Inc. https://manaknightdigital.com/ Year: 2020*/
/**
 * App
 * @copyright 2020 Manaknightdigital Inc.
 * @link https://manaknightdigital.com
 * @license Proprietary Software licensing
 * <AUTHOR>
 *
 */
require('dotenv').config();

const express = require('express');
const { ApolloServer, gql } = require('apollo-server-express');
const fs = require('fs');
const path = require('path');
const body_parser = require('body-parser');
const logger = require('morgan');
const helmet = require('helmet');
const PowerByService = require('./services/PowerByService');
const cookieParser = require('cookie-parser');
// const route = require('./controllers/index');
// const authRoute = require('./routes');
const db = require('./models');
const typeDefs = fs.readFileSync(path.join(__dirname, '/types/schema.graphql'), 'utf8');
const jwtService = require('./services/JwtService');

const resolvers = require('./resolvers');

const { AuthenticationError } = require('apollo-server-express');

const server = new ApolloServer({
  typeDefs,
  resolvers,
  context: async ({ req }) => {
    const token = req.headers.authorization;
    const cleanToken = token.replace('Bearer ', '');
    const verify = jwtService.verifyAccessToken(cleanToken);

    if (!verify) {
      throw new AuthenticationError('Token Not Match');
    }

    if (verify.credential.role_id !== 1) {
      throw new AuthenticationError('Access Denied');
    }

    return {
      authScope: verify.credential.id,
      credential: verify.credential,
      user: verify.user,
      db: db,
    };
  },
  formatError: (err) => {
    if (err.message.indexOf('Invalid Credential') > -1) {
      return new Error('Invalid Credential');
    }
    if (err.message.indexOf('Authorization Token Invalid') > -1) {
      return new Error('Authorization Token Invalid');
    }
    if (err.message.indexOf('Authorization Invalid Role') > -1) {
      return new Error('Authorization Invalid Role');
    }

    if (err.message.indexOf('Access Denied') > -1) {
      return new Error('Access Denied');
    }
    return err;
  },
});

let app = express();
app.get('/version', (req, res) => res.status(200).json({ version: process.env.VERSION }));

if (process.NODE_ENV === 'maintenance') {
  app.all('*', (req, res) => {
    res.status(503).json({ message: 'website under maintenance' });
  });
}

app.set('iocContainer', process.env);
app.set('db', db);
app.use(body_parser.json({ limit: '50mb' }));
app.use(logger('dev'));
app.use(express.json());
app.use(
  express.urlencoded({
    extended: false,
  }),
);
app.use(helmet());

app.use(PowerByService);

// console.log(app.locals);
// error handler
app.use((err, req, res, next) => {
  // set locals, only providing error in development
  res.locals.message = err.message;
  res.locals.error = req.app.get('env') === 'development' ? err : {};

  // render the error page
  res.status(err.status || 500);
  res.json({
    message: err.message,
  });
});

// {{{url_redirect}}}

const uri = '/graphql';
server.applyMiddleware({ app, uri });
module.exports = app;
