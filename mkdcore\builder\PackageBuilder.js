/*Powered By: Manaknightdigital Inc. https://manaknightdigital.com/ Year: 2021*/
/**
 * Copy builder
 * @copyright 2020 Manaknightdigital Inc.
 * @link https://manaknightdigital.com
 * @license Proprietary Software licensing
 * <AUTHOR>
 *
 */

const fs = require('fs');
const path = require('path');
const { config } = require('process');
const Builder = require('./Builder');

module.exports = function (main_configuration) {
  Builder.call(this);

  this._config = main_configuration;
  this._render_list = [];
  this._packages = [];

  /**
   * List over packages and push activated packages to this._packages
   * @param {object} packages config packages
   */
  this.set_packages = function (packages) {
    for (const package in packages) {
      if (packages[package]) {
        this._packages.push({ [package]: packages[package] });
      }
    }
  };

  this.get_config = function () {
    return this._config;
  };

  this.build = function () {
    this._packages.forEach((package, index) => {
      switch (package[index]) {
        case 'analytics':
          console.log('TODO analytics');
          break;
        case 'payment': {
          console.log('Payment Package');
          const payment = require('./../source/packages/payment/index');
          payment._config = this.get_config();
          payment.build();
          break;
        }
        case 'marketing': {
          console.log('Marketing Package');
          const marketing = require('./../source/packages/marketing/index');
          break;
        }
        case 'cache':
          console.log('TODO cache');
          break;
        case 'pdf':
          console.log('TODO analypdftics');
          break;
        case 'voice':
          console.log('TODO voice');
          break;
        case 'url_redirect':
          if (package.url_redirect.active) {
            const UrlRedirect = require('../source/packages/url_redirect');
            UrlRedirect.main(package.permission);
          } else {
            const UrlRedirect = require('../source/packages/url_redirect');
            UrlRedirect.default(package.permission);
          }
          break;
        case 'contact':
          break;
        case 'multi-tenant':
          break;
        case 'permission':
          break;
        case 'profile':
          if (package.profile.active) {
            const Profile = require('../source/packages/profile');
            const profile = new Profile(this._config);
            profile.build(package.profile.portals);
          }
          break;
        case 'analytics':
          break;
      }

      // if (package.contact) {
      //   const Contact = require('../source/packages/contact');
      //   const contact = new Contact();
      //   this._config = contact.build(package.contact, main_configuration);
      // }
      // if (package.contact) {
      //   const Contact = require('../source/packages/contact');
      //   const contact = new Contact();
      //   this._config = contact.build(package.contact, main_configuration);
      // }
      // if (package.profile.active) {
      //   const Profile = require('../source/packages/profile');
      //   const profile = new Profile(main_configuration);
      //   profile.build(package.profile);
      // }
    });

    ['release', 'releaseGraphql'].forEach((folder) => {
      // let fileDirectoryPath = path.dirname(
      //   `../${folder}/configuration.dist.json`,
      // );
      // if (!fs.existsSync(fileDirectoryPath)) {
      //   fs.mkdirSync(fileDirectoryPath);
      // }
      this.createDirectoriesRecursive(`../../${folder}/configuration.dist.json`);
      fs.writeFileSync(path.join(__dirname, `../../${folder}/configuration.dist.json`), JSON.stringify(this._config), {
        mode: 0775,
      });
    });
  };

  // this.build = function () {
  //   this._packages.forEach((package) => {
  //     switch (package) {
  //       case 'url_redirect':
  //         const url_redirect_config = require('../source/packages/url_redirect/config.json');
  //         this.append_config('url_redirect', url_redirect_config);
  //         break;

  //       case 'contact':
  //         const contactConfig = require('../source/packages/contact/config.json');
  //         this.append_config('contact', contactConfig);

  //       case 'analytics':
  //         break;
  //       case 'payment':
  //         break;
  //       case 'cache':
  //         break;
  //       case 'pdf':
  //         break;
  //       case 'voice':
  //         break;
  //     }
  //   });

  //   if (package.url_redirect) {
  //     const UrlRedirectBuilder = require('../source/packages/url_redirect/index');
  //     const urlRedirectBuilder = new UrlRedirectBuilder();
  //     this._config = urlRedirectBuilder.build(
  //       package.url_redirect,
  //       main_configuration,
  //     );
  //   } else {
  //     ['release', 'releaseGraphql'].forEach((folder) => {
  //       let appJs = fs.readFileSync(`../${folder}/app.js`, {
  //         encoding: 'utf-8',
  //       });

  //       appJs = this.inject_substitute(appJs, 'url_redirect', '');

  //       fs.writeFileSync(`../${folder}/app.js`, appJs, {
  //         mode: 0775,
  //       });
  //     });
  //   }
  // };

  this.destroy = function () {};
};
