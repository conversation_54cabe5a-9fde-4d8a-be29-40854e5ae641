const { request, response } = require('express');

const authService = require('../services/AuthService');
const jwtService = require('../services/JwtService');

const errors = {
  NOT_AUTHORIZED: 'NOT_AUTHORIZED',
};

module.exports = {
  /**
   * Forgot password router
   * @param {request} req
   * @param {response} res
   */
  get: async function (req, res) {
    try {
      const token = req.headers.authorization;
      const cleanToken = token.replace('Bearer ', '');

      const verify = jwtService.verifyAccessToken(cleanToken);

      if (!verify) throw new Error(errors.NOT_AUTHORIZED);

      await authService.resetPassword(req.body.password, verify.credential);

      res.status(200).json({ success: true });
    } catch (error) {
      res.status(500).json({ success: false, message: error.message });
    }
  },
};
