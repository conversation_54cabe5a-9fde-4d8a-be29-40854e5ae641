<!DOCTYPE html>
<html>

<head>
    <!-- Required meta tags-->
    <meta charset="utf-8">
    <meta name="viewport"
        content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no, viewport-fit=cover">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <!-- Color theme for statusbar (Android only) -->
    <meta name="theme-color" content="#2196f3">
    <!-- Your app title -->
    <title>Co-Parent</title>
</head>

<body>
    <!-- App root element -->
    <div id="app">
        <div class="page" data-name="expenses">

            <!-- Top Navbar -->
            <div class="navbar">
                <div class="navbar-bg"></div>
                <div class="navbar-inner">
                    <div class="left">
                        <a href="#" class="panel-toggle color-black">&#9776;</a>
                    </div>
                    <div class="title text-align-center">
                        Co-Expenses
                    </div>
                    <div class="right">
                        <a href="/addExpense/"><i class="f7-icons color-black">plus_circle</i></a>
                    </div>
                </div>
            </div>

            <!-- Scrollable page content -->
            <div class="page-content">

                <div class="list no-hairlines no-margin-vertical">
                    <ul class="page-start-padding">
                        <li class="smart-select-list justify-content-flex-end">
                            <div class="item-title item-label smart-select-label">Choose Co-Hort</div>
                            <a class="item-link smart-select smart-select-init expensePageCohortList"
                                data-open-in="sheet" data-close-on-select="true">
                                <select name="choseCohort" id="expenses-cohortselector">

                                </select>
                                <div class="item-content expensesChoseCohortSelect" id="expensesChoseCohortSelect">
                                    <div class="item-inner">
                                        <div class="item-title">Choose Co-Hort</div>
                                    </div>
                                </div>
                            </a>
                        </li>

                        <div class="card expenseRemainingCard">
                            <div class="card-header expenseRemainCardHeader">Expenses balance</div>
                            <div class="card-content card-content-padding">
                                <div class="row">
                                    <div class="col-50" id="amount-payablecontainer">
                                        <!--This is a Placeholder, Value is coming from Javascript-->
                                        <div class="text-align-center amount-container-text">
                                            
                                        </div>
                                    </div>
                                    <div class="col-50">
                                       
                                        <button id="settle-up-button" data-popup=".settleup-popup" onclick="intializeInput()" class="popup-open button button-raised button-fill disabled">Settle up</button>
                                          
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="segmented segmented-raised tab-switch-container">
                            <a href="#expenses-tabs-supercontainer" class="tab-link button button-outline">
                                Expenses
                            </a>
                            <a href="#settlements-tab" class="tab-link button button-outline">
                                Settlements
                            </a>
                            <a href="#disputed-expenses" class="tab-link button button-outline">
                                Disputed
                            </a>
                        </div>

                        <div class="tabs-animated-wrap">
                            <div class="tabs">

                                <div class="expenses-tabs-supercontainer tab tab-active"
                                    id="expenses-tabs-supercontainer">
                                    <!--This is Skeleton, it is not being rendered. The output coming from Javascript-->

                                </div>

                                <div class="expenses-tabs-supercontainer tab" id="settlements-tab">
                                    <!--This is a dummy text, Data is coming from Javascript-->

                                </div>
                                <div class="expenses-tabs-supercontainer tab" id="disputed-expenses">
                                    <!--This is a dummy text, Data is coming from Javascript-->

                                </div>

                            </div>
                        </div>

                    </ul>
                </div>

            </div>

            <div class="popup viewExpense-popup">
                <div class="page-content">
                    <div class="block">
                        <div class="list viewExpense-popup-list">
                            <div id="expenseContainer">
                                <!--This is a Skeleton, Data is rendered through JavaScript-->

                            </div>
                        </div>
                        <button class="button button-raised button-fill view-expense-details-close-button popup-close">
                            Close Co-Expense Report
                        </button>
                    </div>
                </div>
            </div>

            <div class="popup settleup-popup">
                <div class="block">
                  <span class="settleup-popup-close-button">
                    <a href="" class="popup-close">
                      <i class="f7-icons color-black custom-size-20">xmark</i>
                    </a>
                  </span>
                  <div class="list no-hairlines settleup-popup-list">
                    <ul>
                      <li>
                        <div class="item-content item-input">
                          <div class="item-inner">
                            <div class="item-title item-label custom-date-label">Date</div>
                            <div class="item-input-wrap addexpense-date">
                              <input type="text" placeholder="Select Date" readonly="readonly" id="settleup-popup-datepaid" required validate validate-on-blur="true" />
                            </div>
                          </div>
                        </div>
                      </li>
      
                      <li class="item-content item-input item-input-outline">
                        <div class="item-inner">
                          <div class="item-title item-label">Amount</div>
                          <div class="item-input-wrap">
                            <input type="number" name="amount" id="settleup-popup-amountpaid" disabled />
                            <span class="input-clear-button"></span>
                          </div>
                        </div>
                      </li>
      
                      <li class="smart-select-list">
                        <div class="item-title item-label smart-select-label">Payment Method</div>
                        <a class="item-link smart-select smart-select-init" data-open-in="sheet" data-close-on-select="true">
                          <select placeholder="Please choose..." id="settleup-popup-paymentmethod">
                            <option value="etransfer" data-paymentmethod="1">E Transfer</option>
                            <option value="creditcard" data-paymentmethod="2">Credit Card</option>
                            <option value="debit" data-paymentmethod="3">Debit</option>
                            <option value="banktransfer" data-paymentmethod="4">Bank Transfer</option>
                          </select>
                          <div class="item-content expensesChoseCohortSelect">
                            <div class="item-inner">
                              <div class="item-title">Payment Method</div>
                            </div>
                          </div>
                        </a>
                      </li>
      
                      <a onclick="settleUpExpense()" target="_blank">
                        <button class="button button-raised button-fill settleup-popup-submitbutton">Submit</button>
                      </a>
                    </ul>
                  </div>
                </div>
              </div>
        </div>
</body>

</html>