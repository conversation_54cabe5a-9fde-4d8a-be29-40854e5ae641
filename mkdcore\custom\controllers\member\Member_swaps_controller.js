const app = require('express').Router();
const { Op } = require('sequelize');
const sequelize = require('sequelize');
const db = require('../../models');
const { protect } = require('../../middlewares/auth_middleware.js');
const UploadService = require('../../services/UploadService');
const upload = UploadService.upload('files/file');

app.post('/api/user/request-swap', protect, async function (req, res, next) {
  try {
    const { user } = req;
    const { cohort, date, time_type, quantity, notes } = req.body;
    const belongToCohort = await db.cohort.findOne({
      where: {
        [Op.or]: {
          parent_1: user.id,
          parent_2: user.id,
        },
        id: cohort,
      },
    });
    if (!belongToCohort) {
      return res.status(404).json({
        status: 'fail',
        message: 'Cohort not found',
      });
    }
    const swapExisted = await db.swap.findOne({ where: { time_type, date, cohort_id: cohort } });
    if (swapExisted) return res.status(400).json({ message: 'Swap already requested on this date' });
    const swap = await db.swap.insert({ cohort_id: cohort, date, time_type, quantity, notes, status: belongToCohort.parent_2 ? 1 : 0, request_from: user.id });

    return res.status(200).json({
      status: 'success',
      swap,
    });
  } catch (error) {
    console.log(error);
    return res.status(400).json({
      error: true,
      message: error.message,
    });
  }
});

app.delete('/api/user/swap/:swap_id', protect, async function (req, res, next) {
  try {
    const { user } = req;
    const { swap_id } = req.params;

    const swap = await db.swap.findByPk(swap_id);
    await swap.destroy();

    return res.status(200).json({
      status: 'success',
      swap,
    });
  } catch (error) {
    console.log(error);
    return res.status(400).json({
      error: true,
      message: error.message,
    });
  }
});

app.get('/api/user/swaps/:cohortId', protect, async function (req, res, next) {
  try {
    const { user } = req;
    const { cohortId } = req.params;
    const belongToCohort = await db.cohort.findOne({
      where: {
        [Op.or]: {
          parent_1: user.id,
          parent_2: user.id,
        },
        id: cohortId,
      },
    });
    if (!belongToCohort) {
      return res.status(404).json({
        status: 'fail',
        message: 'Cohort not found',
      });
    }
    const swaps = await db.swap.findAll({
      where: { cohort_id: cohortId },
      order: [['date', 'ASC']],
    });
    const balanceSwaps = await db.swap.findAll({
      where: { status: 0, cohort_id: cohortId },
      attributes: ['request_from', 'time_type', [sequelize.fn('sum', sequelize.col('quantity')), 'sum_quantity']],
      group: ['request_from', 'time_type'],
    });
    return res.status(200).json({
      status: 'success',
      swaps,
      balanceSwaps,
    });
  } catch (error) {
    console.log(error);
    return res.status(400).json({
      error: true,
      message: error.message,
    });
  }
});

app.post('/api/user/swaps/approve/:cohortId', protect, async function (req, res, next) {
  try {
    const { swapIds } = req.body;
    const { cohortId } = req.params;
    const user = req.user;
    const belongToCohort = await db.cohort.findOne({
      where: {
        [Op.or]: {
          parent_1: user.id,
          parent_2: user.id,
        },
        id: cohortId,
      },
    });
    if (!belongToCohort) {
      return res.status(404).json({
        status: 'fail',
        message: 'Cohort not found',
      });
    }

    const swapPromise = swapIds.map((swapId) => {
      return db.swap.update({ status: 0 }, { where: { id: swapId, cohort_id: cohortId, request_from: { [Op.ne]: user.id } } });
    });

    const swapRes = await Promise.all(swapPromise);

    const approvedSwaps = swapRes.reduce((acc, swap, idx) => {
      if (swap[0] == 1) {
        return (acc += ` ${swapIds[idx]}`);
      }
      return acc;
    }, '');
    if (approvedSwaps) {
      return res.status(200).json({
        status: 'success',
        message: `${approvedSwaps} Swaps are approved`,
      });
    } else {
      return res.status(400).json({
        status: 'fails',
        message: `Swaps are not approved`,
      });
    }
  } catch (error) {
    console.log(error);
    return res.status(400).json({
      error: true,
      message: error.message,
    });
  }
});

module.exports = app;
