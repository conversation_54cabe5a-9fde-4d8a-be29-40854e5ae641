<% if(it.layout_clean_mode) {%> <% layout("../layouts/admin/Clean") %> <% } else {%> <% layout("../layouts/admin/Main") %> <%}%> <%~ includeFile("../partials/admin/Breadcrumb",
it)%>

<div class="tab-content mx-4 my-4">
  <div class="row">
    <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
      <%~ includeFile("../partials/admin/GlobalResponse.eta", it) %>
      <div class="form-container card p-4">
        <h5 class="primaryHeading2 mb-4 text-md-left pl-3"><%= it.heading %></h5>

        <form action="/admin/emails-edit/<%= it.form_fields['id'] %>" method="POST">
          <div class="form-group col-md-5 col-sm-12">
            <label for="slug" class="control-label">Slug</label>

            <input type="text" class="form-control data-input" id="text_slug" name="slug" value="<%= it.form_fields['slug'] %>" />
          </div>

          <div class="form-group required col-md-5 col-sm-12">
            <label for="subject" class="control-label">Subject</label>

            <input type="text" required class="form-control data-input" id="text_subject" name="subject" value="<%= it.form_fields['subject'] %>" />
          </div>

          <div class="form-group col-md-5 col-sm-12">
            <label for="tag" class="control-label">Replacement Tags</label>

            <input type="text" class="form-control data-input" id="text_tag" name="tag" value="<%= it.form_fields['tag'] %>" />
          </div>

          <div class="form-group required col-md-5 col-sm-12">
            <label for="html" class="control-label">Email Body</label>

            <textarea type="text" required class="form-control data-input" id="text_html" name="html" value="<%= it.form_fields['html'] %>">
            <%= it.form_fields['html'] %>
            </textarea>
          </div>

          <div class="form-group pl-3">
            <button type="submit" class="btn btn-primary">Submit</button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
