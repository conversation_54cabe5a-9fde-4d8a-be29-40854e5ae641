/*Powered By: Manaknightdigital Inc. https://manaknightdigital.com/ Year: 2020*/
/**
 * Controller builder
 * @copyright 2020 Manaknightdigital Inc.
 * @link https://manaknightdigital.com
 * @license Proprietary Software licensing
 * <AUTHOR>
 *
 */

const fs = require('fs');
const path = require('path');
const Builder = require('./Builder');
const { cloneDeep, concat, clone } = require('lodash');
const validation_field_mapper = require('../source/utils/controllers/validations/field_mapper');
const inputFieldMapper = require('../source/utils/controllers/views/input_field_mapper');
const extendViewModel = require('../source/utils/controllers/extends/view_model/index');

module.exports = function (config) {
  this._config = config;
  this._roles = [];
  this._models = [];
  this._portals = [];
  this._controllers = [];
  this._menu = {};
  this._routes = [];
  this._render_list = [];
  this._autocomplete_routes = [];
  this._autocompleteJS = '';
  this.controller_used = {};

  Builder.call(this);

  this.set_menu = function (menu) {
    this._menu = menu;
  };

  this.set_controllers = function (controllers) {
    this._controllers = controllers;
  };

  this.get_controller = function () {
    return this._controller;
  };
  this.get_controllers = function () {
    return this._controllers;
  };

  this.set_model = function (models) {
    this._models = models;
  };

  this.get_model = function () {
    return this._models;
  };

  this.get_role = function () {
    return this._roles;
  };

  this.set_roles = function (roles) {
    this._roles = roles;
    for (const key in this._portals) {
      this._menu[this._portals[key]['name']] = this._portals[key]['menu'];
      for (const key2 in this._roles) {
        if (this._portals[key]['name'] == this._roles[key2]['name']) {
          this._portals[key]['role_id'] = this._roles[key2]['id'];
        }
      }
    }
  };

  this.get_portal = function () {
    return this._portal;
  };

  this.set_portals = function (portal) {
    this._portals = portal;
  };

  this.build = function () {
    /**
     * 1.Is it crud? Yes load Crud Template, else Other
     * 2.Is it listing? Load listing template into listing
     * 2b.Is it paginate? Load paginate template into listing
     * 2c.Is it filter? Load filter template into listing
     * 3.Is it add? Load add template into add
     * 4.Is it edit? Load edit template into edit
     * 5.Is it delete? Load delete template into delete
     * 6.Is it view? Load view template into view
     * 7.Is it menu? Load menu template into menu
     * 8.Add to routes
     * 9.If Override, put in override
     */
    for (let i = 0; i < this._controllers.length; i++) {
      const controller = this._controllers[i];
      this.setup_crud(controller, controller.is_crud && controller.view, controller.is_crud && controller.api);
    }

    for (const key in this._render_list) {
      const page = this._render_list[key];

      try {
        this.writeFileSyncRecursive(key, page, { mode: 0775 });
      } catch (error) {
        console.log('Controller Build Error', error);
      }
    }

    this.generate_index_file();
    console.log('Controller build success');
  };

  this.setup_crud = function (controller, isView, isApi) {
    const uc_portal = this.ucFirst(controller['portal']);
    const portal = controller['portal'];
    const uc_name = this.ucFirst(controller['name']);
    const correctPortal = this._portals.filter(function (singlePortal) {
      return singlePortal.role == portal;
    });
    let role = correctPortal[0]['role_id'];
    let template = fs.readFileSync('../mkdcore/source/portal/controller_template.js', 'utf8');

    template = this.inject_substitute(template, 'controller_name', controller['controller'].replace('.js', ''));
    template = this.inject_substitute(template, 'uc_portal', uc_portal);
    template = this.inject_substitute(template, 'role', role);
    template = this.inject_substitute(template, 'portal', portal);
    template = this.inject_substitute(template, 'page_name', controller['page_name']);
    template = this.inject_substitute(template, 'name', controller['name']);
    template = this.inject_substitute(template, 'model', controller['model']);
    template = this.inject_substitute(template, 'portal', portal);
    template = this.inject_substitute(template, 'uc_name', uc_name);
    template = this.inject_substitute(template, 'method', controller.method);
    //TODO
    template = this.inject_substitute(template, 'middleware', '');
    template = this.inject_substitute(template, 'middleware_list', '');

    if (!isApi) {
      // Remove API Controllers
      template = this.inject_substitute(template, 'api_list', '');
      template = this.inject_substitute(template, 'api_add', '');
      template = this.inject_substitute(template, 'api_edit', '');
      template = this.inject_substitute(template, 'api_delete', '');
      template = this.inject_substitute(template, 'api_view', '');
      template = this.inject_substitute(template, 'api_bulk_delete', '');
    }
    if (!isView) {
      // Remove Views Controllers
      template = this.inject_substitute(template, 'list', '');
      template = this.inject_substitute(template, 'add', '');
      template = this.inject_substitute(template, 'edit', '');
      template = this.inject_substitute(template, 'delete', '');
      template = this.inject_substitute(template, 'view', '');
      template = this.inject_substitute(template, 'bulk_delete', '');
    }
    if (isApi || isView) {
      if (controller['is_list']) {
        if (controller['paginate']) {
          if (controller['is_filter']) {
            //is filter
            const [list, api_list] = this.paginate_filter(controller, isView, isApi);

            template = this.inject_substitute(template, 'list', isView ? list : '');
            // TODO
            template = this.inject_substitute(template, 'api_list', isApi ? api_list : '');
          } else {
            const [list, api_list] = this.paginate(controller, isView, isApi);

            //is paginate
            template = this.inject_substitute(template, 'list', isView ? list : '');
            // TODO
            template = this.inject_substitute(template, 'api_list', isApi ? api_list : '');
          }
        } else {
          if (controller['is_filter']) {
            //is filter

            const [list, api_list] = this.list_filter(controller, isView, isApi);

            template = this.inject_substitute(template, 'list', isView ? list : '');
            // TODO
            template = this.inject_substitute(template, 'api_list', isApi ? api_list : '');
          } else {
            const [list, api_list] = this.list(controller, isView, isApi);
            template = this.inject_substitute(template, 'list', isView ? list : '');

            template = this.inject_substitute(template, 'api_list', isApi ? api_list : '');
          }
        }
      } else {
        template = this.inject_substitute(template, 'list', '');
        template = this.inject_substitute(template, 'api_list', '');
      }

      // Both the View and API will be handled inside their own methods
      if (controller['is_add']) {
        const [add, api_add] = this.add(controller, isView, isApi);
        template = this.inject_substitute(template, 'api_add', isApi ? api_add : '');
        template = this.inject_substitute(template, 'add', isView ? add : '');
      } else {
        template = this.inject_substitute(template, 'api_add', '');
        template = this.inject_substitute(template, 'add', '');
      }

      if (controller['is_edit']) {
        const [edit, api_edit] = this.edit(controller, isView, isApi);
        template = this.inject_substitute(template, 'api_edit', isApi ? api_edit : '');
        template = this.inject_substitute(template, 'edit', isView ? edit : '');
      } else {
        template = this.inject_substitute(template, 'api_edit', '');
        template = this.inject_substitute(template, 'edit', '');
      }

      if (controller['is_delete'] || controller['is_real_delete']) {
        const [_delete, api_delete] = this.delete(controller, isView, isApi);

        template = this.inject_substitute(template, 'api_delete', isApi ? api_delete : '');
        template = this.inject_substitute(template, 'delete', isView ? _delete : '');
      } else {
        template = this.inject_substitute(template, 'api_delete', '');
        template = this.inject_substitute(template, 'delete', '');
      }

      if (controller['bulk_delete']) {
        const [bulk_delete, api_bulk_delete] = this.bulk_delete(controller, isView, isApi);

        template = this.inject_substitute(template, 'api_bulk_delete', isApi ? api_bulk_delete : '');

        template = this.inject_substitute(template, 'bulk_delete', isView ? bulk_delete : '');
      } else {
        template = this.inject_substitute(template, 'bulk_delete', '');
        template = this.inject_substitute(template, 'api_bulk_delete', '');
      }

      if (controller['is_view']) {
        const [detail, api_detail] = this.view(controller, isView, isApi);

        template = this.inject_substitute(template, 'api_view', isApi ? api_detail : '');
        template = this.inject_substitute(template, 'view', isView ? detail : '');
      } else {
        template = this.inject_substitute(template, 'api_view', '');
        template = this.inject_substitute(template, 'view', '');
      }
    }

    this.generateFileUpload(controller, isView, isApi);

    if (controller['paginate']) {
      route = controller['route'];
      // route = controller['route'] + '/0';
    }

    //TODO dynamic_mapping
    //TODO autocomplete

    if (controller.load_libraries && controller.load_libraries.length > 0) {
      template = this.inject_substitute(template, 'load_libraries', controller.load_libraries.join('\n'));
    } else {
      template = this.inject_substitute(template, 'load_libraries', '');
    }

    this._render_list['../../release/controllers/' + portal + '/' + controller['controller']] = template;
  };

  this.output_paginate_filter_post = function (fields) {
    let result = '';
    for (let i = 0; i < fields.length; i++) {
      let field = fields[i];
      field = field.split('|');
      field = field[0];
      if (field.includes('.')) {
        let fieldTable = field.split('.')[0];
        let fieldName = field.split('.')[1];
        result += `viewModel.set_${fieldTable}_${fieldName}(req.query.${fieldTable}_${fieldName} ? req.query.${fieldTable}_${fieldName} : '');\n\t\t`;
      } else {
        result += `viewModel.set_${field}(req.query.${field} ? req.query.${field} : '');\n\t\t`;
      }
    }

    return result;
  };
  this.output_paginate_filter_where = function (fields, all_records, active_only) {
    let result = '';
    for (let i = 0; i < fields.length; i++) {
      let field = fields[i];
      field = field.split('|');
      field = field[0];
      result += `'${field}': viewModel.get_${field}(),\n\t\t\t`;
    }

    if (!all_records && !fields.includes('user_id')) {
      result += "'user_id': session.user,\n\t\t\t";
    }

    if (active_only) {
      result += " 'status': 1,\n\t\t\t";
    }

    return result;
  };
  this.output_paginate_api_filter_where = function (fields, all_records, active_only) {
    let result = '';
    for (let i = 0; i < fields.length; i++) {
      let field = fields[i];
      field = field.split('|');
      field = field[0];
      result += `'${field}': viewModel.get_${field}(),\n\t\t\t`;
    }

    if (!all_records && !fields.includes('user_id')) {
      result += "'user_id': user_id,\n\t\t\t";
    }

    if (active_only) {
      result += " 'status': 1,\n\t\t\t";
    }

    return result;
  };
  this.output_paginate_associated_filter_where = function (fields) {
    let result = '';
    for (let i = 0; i < fields.length; i++) {
      let field = fields[i];
      field = field.split('|');
      field = field[0];
      let fieldTable = field.split('.')[0];
      let fieldName = field.split('.')[1];
      result += `'${fieldName}': viewModel.get_${fieldTable}_${fieldName}(),\n\t\t\t`;
    }

    return result;
  };

  /**
   * Steps:
   * 1.Field are taken
   * 2.Check model fields and pull them into a list
   * 3.Return this list
   *
   * @param [type] $controller
   * @param [type] $field_type
   * @param [type] $paginateJoin
   * @return void
   */
  this.make_input_fields = function (controller, field_type, join = '') {
    // const models = [...this._models];
    const models = cloneDeep(this._models);
    let fields = cloneDeep(controller[field_type]);
    let clean_list = [];
    let model = models.find((model) => {
      return model.name == controller.model;
    });
    let model_fields = model.field;
    if (join.length > 0) {
      let joinedTable = join;
      let joinedModel = models.find((model) => {
        return model.name == joinedTable;
      });
      let joinedTableFieldsNames = [];
      fields.forEach((field) => {
        if (field.includes('.')) {
          joinedTableFieldsNames.push(field.split('.')[1]);
        }
      });
      let joinedTableFullFields = joinedModel.field.filter((field) => {
        if (joinedTableFieldsNames.includes(field[0])) {
          field[0] = `${joinedTable}.${field[0]}`;
          field[3] = `${this.capitalize(joinedTable.replace(/_/g, ' '))} ${field[3]}`;
          return field;
        }
      });
      model_fields = model_fields.concat(joinedTableFullFields);
    }
    for (let i = 0; i < fields.length; i++) {
      var field = fields[i];
      for (let j = 0; j < model_fields.length; j++) {
        const model_field = model_fields[j];
        if (model_field[0] == field) {
          clean_list.push(model_field);
        }
      }

      if (field == 'created_at') {
        clean_list.push(['created_at', 'date', [], 'xyzCreated At', '', '']);
      }
      if (field == 'updated_at') {
        clean_list.push(['updated_at', 'datetime', [], 'xyzUpdated At', '', '']);
      }
    }
    return clean_list;
  };

  this.get_autocomplete_fields = function (controller, type) {
    let autocomplete_array = [];
    let fields = controller[type];
    let result = type.split('_');
    let method_type = result[0];
    for (let i = 1; i < fields.length; i++) {
      let str_pipe = fields[i].split('|');
      let pipe = str_pipe.length > 1 ? str_pipe[1] : '';
      if (pipe.indexOf('autocomplete') > -1) {
        let fields_array = fields[i].split('|');
        let autocomplete_fields = fields[i].split(':');
        if (fields_array.length > 1) {
          let temp = {
            field_name: fields_array[0],
            table_name: autocomplete_fields[1],
            field_search: autocomplete_fields[2],
            field_label_field: autocomplete_fields[3],
            field_value_field: autocomplete_fields[4],
            method_type: method_type,
          };

          autocomplete_array.push(temp);
        }
      }
    }
    return autocomplete_array;
  };

  this.strip_pipes_controller = function (fields) {
    for (let i = 0; i < fields.length; i++) {
      let value = fields[i];
      fields[i] = value.replace(new RegExp('|', 'g'), '');
    }
    return fields;
  };

  this.output_list_filter = function (controller) {
    let fields = this.make_input_fields(
      {
        model: controller['model'],
        filter_fields: this.strip_pipes_controller(controller['filter_fields']),
      },
      'filter_fields',
      controller.join,
    );
    // let mappings = this.make_filter_mapping_fields(controller);
    let mappings = this.make_rows_mapping_fields(controller);
    let autocomplete_fields = this.get_autocomplete_fields(controller, 'filter_fields');
    let result = '';

    for (let i = 0; i < fields.length; i++) {
      let field = fields[i];

      let has_mapping = Object.keys(mappings).includes(field[0]);
      // let mapping_function = has_mapping ? `${field[0].replace(/_([^_]*)$/, '.' + '$1')}_mapping` : '';
      let mapping_function = has_mapping ? `${field[0]}_mapping` : '';

      if (field[0].includes('.')) {
        field[0] = field[0].replace(/\./g, '_');
      }
      if (has_mapping) {
        result += '\n<div class="col-xl-4 col-lg-4 col-md-4 col-sm-12 col-12">\n';
        result += '\n<div class="form-group">\n';
        result += `\n\t<label for=\"${field[3]}\">${field[3]}</label>\n`;
        result += `\n\t<select name=\"${field[0]}\" class=\"form-control\">\n`;
        result += `\n\t\t<option value=\"\">xyzAll</option>\n`;
        result += `\n\t\t<% Object.keys(it.${mapping_function}()).forEach(function(value){ %>\n`;
        result += `\n\t\t\t<option value="<%= value %>" <%= (it.get_${field[0]}() == value && it.get_${field[0]}() != '') ? 'selected' : '' %> > <%= it.${mapping_function}()[value]%> </option>`;
        result += `\n\t\t<%}); %>\n`;
        result += `\n\t</select>\n`;
        result += `\n</div>\n`;
        result += `\n</div>\n`;
      } else {
        switch (field[1]) {
          case 'string':
            result += '\n<div class="col-xl-4 col-lg-4 col-md-4 col-sm-12 col-12">\n';
            result += '\n<div class="form-group">\n';
            result += `\n\t<label for=\"${field[3]}\">${field[3]}</label>\n`;
            result += `\n\t<input type=\"text\" class=\"form-control\" id=\"${field[0]}\" name=\"${field[0]}\" value=\"<%= it.get_${field[0]}() %>\"/>\n`;
            result += '\n</div>\n';
            result += '\n</div>\n';
            break;
          case 'boolean':
            result += '\n<div class="col-xl-4 col-lg-4 col-md-4 col-sm-12 col-12">\n';
            result += '\n<div class="form-group">\n';
            result += `\n\t<label class=\"custom-control custom-checkbox\">\n`;
            result += `\n\t\t<input type=\"checkbox\" checked=\"<%= it.get_${field[0]}() %>\" class=\"custom-control-input\" id=\"form_${field[0]}\" name=\"${field[0]}\" value=\"1\"/><span class=\"custom-control-label\">${field[3]}</span>\n`;
            result += '\n\t</label>\n';
            result += '\n</div>\n';
            result += '\n</div>\n';
            break;
          case 'date':
            result += '\n<div class="col-xl-4 col-lg-4 col-md-4 col-sm-12 col-12">\n';
            result += '\n<div class="form-group">\n';
            result += `\n\t<label for=\"${field[3]}\">${field[3]}</label>\n`;
            result += `\n\t<input type=\"date\" class=\"form-control\" id=\"${field[0]}\" name=\"${field[0]}\" value=\"<%= it.get_${field[0]}() %>\"/>\n`;
            result += '\n</div>\n';
            result += '\n</div>\n';
            break;
          case 'datetime':
            result += '\n<div class="col-xl-4 col-lg-4 col-md-4 col-sm-12 col-12">\n';
            result += '\n<div class="form-group">\n';
            result += `\n\t<label for=\"${field[3]}\">${field[3]}</label>\n`;
            result += `\n\t<input type=\"datetime-local\" class=\"form-control\" id=\"${field[0]}\" name=\"${field[0]}\" value=\"<%= it.get_${field[0]}()%>\"/>\n`;
            result += '\n</div>\n';
            result += '\n</div>\n';
            break;
          case 'time':
            result += '\n<div class="col-xl-4 col-lg-4 col-md-4 col-sm-12 col-12">\n';
            result += '\n<div class="form-group">\n';
            result += `\n\t<label for=\"${field[3]}\">${field[3]}</label>\n`;
            result += `\n\t<input type=\"time\" class=\"form-control\" id=\"${field[0]}\" name=\"${field[0]}\" value=\"<%= it.get_${field[0]}()%>\"/>\n`;
            result += '\n</div>\n';
            result += '\n</div>\n';
            break;
          case 'text':
            result += '\n<div class="col-xl-4 col-lg-4 col-md-4 col-sm-12 col-12">\n';
            result += '\n<div class="form-group">\n';
            result += `\n\t<label for=\"${field[3]}\">${field[3]}</label>\n`;
            result += `\n\t<textarea id='${field[0]}' name='${field[0]}' class='form-control' rows='5'><%= it.get_${field[0]}()%></textarea>\n`;
            result += '\n</div>\n';
            result += '\n</div>\n';
            break;
          case 'email':
            result += '\n<div class="col-xl-4 col-lg-4 col-md-4 col-sm-12 col-12">\n';
            result += '\n<div class="form-group">\n';
            result += `\n\t<label for=\"${field[3]}\">${field[3]}</label>\n`;
            result += `\n\t<input type=\"email\" class=\"form-control\" id=\"${field[0]}\" name=\"${field[0]}\" value=\"<%= it.get_${field[0]}()%>\"/>\n`;
            result += '\n</div>\n';
            result += '\n</div>\n';
            break;
          case 'integer':
            result += '\n<div class="col-xl-4 col-lg-4 col-md-4 col-sm-12 col-12">\n';
            result += '\n<div class="form-group">\n';
            result += `\n\t<label for=\"${field[3]}\">${field[3]}</label>\n`;
            result += `\n\t<input type=\"text\" class=\"form-control\" id=\"${field[0]}\" name=\"${field[0]}\" value=\"<%= it.get_${field[0]}() %>\" onkeypress=\"return event.charCode >= 48 && event.charCode <= 57\"/>\n`;
            result += '\n</div>\n';
            result += '\n</div>\n';
            break;
          case 'float':
            result += '\n<div class="col-xl-4 col-lg-4 col-md-4 col-sm-12 col-12">\n';
            result += '\n<div class="form-group">\n';
            result += `\n\t<label for=\"${field[3]}\">${field[3]}</label>\n`;
            result += `\n\t<input type=\"text\" class=\"form-control\" id=\"${field[0]}\" name=\"${field[0]}\" value=\"<%= it.get_${field[0]}() %>\" onkeypress=\"return mkd_is_number(event,this)\"/>\n`;
            result += '\n</div>\n';
            result += '</div>\n';
            break;
          case 'autocomplete':
            break;

          default:
            break;
        }
      }
    }

    return result;
  };

  this.paginate_filter = function (controller, isView, isAPI) {
    const today = new Date();
    const year = today.getFullYear();
    const uc_portal = this.ucFirst(controller['portal']);
    const portal = controller['portal'];
    const uc_name = this.ucFirst(controller['name']);
    let route = controller['route'];
    let list_str = fs.readFileSync('../mkdcore/source/portal/controller_list_paginate_filter.js', 'utf8');
    const controllerRoute = controller.route.startsWith('/') ? controller.route.slice(1) : controller.route;
    const getFilterFields = controller.filter_fields;

    let autocompleteAPIController = '';
    getFilterFields.forEach((field) => {
      const splitPipe = field.split('|');
      if (splitPipe.length > 1 && splitPipe[1].startsWith('autocomplete')) {
        autocompleteAPIController = this.generateAutocomplete(field, controller);
      }
    });

    // Add autocomplete
    list_str = this.inject_substitute(list_str, 'autocomplete', autocompleteAPIController);

    list_str = this.inject_substitute(list_str, 'model', controller['model']);
    list_str = this.inject_substitute(list_str, 'uc_portal', uc_portal);
    list_str = this.inject_substitute(list_str, 'portal', portal);
    list_str = this.inject_substitute(list_str, 'route', route);

    list_str = this.inject_substitute(list_str, 'list_paginate_filter_post', this.output_paginate_filter_post(controller['filter_fields']));

    let associatedFields = controller['filter_fields'].filter((field) => {
      return field.includes('.');
    });
    let normalFields = controller['filter_fields'].filter((field) => {
      return !field.includes('.');
    });

    let normalWhere = this.output_paginate_filter_where(normalFields, controller['all_records'], controller['active_only']);
    let associatedWhere = this.output_paginate_associated_filter_where(associatedFields);
    list_str = this.inject_substitute(list_str, 'list_paginate_filter_where', normalWhere);

    list_str = this.inject_substitute(list_str, 'page_name', controller['page_name']);
    list_str = this.inject_substitute(list_str, 'uc_name', uc_name);
    list_str = this.inject_substitute(list_str, 'name', controller['name']);
    list_str = this.inject_substitute(list_str, 'year', year);

    list_str = this.inject_substitute(list_str, 'method_list_pre', controller.method_list_pre.join('\n\n'));
    list_str = this.inject_substitute(list_str, 'method_list', controller.method_list.length > 0 ? controller.method_list.join(',\n') : '');

    list_str = this.inject_substitute(list_str, 'method_list_success', controller.method_list_success.join('\n\n'));

    if (controller.join.length > 0) {
      let join_filter_flow = `
              let associatedWhere = helpers.filterEmptyFields({
                {{{associate_filter_where}}}
              });
              const isAssociationRequired = Object.keys(associatedWhere).length > 0 ? true : false;
            `;
      let joinedTable = controller.join;
      const joineeTable = this._models.find((model) => {
        return model.name == controller.model;
      });
      const JoineeToJoinedStatement = joineeTable.join.find((js) => {
        return js.name == joinedTable;
      });
      list_str = this.inject_substitute(list_str, 'join_filter_flow', join_filter_flow);
      list_str = this.inject_substitute(list_str, 'associate_filter_where', associatedWhere);
      let constructed_include = `{model: db.${joinedTable}, where: associatedWhere, required: isAssociationRequired,`;
      JoineeToJoinedStatement ? (constructed_include += `as: '${JoineeToJoinedStatement.as}' },`) : (constructed_include += `}`);
      list_str = this.inject_substitute(list_str, 'joined_db_functions', `viewModel.${joinedTable} = await db.${joinedTable};`);
      list_str = this.inject_substitute(list_str, 'include_option', constructed_include);
      list_str = this.inject_substitute(list_str, 'paginate_function', `get_${joinedTable}_paginated`);
      list_str = this.inject_substitute(list_str, 'conditional_db', 'db, associatedWhere,');
    } else {
      list_str = this.inject_substitute(list_str, 'join_filter_flow', '');
      list_str = this.inject_substitute(list_str, 'joined_db_functions', '');
      list_str = this.inject_substitute(list_str, 'include_option', '');
      list_str = this.inject_substitute(list_str, 'paginate_function', 'getPaginated');
      list_str = this.inject_substitute(list_str, 'conditional_db', '');
    }

    if (this._config.packages.permission.active) {
      list_str = this.inject_substitute(list_str, 'permission', `PermissionService.verifyPermission(role, "${controller.portal}"),`);
    } else {
      list_str = this.inject_substitute(list_str, 'permission', '');
    }

    let list_view_str = fs.readFileSync('../mkdcore/source/views/custom/Paginate_Filter.eta', 'utf8');

    list_view_str = this.inject_substitute(
      list_view_str,
      'bulk_select_all',
      controller.bulk_delete || controller.bulk_edit
        ? `<th>
                  <div class="form-check mb-4">
                    <input type="checkbox" class="form-check-input" id="bulkSelectAll" >
                  </div>
                </th>`
        : '',
    );

    list_view_str = this.inject_substitute(
      list_view_str,
      'bulk_select_individual',
      controller.bulk_delete || controller.bulk_edit
        ? `<td>
                  <div class="form-check mb-4">
                    <input type="checkbox" class="form-check-input bulkSelect" data-id="<%= data.id %>" onchange="handleBulkSelectChange(this)">
                  </div>
                    </td>`
        : '',
    );

    list_view_str = this.inject_substitute(
      list_view_str,
      'bulk_delete_button',
      controller.bulk_delete
        ? `
                <a id="bulkDeleteButton" class="btn btn-primary btn-sm ml-1 ${controller.is_real_delete ? 'btn-danger' : 'btn-warning'}" href="/${
            controller.portal
          }/${controllerRoute}-bulk-delete/" id="bulkDeleteAnchor">
                xyzBulk_Delete \n</a>\n`
        : '',
    );

    list_view_str = this.inject_substitute(
      list_view_str,
      'bulk_edit_button',
      controller.bulk_edit
        ? `
                <a id="bulkEditButton" class="btn btn-primary btn-sm ml-1" href="/${controller.portal}/${controllerRoute}-bulk-edit/" id="bulkEditAnchor">
                xyzBulk_Edit \n</a>`
        : '',
    );

    list_view_str = this.inject_substitute(list_view_str, 'name', controller['name'].replace(new RegExp(' ', 'g'), ''));
    list_view_str = this.inject_substitute(list_view_str, 'portal', controller.portal);
    list_view_str = this.inject_substitute(list_view_str, 'role', portal);
    list_view_str = this.inject_substitute(list_view_str, 'route', controller.route + '/0');

    list_view_str = this.inject_substitute(list_view_str, 'row', this.output_list_rows_raw(controller['listing_rows'], controller, this.make_rows_mapping_fields(controller)));

    if (controller['export']) {
      list_view_str = this.inject_substitute(list_view_str, 'export', this.output_export_button(controller));
      list_view_str = this.inject_substitute(list_view_str, 'add_class', 'add-part d-flex justify-content-md-end  ');
    } else {
      list_view_str = this.inject_substitute(list_view_str, 'export', '');
    }

    if (controller['is_add']) {
      list_view_str = this.inject_substitute(list_view_str, 'add', this.output_add_button(controller));
      list_view_str = this.inject_substitute(list_view_str, 'add_class', 'add-part d-flex justify-content-md-end  ');
    } else {
      list_view_str = this.inject_substitute(list_view_str, 'add_class', 'd-none');
      list_view_str = this.inject_substitute(list_view_str, 'add', '');
    }

    if (controller['import']) {
      list_view_str = this.inject_substitute(list_view_str, 'import', this.import(controller['model']));
      list_view_str = this.inject_substitute(list_view_str, 'import_class', '');
    } else {
      list_view_str = this.inject_substitute(list_view_str, 'import_class', 'd-none');
      list_view_str = this.inject_substitute(list_view_str, 'import', '');
    }

    list_view_str = this.inject_substitute(list_view_str, 'filter', this.output_list_filter(controller));

    this._render_list['../../release/views/' + portal + '/' + uc_name + '.eta'] = list_view_str;

    let list_view_model_str = fs.readFileSync('../mkdcore/source/portal/Paginate_list_view_model.js', 'utf8');
    list_view_model_str = this.inject_substitute(list_view_model_str, 'uc_name', uc_name);
    list_view_model_str = this.inject_substitute(list_view_model_str, 'route', '/' + controller.portal + controller.route);
    list_view_model_str = this.inject_substitute(list_view_model_str, 'uc_name_space', uc_name.replace(new RegExp('_', 'g'), ' '));
    list_view_model_str = this.inject_substitute(list_view_model_str, 'portal', portal);
    list_view_model_str = this.inject_substitute(
      list_view_model_str,
      'to_json',
      this.output_list_to_json(
        controller['listing_rows'].map((field) => {
          const splitted = field.split('|');
          const fieldName = splitted[0];
          let actualValue = fieldName;
          if (splitted[1].includes('~')) {
            actualValue = splitted[1].split('~')[1];
          }
          return [fieldName, actualValue];
        }),
        this.make_mapping_fields(controller),
      ),
    );
    list_view_model_str = this.inject_substitute(list_view_model_str, 'mapping', this.output_view_model_mapping(this.make_mapping_fields(controller)));
    list_view_model_str = this.inject_substitute(list_view_model_str, 'column', this.output_view_model_column_raw(controller, controller['listing_headers']));
    const readable_columns = this.output_view_model_readable_columns(controller);

    list_view_model_str = this.inject_substitute(list_view_model_str, 'readable_columns', readable_columns[0]);
    list_view_model_str = this.inject_substitute(list_view_model_str, 'readable_field_columns', readable_columns[1]);

    list_view_model_str = this.inject_substitute(list_view_model_str, 'year', year);

    list_view_model_str = this.inject_substitute(list_view_model_str, 'field_column', this.output_view_model_field_column(controller['listing_rows'], controller));
    list_view_model_str = this.inject_substitute(list_view_model_str, 'filter_fields', this.output_filter_field(controller['filter_fields']));
    this._render_list['../../release/view_models/' + controller.name + '_' + portal + '_list_paginate_view_model.js'] = list_view_model_str;

    let api_list_str = null;
    if (isAPI) {
      api_list_str = fs.readFileSync('../mkdcore/source/portal/api_controller_list_paginate_filter.js', 'utf8');

      api_list_str = this.inject_substitute(api_list_str, 'model', controller['model']);
      api_list_str = this.inject_substitute(api_list_str, 'uc_portal', uc_portal);
      api_list_str = this.inject_substitute(api_list_str, 'portal', portal);
      api_list_str = this.inject_substitute(api_list_str, 'route', route);
      api_list_str = this.inject_substitute(api_list_str, 'page_name', controller['page_name']);
      api_list_str = this.inject_substitute(api_list_str, 'uc_name', uc_name);
      api_list_str = this.inject_substitute(api_list_str, 'name', controller['name']);
      api_list_str = this.inject_substitute(api_list_str, 'year', year);

      api_list_str = this.inject_substitute(api_list_str, 'list_paginate_filter_post', this.output_paginate_filter_post(controller['filter_fields']));
      api_list_str = this.inject_substitute(api_list_str, 'method_list_pre', controller.method_list_pre.join('\n\n'));
      api_list_str = this.inject_substitute(api_list_str, 'method_list', controller.method_list.length > 0 ? controller.method_list.join(',\n') : '');

      api_list_str = this.inject_substitute(api_list_str, 'method_list_success', controller.method_list_success.join('\n\n'));
      let associatedFields = controller['filter_fields'].filter((field) => {
        return field.includes('.');
      });
      let normalFields = controller['filter_fields'].filter((field) => {
        return !field.includes('.');
      });

      let normalWhere = this.output_paginate_api_filter_where(normalFields, controller['all_records'], controller['active_only']);
      let associatedWhere = this.output_paginate_associated_filter_where(associatedFields);
      api_list_str = this.inject_substitute(api_list_str, 'list_paginate_filter_where', normalWhere);

      // Paginate Join
      if (controller.join.length > 0) {
        let join_filter_flow = `
                let associatedWhere = helpers.filterEmptyFields({
                  {{{associate_filter_where}}}
                });
                const isAssociationRequired = Object.keys(associatedWhere).length > 0 ? true : false;
              `;
        let joinedTable = controller.join;
        const joineeTable = this._models.find((model) => {
          return model.name == controller.model;
        });
        const JoineeToJoinedStatement = joineeTable.join.find((js) => {
          return js.name == joinedTable;
        });
        api_list_str = this.inject_substitute(api_list_str, 'join_filter_flow', join_filter_flow);
        api_list_str = this.inject_substitute(api_list_str, 'associate_filter_where', associatedWhere);
        let constructed_include = `{model: db.${joinedTable}, where: associatedWhere, required: isAssociationRequired, as: '${JoineeToJoinedStatement.as}' }`;
        api_list_str = this.inject_substitute(api_list_str, 'include_option', constructed_include);
        api_list_str = this.inject_substitute(api_list_str, 'conditional_db', 'db, associatedWhere,');
      } else {
        api_list_str = this.inject_substitute(api_list_str, 'join_filter_flow', '');
        api_list_str = this.inject_substitute(api_list_str, 'include_option', '');
        api_list_str = this.inject_substitute(api_list_str, 'conditional_db', '');
      }
    }
    return [list_str, api_list_str];
  };

  this.paginate = function (controller, isView, isApi) {
    const today = new Date();
    const year = today.getFullYear();
    const uc_portal = this.ucFirst(controller['portal']);
    const portal = controller['portal'];
    const uc_name = this.ucFirst(controller['name']);
    let route = controller['route'];
    const controllerRoute = controller.route.startsWith('/') ? controller.route.slice(1) : controller.route;

    let list_str = fs.readFileSync('../mkdcore/source/portal/controller_list_paginate.js', 'utf8');
    list_str = this.inject_substitute(list_str, 'model', controller['model']);
    list_str = this.inject_substitute(list_str, 'uc_portal', uc_portal);
    list_str = this.inject_substitute(list_str, 'portal', portal);
    list_str = this.inject_substitute(list_str, 'route', route);
    list_str = this.inject_substitute(list_str, 'page_name', controller['page_name']);
    list_str = this.inject_substitute(list_str, 'uc_name', uc_name);
    list_str = this.inject_substitute(list_str, 'name', controller['name']);
    list_str = this.inject_substitute(list_str, 'year', year);
    list_str = this.inject_substitute(list_str, 'method_list_pre', controller.method_list_pre.join('\n\n'));
    list_str = this.inject_substitute(list_str, 'method_list', controller.method_list.length > 0 ? controller.method_list.join(',\n') : '');
    list_str = this.inject_substitute(list_str, 'method_list_success', controller.method_list_success.join('\n\n'));

    if (!controller['all_records']) {
      if (controller['active_only']) {
        list_str = this.inject_substitute(list_str, 'all_records', '"user_id": session.user, "status": 1,');
      } else {
        list_str = this.inject_substitute(list_str, 'all_records', '"user_id": session.user,');
      }
    } else {
      if (controller['active_only']) {
        list_str = this.inject_substitute(list_str, 'all_records', '"status": 1,');
      } else {
        list_str = this.inject_substitute(list_str, 'all_records', '');
      }
    }

    // Paginate Join
    if (controller.join.length > 0) {
      let joinedTable = controller.join;
      const joineeTable = this._models.find((model) => {
        return model.name == controller.model;
      });
      const JoineeToJoinedStatement = joineeTable.join.find((js) => {
        return js.name == joinedTable;
      });
      let constructed_include = `{model: db.${joinedTable}, where: {}, required: false, `;
      JoineeToJoinedStatement ? (constructed_include += `as: '${JoineeToJoinedStatement.as}', }`) : (constructed_include += `}`);
      list_str = this.inject_substitute(list_str, 'joined_db_functions', `viewModel.${joinedTable} = await db.${joinedTable};`);
      list_str = this.inject_substitute(list_str, 'include_option', constructed_include);
      list_str = this.inject_substitute(list_str, 'getPaginated', `get_${joinedTable}_paginated`);
      list_str = this.inject_substitute(list_str, 'conditional_db', 'db, {},');
    } else {
      list_str = this.inject_substitute(list_str, 'joined_db_functions', '');
      list_str = this.inject_substitute(list_str, 'include_option', '');
      list_str = this.inject_substitute(list_str, 'getPaginated', 'getPaginated');
      list_str = this.inject_substitute(list_str, 'conditional_db', '');
    }

    if (this._config.packages.permission.active) {
      list_str = this.inject_substitute(list_str, 'permission', `PermissionService.verifyPermission(role, "${controller.portal}"),`);
    } else {
      list_str = this.inject_substitute(list_str, 'permission', '');
    }

    let api_list_str = null;
    if (isApi) {
      api_list_str = fs.readFileSync('../mkdcore/source/portal/api_controller_list_paginate.js', 'utf8');

      api_list_str = this.inject_substitute(api_list_str, 'model', controller['model']);
      api_list_str = this.inject_substitute(api_list_str, 'uc_portal', uc_portal);
      api_list_str = this.inject_substitute(api_list_str, 'portal', portal);
      api_list_str = this.inject_substitute(api_list_str, 'route', route);
      api_list_str = this.inject_substitute(api_list_str, 'page_name', controller['page_name']);
      api_list_str = this.inject_substitute(api_list_str, 'uc_name', uc_name);
      api_list_str = this.inject_substitute(api_list_str, 'name', controller['name']);
      api_list_str = this.inject_substitute(api_list_str, 'year', year);

      api_list_str = this.inject_substitute(api_list_str, 'method_list_pre', controller.method_list_pre.join('\n\n'));
      api_list_str = this.inject_substitute(api_list_str, 'method_list', controller.method_list.length > 0 ? controller.method_list.join(',\n') : '');

      api_list_str = this.inject_substitute(api_list_str, 'method_list_success', controller.method_list_success.join('\n\n'));

      // Paginate Join
      if (controller.join.length > 0) {
        let joinedTable = controller.join;
        const joineeTable = this._models.find((model) => {
          return model.name == controller.model;
        });
        const JoineeToJoinedStatement = joineeTable.join.find((js) => {
          return js.name == joinedTable;
        });
        let constructed_include = `{model: db.${joinedTable}, where: {}, required: false, as: '${JoineeToJoinedStatement.as}' },`;

        api_list_str = this.inject_substitute(api_list_str, 'include_option', constructed_include);
      } else {
        api_list_str = this.inject_substitute(api_list_str, 'include_option', '');
      }

      if (!controller['all_records']) {
        if (controller['active_only']) {
          api_list_str = this.inject_substitute(api_list_str, 'all_records', '"user_id":user_id, "status": 1,');
        } else {
          api_list_str = this.inject_substitute(api_list_str, 'all_records', '"user_id":user_id,');
        }
      } else {
        if (controller['active_only']) {
          api_list_str = this.inject_substitute(api_list_str, 'all_records', '"status": 1,');
        } else {
          api_list_str = this.inject_substitute(api_list_str, 'all_records', '');
        }
      }
    }

    let list_view_str = fs.readFileSync('../mkdcore/source/views/custom/Paginate.eta', 'utf8');

    // Check if bulk delete or edit is enabled
    // TODO: Bulk Edit

    list_view_str = this.inject_substitute(
      list_view_str,
      'bulk_select_all',
      controller.bulk_delete || controller.bulk_edit
        ? `<th>
                  <div class="form-check mb-4">
                    <input type="checkbox" class="form-check-input" id="bulkSelectAll" >
                  </div>
                </th>`
        : '',
    );

    list_view_str = this.inject_substitute(
      list_view_str,
      'bulk_select_individual',
      controller.bulk_delete || controller.bulk_edit
        ? `<td>
                  <div class="form-check mb-4">
                    <input type="checkbox" class="form-check-input bulkSelect" data-id="<%= data.id %>" onchange="handleBulkSelectChange(this)">
                  </div>
                    </td>`
        : '',
    );

    list_view_str = this.inject_substitute(
      list_view_str,
      'bulk_delete_button',
      controller.bulk_delete
        ? `
                <a id="bulkDeleteButton" class="btn btn-primary btn-sm ml-1 ${controller.is_real_delete ? 'btn-danger' : 'btn-warning'}" href="/${
            controller.portal
          }/${controllerRoute}-bulk-delete/" id="bulkDeleteAnchor">
                xyzBulk_Delete \n</a>\n</span>`
        : '',
    );

    list_view_str = this.inject_substitute(
      list_view_str,
      'bulk_edit_button',
      controller.bulk_edit
        ? `
                <a id="bulkEditButton" class="btn btn-primary btn-sm ml-1" href="/${controller.portal}/${controllerRoute}-bulk-edit/" id="bulkEditAnchor">
                xyzBulk_Edit \n</a>`
        : '',
    );

    list_view_str = this.inject_substitute(list_view_str, 'name', controller['name'].replace(new RegExp(' ', 'g'), ''));
    list_view_str = this.inject_substitute(list_view_str, 'role', portal);
    list_view_str = this.inject_substitute(list_view_str, 'row', this.output_list_rows_raw(controller['listing_rows'], controller, this.make_rows_mapping_fields(controller)));

    if (controller['export']) {
      list_view_str = this.inject_substitute(list_view_str, 'export', this.output_export_button(controller));
      list_view_str = this.inject_substitute(list_view_str, 'add_class', 'add-part d-flex justify-content-md-end  ');
    } else {
      list_view_str = this.inject_substitute(list_view_str, 'export', '');
    }

    if (controller['is_add']) {
      list_view_str = this.inject_substitute(list_view_str, 'add', this.output_add_button(controller));
      list_view_str = this.inject_substitute(list_view_str, 'add_class', 'add-part d-flex justify-content-md-end  ');
    } else {
      list_view_str = this.inject_substitute(list_view_str, 'add_class', 'd-none');
      list_view_str = this.inject_substitute(list_view_str, 'add', '');
    }

    if (controller['import']) {
      list_view_str = this.inject_substitute(list_view_str, 'import', this.import(controller['model']));
      list_view_str = this.inject_substitute(list_view_str, 'import_class', '');
    } else {
      list_view_str = this.inject_substitute(list_view_str, 'import_class', 'd-none');
      list_view_str = this.inject_substitute(list_view_str, 'import', '');
    }

    this._render_list['../../release/views/' + portal + '/' + uc_name + '.eta'] = list_view_str;

    let list_view_model_str = fs.readFileSync('../mkdcore/source/portal/Paginate_list_view_model.js', 'utf8');

    //
    list_view_model_str = this.inject_substitute(list_view_model_str, 'allowed_queries', controller.listing_headers.join());

    list_view_model_str = this.inject_substitute(list_view_model_str, 'column_headers', controller.listing_headers.join());

    list_view_model_str = this.inject_substitute(list_view_model_str, 'uc_name', uc_name);

    list_view_model_str = this.inject_substitute(list_view_model_str, 'route', '/' + controller.portal + controller.route);
    list_view_model_str = this.inject_substitute(list_view_model_str, 'uc_name_space', uc_name.replace(new RegExp('_', 'g'), ' '));
    list_view_model_str = this.inject_substitute(list_view_model_str, 'portal', portal);
    list_view_model_str = this.inject_substitute(
      list_view_model_str,
      'to_json',
      this.output_list_to_json(
        controller['listing_rows'].map((field) => {
          const splitted = field.split('|');
          const fieldName = splitted[0];
          let actualValue = fieldName;
          if (splitted[1].includes('~')) {
            actualValue = splitted[1].split('~')[1];
          }
          return [fieldName, actualValue];
        }),
        this.make_mapping_fields(controller),
      ),
    );
    list_view_model_str = this.inject_substitute(list_view_model_str, 'mapping', this.output_view_model_mapping(this.make_mapping_fields(controller)));

    list_view_model_str = this.inject_substitute(list_view_model_str, 'column', this.output_view_model_column_raw(controller, controller['listing_headers']));
    const readable_columns = this.output_view_model_readable_columns(controller);
    list_view_model_str = this.inject_substitute(list_view_model_str, 'readable_columns', readable_columns[0]);
    list_view_model_str = this.inject_substitute(list_view_model_str, 'readable_field_columns', readable_columns[1]);
    list_view_model_str = this.inject_substitute(list_view_model_str, 'year', year);

    list_view_model_str = this.inject_substitute(list_view_model_str, 'field_column', this.output_view_model_field_column(controller['listing_rows'], controller));
    list_view_model_str = this.inject_substitute(list_view_model_str, 'filter_fields', '');
    this._render_list['../../release/view_models/' + controller.name + '_' + portal + '_list_paginate_view_model.js'] = list_view_model_str;

    return [list_str, api_list_str];
  };

  this.list = function (controller, isView, isAPI) {
    const model = this._models.find((model) => {
      return model.name === controller.model;
    });

    const today = new Date();
    const year = today.getFullYear();
    const uc_portal = this.ucFirst(controller['portal']);
    const portal = controller['portal'];
    const uc_name = this.ucFirst(controller['name']);
    const name = controller['name'];
    let route = controller['route'];
    const controllerRoute = controller.route.startsWith('/') ? controller.route.slice(1) : controller.route;

    // View model
    let list_view_model_str = fs.readFileSync('../mkdcore/source/portal/List_view_model.js', 'utf8');
    list_view_model_str = this.inject_substitute(list_view_model_str, 'uc_name', uc_name);
    list_view_model_str = this.inject_substitute(list_view_model_str, 'uc_name_space', uc_name.replace(new RegExp('_', 'g'), ' '));
    list_view_model_str = this.inject_substitute(list_view_model_str, 'portal', portal);

    list_view_model_str = this.inject_substitute(list_view_model_str, 'route', '/' + controller.portal + controller.route);
    list_view_model_str = this.inject_substitute(list_view_model_str, 'filter_fields', '');

    list_view_model_str = this.inject_substitute(
      list_view_model_str,
      'to_json',
      this.output_list_to_json_single(
        controller['listing_rows'].map((field) => field.split('|')[0]),
        this.make_mapping_fields(controller),
      ),
    );
    list_view_model_str = this.inject_substitute(list_view_model_str, 'mapping', this.output_view_model_mapping(this.make_mapping_fields(controller)));
    list_view_model_str = this.inject_substitute(list_view_model_str, 'column', this.output_view_model_column_raw(controller, controller['listing_headers']));

    const readable_columns = this.output_view_model_readable_columns(controller);
    list_view_model_str = this.inject_substitute(list_view_model_str, 'readable_columns', readable_columns[0]);
    list_view_model_str = this.inject_substitute(list_view_model_str, 'readable_field_columns', readable_columns[1]);
    list_view_model_str = this.inject_substitute(list_view_model_str, 'field_column', this.output_view_model_field_column(controller['listing_rows'], controller));
    list_view_model_str = this.inject_substitute(list_view_model_str, 'filter_fields', '');
    list_view_model_str = this.inject_substitute(list_view_model_str, 'year', year);

    this._render_list['../../release/view_models/' + name + '_' + portal + '_list_view_model.js'] = list_view_model_str;

    let list_str = '';
    let api_list_str = '';

    if (isView) {
      list_str = fs.readFileSync('../mkdcore/source/portal/controller_list.js', 'utf8');

      list_str = this.inject_substitute(list_str, 'model', model.name);
      list_str = this.inject_substitute(list_str, 'uc_portal', uc_portal);
      list_str = this.inject_substitute(list_str, 'portal', portal);
      list_str = this.inject_substitute(list_str, 'route', route);
      list_str = this.inject_substitute(list_str, 'page_name', controller['page_name']);
      list_str = this.inject_substitute(list_str, 'uc_name', uc_name);
      list_str = this.inject_substitute(list_str, 'name', controller['name']);
      list_str = this.inject_substitute(list_str, 'year', year);

      list_str = this.inject_substitute(list_str, 'method_list_pre', controller.method_list_pre.join('\n\n'));

      list_str = this.inject_substitute(list_str, 'method_list', controller.method_list.length > 0 ? controller.method_list.join(',\n') : '');

      list_str = this.inject_substitute(list_str, 'method_list_success', controller.method_list_success.join('\n\n'));

      if (!controller['all_records']) {
        if (controller['active_only']) {
          list_str = this.inject_substitute(list_str, 'all_records', '"user_id": session.user, "status": 1,');
        } else {
          list_str = this.inject_substitute(list_str, 'all_records', '"user_id": session.user,');
        }
      } else {
        if (controller['active_only']) {
          list_str = this.inject_substitute(list_str, 'all_records', '"status": 1,');
        } else {
          list_str = this.inject_substitute(list_str, 'all_records', '');
        }
      }

      if (controller.join.length > 0) {
        let joinedTable = controller.join;
        const joineeTable = this._models.find((model) => {
          return model.name == controller.model;
        });
        const JoineeToJoinedStatement = joineeTable.join.find((js) => {
          return js.name == joinedTable;
        });
        let constructed_include = `{model: db.${joinedTable}, where: {}, required: false, `;
        JoineeToJoinedStatement ? (constructed_include += `as: '${JoineeToJoinedStatement.as}' },`) : (constructed_include += `}`);

        list_str = this.inject_substitute(list_str, 'joined_db_functions', `viewModel.${joinedTable} = await db.${joinedTable};`);
        list_str = this.inject_substitute(list_str, 'include_option', constructed_include);
      } else {
        list_str = this.inject_substitute(list_str, 'joined_db_functions', '');
        list_str = this.inject_substitute(list_str, 'include_option', '');
      }

      if (this._config.packages.permission.active) {
        list_str = this.inject_substitute(list_str, 'permission', `PermissionService.verifyPermission(role, "${controller.portal}"),`);
      } else {
        list_str = this.inject_substitute(list_str, 'permission', '');
      }

      let list_view_str = fs.readFileSync('../mkdcore/source/views/custom/List.eta', 'utf8');

      // Check if bulk delete or edit is enabled
      // TODO: Bulk Edit

      list_view_str = this.inject_substitute(
        list_view_str,
        'bulk_select_all',
        controller.bulk_delete || controller.bulk_edit
          ? `<th>
                  <div class="form-check mb-4">
                    <input type="checkbox" class="form-check-input" id="bulkSelectAll" >
                  </div>
                </th>`
          : '',
      );

      list_view_str = this.inject_substitute(
        list_view_str,
        'bulk_select_individual',
        controller.bulk_delete || controller.bulk_edit
          ? `<td>
                  <div class="form-check mb-4">
                    <input type="checkbox" class="form-check-input bulkSelect" data-id="<%= data.id %>" onchange="handleBulkSelectChange(this)">
                  </div>
                    </td>`
          : '',
      );

      list_view_str = this.inject_substitute(
        list_view_str,
        'bulk_delete_button',
        controller.bulk_delete
          ? `
                  <a id="bulkDeleteButton" class="btn btn-primary btn-sm ml-1 ${controller.is_real_delete ? 'btn-danger' : 'btn-warning'}" href="/${
              controller.portal
            }/${controllerRoute}-bulk-delete/" id="bulkDeleteAnchor">
                  xyzBulk_Delete \n</a>\n</span>`
          : '',
      );

      list_view_str = this.inject_substitute(
        list_view_str,
        'bulk_edit_button',
        controller.bulk_edit
          ? `
                  <a id="bulkEditButton" class="btn btn-primary btn-sm ml-1" href="/${controller.portal}/${controllerRoute}-bulk-edit/" id="bulkEditAnchor">
                  xyzBulk_Edit \n</a>`
          : '',
      );
      list_view_str = this.inject_substitute(list_view_str, 'name', controller['name'].replace(new RegExp(' ', 'g'), ''));
      list_view_str = this.inject_substitute(list_view_str, 'role', controller.portal);

      list_view_str = this.inject_substitute(list_view_str, 'row', this.output_list_rows_raw(controller['listing_rows'], controller, this.make_rows_mapping_fields(controller)));

      if (controller['export']) {
        list_view_str = this.inject_substitute(list_view_str, 'export', this.output_export_button(controller));
        list_view_str = this.inject_substitute(list_view_str, 'add_class', 'add-part d-flex justify-content-md-end  ');
      } else {
        list_view_str = this.inject_substitute(list_view_str, 'export', '');
      }

      if (controller['is_add']) {
        list_view_str = this.inject_substitute(list_view_str, 'add', this.output_add_button(controller));
        list_view_str = this.inject_substitute(list_view_str, 'add_class', 'add-part d-flex justify-content-md-end  ');
      } else {
        list_view_str = this.inject_substitute(list_view_str, 'add_class', 'd-none');
        list_view_str = this.inject_substitute(list_view_str, 'add', '');
      }

      if (controller['import']) {
        list_view_str = this.inject_substitute(list_view_str, 'import', this.import(controller['model']));
        list_view_str = this.inject_substitute(list_view_str, 'import_class', '');
      } else {
        list_view_str = this.inject_substitute(list_view_str, 'import_class', 'd-none');
        list_view_str = this.inject_substitute(list_view_str, 'import', '');
      }

      this._render_list['../../release/views/' + portal + '/' + uc_name + '.eta'] = list_view_str;
    }
    if (isAPI) {
      api_list_str = fs.readFileSync('../mkdcore/source/portal/api_controller_list.js', 'utf-8');
      api_list_str = this.inject_substitute(api_list_str, 'model', model.name);
      api_list_str = this.inject_substitute(api_list_str, 'portal', portal);
      api_list_str = this.inject_substitute(api_list_str, 'page_name', controller['page_name']);
      api_list_str = this.inject_substitute(api_list_str, 'name', name);
      api_list_str = this.inject_substitute(api_list_str, 'route', route);

      api_list_str = this.inject_substitute(api_list_str, 'method_list_pre', controller.method_list_pre.join('\n\n'));
      api_list_str = this.inject_substitute(api_list_str, 'method_list', controller.method_list.length > 0 ? controller.method_list.join(',\n') : '');
      api_list_str = this.inject_substitute(api_list_str, 'method_list_success', controller.method_list_success.join('\n\n'));

      if (controller.join.length > 0) {
        let joinedTable = controller.join;
        const joineeTable = this._models.find((model) => {
          return model.name == controller.model;
        });
        const JoineeToJoinedStatement = joineeTable.join.find((js) => {
          return js.name == joinedTable;
        });
        let constructed_include = `{model: db.${joinedTable}, where: {}, required: false, as: '${JoineeToJoinedStatement.as}' },`;

        api_list_str = this.inject_substitute(api_list_str, 'include_option', constructed_include);
      } else {
        api_list_str = this.inject_substitute(api_list_str, 'include_option', '');
      }

      if (!controller['all_records']) {
        if (controller['active_only']) {
          api_list_str = this.inject_substitute(api_list_str, 'all_records', 'user_id: user_id, status: 1,');
        } else {
          api_list_str = this.inject_substitute(api_list_str, 'all_records', 'user_id,');
        }
      } else {
        if (controller['active_only']) {
          api_list_str = this.inject_substitute(api_list_str, 'all_records', 'status: 1,');
        } else {
          api_list_str = this.inject_substitute(api_list_str, 'all_records', '');
        }
      }
    }

    return [list_str, api_list_str];
  };

  this.list_filter = function (controller, isView, isAPI) {
    const model = this._models.find((model) => {
      return model.name === controller.model;
    });

    const today = new Date();
    const year = today.getFullYear();
    const uc_portal = this.ucFirst(controller['portal']);
    const portal = controller['portal'];
    const uc_name = this.ucFirst(controller['name']);
    const name = controller['name'];
    let route = controller['route'];
    const controllerRoute = controller.route.startsWith('/') ? controller.route.slice(1) : controller.route;

    // View model
    let list_view_model_str = fs.readFileSync('../mkdcore/source/portal/List_view_model.js', 'utf8');
    list_view_model_str = this.inject_substitute(list_view_model_str, 'uc_name', uc_name);
    list_view_model_str = this.inject_substitute(list_view_model_str, 'uc_name_space', uc_name.replace(new RegExp('_', 'g'), ' '));
    list_view_model_str = this.inject_substitute(list_view_model_str, 'portal', portal);

    list_view_model_str = this.inject_substitute(list_view_model_str, 'route', '/' + controller.portal + controller.route);

    list_view_model_str = this.inject_substitute(
      list_view_model_str,
      'to_json',
      this.output_list_to_json_single(
        controller['listing_rows'].map((field) => field.split('|')[0]),
        this.make_mapping_fields(controller),
      ),
    );
    list_view_model_str = this.inject_substitute(list_view_model_str, 'mapping', this.output_view_model_mapping(this.make_mapping_fields(controller)));
    list_view_model_str = this.inject_substitute(list_view_model_str, 'column', this.output_view_model_column_raw(controller, controller['listing_headers']));

    const readable_columns = this.output_view_model_readable_columns(controller);
    list_view_model_str = this.inject_substitute(list_view_model_str, 'readable_columns', readable_columns[0]);
    list_view_model_str = this.inject_substitute(list_view_model_str, 'readable_field_columns', readable_columns[1]);

    list_view_model_str = this.inject_substitute(list_view_model_str, 'year', year);
    list_view_model_str = this.inject_substitute(list_view_model_str, 'field_column', this.output_view_model_field_column(controller['listing_rows'], controller));
    list_view_model_str = this.inject_substitute(list_view_model_str, 'filter_fields', this.output_filter_field(controller['filter_fields']));

    this._render_list['../../release/view_models/' + name + '_' + portal + '_list_view_model.js'] = list_view_model_str;

    let list_str = '';
    let api_list_str = '';

    if (isView) {
      list_str = fs.readFileSync('../mkdcore/source/portal/controller_list_filter.js', 'utf8');

      list_str = this.inject_substitute(list_str, 'model', model.name);
      list_str = this.inject_substitute(list_str, 'uc_portal', uc_portal);
      list_str = this.inject_substitute(list_str, 'portal', portal);
      list_str = this.inject_substitute(list_str, 'route', route);
      list_str = this.inject_substitute(list_str, 'page_name', controller['page_name']);
      list_str = this.inject_substitute(list_str, 'uc_name', uc_name);
      list_str = this.inject_substitute(list_str, 'name', controller['name']);
      list_str = this.inject_substitute(list_str, 'year', year);

      list_str = this.inject_substitute(list_str, 'method_list_pre', controller.method_list_pre.join('\n\n'));

      list_str = this.inject_substitute(list_str, 'method_list', controller.method_list.length > 0 ? controller.method_list.join(',\n') : '');

      list_str = this.inject_substitute(list_str, 'method_list_success', controller.method_list_success.join('\n\n'));

      list_str = this.inject_substitute(list_str, 'filter_post', this.output_paginate_filter_post(controller['filter_fields']));

      let associatedFields = controller['filter_fields'].filter((field) => {
        return field.includes('.');
      });
      let normalFields = controller['filter_fields'].filter((field) => {
        return !field.includes('.');
      });

      let normalWhere = this.output_paginate_filter_where(normalFields, controller['all_records'], controller['active_only']);
      let associatedWhere = this.output_paginate_associated_filter_where(associatedFields);
      list_str = this.inject_substitute(list_str, 'filter_where', normalWhere);

      if (controller.join.length > 0) {
        let join_filter_flow = `
                let associatedWhere = helpers.filterEmptyFields({
                  {{{associate_filter_where}}}
                });
                const isAssociationRequired = Object.keys(associatedWhere).length > 0 ? true : false;
              `;
        list_str = this.inject_substitute(list_str, 'join_filter_flow', join_filter_flow);
        list_str = this.inject_substitute(list_str, 'associate_filter_where', associatedWhere);
        let joinedTable = controller.join;
        const joineeTable = this._models.find((model) => {
          return model.name == controller.model;
        });
        const JoineeToJoinedStatement = joineeTable.join.find((js) => {
          return js.name == joinedTable;
        });
        let constructed_include = `{model: db.${joinedTable}, where: associatedWhere, required: isAssociationRequired, as: '${JoineeToJoinedStatement.as}' },`;

        list_str = this.inject_substitute(list_str, 'joined_db_functions', `viewModel.${joinedTable} = await db.${joinedTable};`);
        list_str = this.inject_substitute(list_str, 'include_option', constructed_include);
      } else {
        list_str = this.inject_substitute(list_str, 'join_filter_flow', '');
        list_str = this.inject_substitute(list_str, 'joined_db_functions', '');
        list_str = this.inject_substitute(list_str, 'include_option', '');
      }

      if (this._config.packages.permission.active) {
        list_str = this.inject_substitute(list_str, 'permission', `PermissionService.verifyPermission(role, "${controller.portal}"),`);
      } else {
        list_str = this.inject_substitute(list_str, 'permission', '');
      }

      let list_view_str = fs.readFileSync('../mkdcore/source/views/custom/List_Filter.eta', 'utf8');

      // Check if bulk delete or edit is enabled
      // TODO: Bulk Edit

      list_view_str = this.inject_substitute(
        list_view_str,
        'bulk_select_all',
        controller.bulk_delete || controller.bulk_edit
          ? `<th>
                  <div class="form-check mb-4">
                    <input type="checkbox" class="form-check-input" id="bulkSelectAll" >
                  </div>
                </th>`
          : '',
      );

      list_view_str = this.inject_substitute(
        list_view_str,
        'bulk_select_individual',
        controller.bulk_delete || controller.bulk_edit
          ? `<td>
                  <div class="form-check mb-4">
                    <input type="checkbox" class="form-check-input bulkSelect" data-id="<%= data.id %>" onchange="handleBulkSelectChange(this)">
                  </div>
                    </td>`
          : '',
      );

      list_view_str = this.inject_substitute(
        list_view_str,
        'bulk_delete_button',
        controller.bulk_delete
          ? `
                  <a id="bulkDeleteButton" class="btn btn-primary btn-sm ml-1 ${controller.is_real_delete ? 'btn-danger' : 'btn-warning'}" href="/${
              controller.portal
            }/${controllerRoute}-bulk-delete/" id="bulkDeleteAnchor">
                  xyzBulk_Delete \n</a>\n</span>`
          : '',
      );

      list_view_str = this.inject_substitute(
        list_view_str,
        'bulk_edit_button',
        controller.bulk_edit
          ? `
                  <a id="bulkEditButton" class="btn btn-primary btn-sm ml-1" href="/${controller.portal}/${controllerRoute}-bulk-edit/" id="bulkEditAnchor">
                  xyzBulk_Edit \n</a>`
          : '',
      );

      list_view_str = this.inject_substitute(list_view_str, 'portal', portal);
      list_view_str = this.inject_substitute(list_view_str, 'route', route);

      list_view_str = this.inject_substitute(list_view_str, 'name', controller['name'].replace(new RegExp(' ', 'g'), ''));
      list_view_str = this.inject_substitute(list_view_str, 'role', controller.portal);

      list_view_str = this.inject_substitute(list_view_str, 'row', this.output_list_rows_raw(controller['listing_rows'], controller, this.make_rows_mapping_fields(controller)));

      if (controller['export']) {
        list_view_str = this.inject_substitute(list_view_str, 'export', this.output_export_button(controller));
        list_view_str = this.inject_substitute(list_view_str, 'add_class', 'add-part d-flex justify-content-md-end  ');
      } else {
        list_view_str = this.inject_substitute(list_view_str, 'export', '');
      }

      if (controller['is_add']) {
        list_view_str = this.inject_substitute(list_view_str, 'add', this.output_add_button(controller));
        list_view_str = this.inject_substitute(list_view_str, 'add_class', 'add-part d-flex justify-content-md-end  ');
      } else {
        list_view_str = this.inject_substitute(list_view_str, 'add_class', 'd-none');
        list_view_str = this.inject_substitute(list_view_str, 'add', '');
      }

      if (controller['import']) {
        list_view_str = this.inject_substitute(list_view_str, 'import', this.import(controller['model']));
        list_view_str = this.inject_substitute(list_view_str, 'import_class', '');
      } else {
        list_view_str = this.inject_substitute(list_view_str, 'import_class', 'd-none');
        list_view_str = this.inject_substitute(list_view_str, 'import', '');
      }

      list_view_str = this.inject_substitute(list_view_str, 'filter', this.output_list_filter(controller, false));
      this._render_list['../../release/views/' + portal + '/' + uc_name + '.eta'] = list_view_str;
    }

    if (isAPI) {
      api_list_str = fs.readFileSync('../mkdcore/source/portal/api_controller_list_filter.js', 'utf-8');
      api_list_str = this.inject_substitute(api_list_str, 'model', model.name);
      api_list_str = this.inject_substitute(api_list_str, 'portal', portal);
      api_list_str = this.inject_substitute(api_list_str, 'page_name', controller['page_name']);
      api_list_str = this.inject_substitute(api_list_str, 'name', name);
      api_list_str = this.inject_substitute(api_list_str, 'route', route);

      api_list_str = this.inject_substitute(api_list_str, 'method_list_pre', controller.method_list_pre.join('\n\n'));
      api_list_str = this.inject_substitute(api_list_str, 'method_list', controller.method_list.length > 0 ? controller.method_list.join(',\n') : '');
      api_list_str = this.inject_substitute(api_list_str, 'method_list_success', controller.method_list_success.join('\n\n'));

      api_list_str = this.inject_substitute(api_list_str, 'filter_post', this.output_paginate_filter_post(controller['filter_fields']));
      // if (controller.join.length > 0) {
      //   let joinedTable = controller.join;
      //   const joineeTable = this._models.find((model) => {
      //     return model.name == controller.model;
      //   });
      //   const JoineeToJoinedStatement = joineeTable.join.find((js) => {
      //     return js.name == joinedTable;
      //   });
      //   let constructed_include = `{model: db.${joinedTable}, where: {}, required: false, `;
      //   JoineeToJoinedStatement ? (constructed_include += `as: '${JoineeToJoinedStatement.as}' },`) : (constructed_include += `}`);

      //   api_list_str = this.inject_substitute(api_list_str, 'include_option', constructed_include);
      // } else {
      //   api_list_str = this.inject_substitute(api_list_str, 'include_option', '');
      // }

      let associatedFields = controller['filter_fields'].filter((field) => {
        return field.includes('.');
      });
      let normalFields = controller['filter_fields'].filter((field) => {
        return !field.includes('.');
      });

      let normalWhere = this.output_paginate_api_filter_where(normalFields, controller['all_records'], controller['active_only']);
      let associatedWhere = this.output_paginate_associated_filter_where(associatedFields);
      api_list_str = this.inject_substitute(api_list_str, 'filter_where', normalWhere);

      if (controller.join.length > 0) {
        let join_filter_flow = `
                let associatedWhere = helpers.filterEmptyFields({
                  {{{associate_filter_where}}}
                });
                const isAssociationRequired = Object.keys(associatedWhere).length > 0 ? true : false;
              `;
        api_list_str = this.inject_substitute(api_list_str, 'join_filter_flow', join_filter_flow);
        api_list_str = this.inject_substitute(api_list_str, 'associate_filter_where', associatedWhere);
        let joinedTable = controller.join;
        const joineeTable = this._models.find((model) => {
          return model.name == controller.model;
        });
        const JoineeToJoinedStatement = joineeTable.join.find((js) => {
          return js.name == joinedTable;
        });
        let constructed_include = `{model: db.${joinedTable}, where: associatedWhere, required: isAssociationRequired, as: '${JoineeToJoinedStatement.as}' },`;
        api_list_str = this.inject_substitute(api_list_str, 'include_option', constructed_include);
      } else {
        api_list_str = this.inject_substitute(api_list_str, 'join_filter_flow', '');
        api_list_str = this.inject_substitute(api_list_str, 'include_option', '');
      }
    }

    return [list_str, api_list_str];
  };

  // this.constructIncludeRecursive = function (joins, controllerModel, accumulator = '[', steps = 0) {
  //   // [["question", ["question->answer","test"]]],
  //   // include = [{model: question, as:'questions', include:[{model: answer, as:'answers', include:[{model: test, as:'tests'}]}]}] desired outcome

  //   joins.forEach((join) => {
  //     if (Array.isArray(join)) {
  //       steps++;
  //       accumulator += 'include: [';
  //       return this.constructIncludeRecursive(join, controllerModel, accumulator, steps);
  //     } else {
  //       if (join.includes('->')) {
  //         controllerModel = join.split('->')[0];
  //         var joinedModel = join.split('->')[1];
  //       } else {
  //         var joinedModel = join;
  //       }
  //       const joineeTable = this._models.find((model) => {
  //         return model.name == controllerModel;
  //       });
  //       const joinStatment = joineeTable.join.find((JS) => {
  //         return JS.name == joinedModel;
  //       });
  //       accumulator += `{model: ${joinStatment.name}, as:${joinStatment.as}, `;
  //       for (let i = 0; i < steps; i++) {
  //         accumulator += '}]';
  //       }
  //       accumulator += '},';
  //     }
  //   });
  //   return (accumulator += ']');
  // };

  this.output_view_model_field_column = function (fields, controller) {
    let result = [];
    let model = this._models.find((model) => {
      return model.name == controller.model;
    });
    let joinStmnt = model.join.find((stmnt) => {
      return stmnt.name == controller.join;
    });
    for (let i = 0; i < fields.length; i++) {
      const field = fields[i];

      if (field.indexOf('complex|') > -1) {
        result.push("''");
      } else if (field.indexOf('~') > -1) {
        let parts = field.split('~');
        // let foreignTable = parts[1].split('.')[0];
        let foreignField = parts[1].split('.')[1];
        result.push(`'${joinStmnt.as}.${foreignField}'`);
        // if (joinStmnt && foreignTable == joinStmnt.name) {
        // } else {
        //   result.push(`'${parts[1]}'`);
        // }
      } else {
        let parts = field.split('|');
        result.push(`'${parts[0]}'`);
      }
    }

    return result.join(',');
  };

  this.output_filter_field = function (fields) {
    result = '';

    for (let i = 0; i < fields.length; i++) {
      let field = fields[i];
      field = field.split('|');
      field = field[0];
      if (field.includes('.')) {
        let fieldTable = field.split('.')[0];
        field = field.split('.')[1];
        result += `\n\tthis.get_${fieldTable}_${field} = function () {\n\t\treturn this._${fieldTable}_${field};\n\t}\n\n`;
        result += `\tthis.set_${fieldTable}_${field} = function (${fieldTable}_${field}) {\n\t\tthis._${fieldTable}_${field} = ${fieldTable}_${field};\n\t}\n\n\tthis._${fieldTable}_${field} = null;\n`;
      } else {
        result += `\n\tthis.get_${field} = function () {\n\t\treturn this._${field};\n\t}\n\n`;
        result += `\tthis.set_${field} = function (${field}) {\n\t\tthis._${field} = ${field};\n\t}\n\n\tthis._${field} = null;\n`;
      }
    }
    return result;
  };

  this.import = function (model) {
    return `<div class='mkd-upload-form-btn-wrapper'> <button id='btn-csv-upload-dialog' data-toggle='modal' data-model='${model}' data-target='#mkd-csv-import' class='mkd-upload-btn btn btn-primary d-block'>xyzImport</button></div>`;
  };

  this.output_export_button = function (controller) {
    return '<a class="btn btn-info btn-sm ml-1" onclick=\'mkd_export_table(window.location.href);return false;\'><i class="fas fa-file-download" style="color:white;"></i></a>';
  };

  this.output_add_button = function (controller) {
    return `<a class=\"btn btn-primary btn-sm\" target=\"__blank\" href=\"/${controller['portal']}${controller['route']}-add\"><i class=\"fas fa-plus-circle\"></i></a>`;
  };

  this.construct_view_row = function (view_stmnt, controller, view_field, associatedField) {
    if (associatedField) {
      var partsArray = view_field.split('.');
      var table = partsArray[0];
      var field = partsArray[1];
      var actualModel = this._models.find((model) => {
        return model.name == controller.model;
      });
      var joinStatment = actualModel.join.find((stmnt) => {
        return stmnt.name == table;
      });

      if (joinStatment.type == 'N:1') {
        view_stmnt = this.inject_substitute(view_stmnt, 'col_val', `item.${field}`);
        return `
            <td>
              <% data.${joinStatment.as}.forEach(item => { %>
                ${view_stmnt}</br>
              <% }) %>
            </td>
            `;
      } else {
        view_stmnt = this.inject_substitute(view_stmnt, 'col_val', `data.${view_field}`);
        return `
            <td> 
            ${view_stmnt}
            </td>
            `;
      }
    } else {
      view_stmnt = this.inject_substitute(view_stmnt, 'col_val', `data.${view_field}`);
      return `
              <td> 
              ${view_stmnt}
              </td>
            `;
    }
  };
  this.output_list_rows_raw = function (fields, controller, mappings) {
    let result = [];
    for (let i = 0; i < fields.length; i++) {
      const field = fields[i];
      let has_mapping = false;
      let mapping_function = '';
      let parts = field.split('|');
      let field_name = parts[0];
      let splitted = parts[1].split('~');
      let field_type = splitted[0];
      let value_field = splitted.length > 1 ? parts[1].split('~')[1] : field_name;
      let associatedField = value_field.includes('.') ? true : false;
      for (const mapping_key in mappings) {
        const mapping_value = mappings[mapping_key];
        if (mapping_key == value_field) {
          has_mapping = true;
          mapping_function = `${mapping_key}_mapping`;
        }
      }
      /**
       * Steps:
       * 1.Is it complex || field
       * 2.If field, get field type
       * 3.field type switch by type
       * 4.If complex split fields
       * 5.complex show fields
       */
      if (field_name == 'complex') {
        let field_list = parts[1].split(':');
        let complex_list = [];

        for (let i = 0; i < field_list.length; i++) {
          const _value = field_list[i];
          const splitJoin = _value.split('~');
          const value = splitJoin[0];
          const fieldValue = splitJoin.length > 1 ? splitJoin[1] : value;
          const label = this.typeToLabel(value);

          let has_mapping = false;
          let mapping_function = '';
          for (const mapping_key in mappings) {
            const mapping_value = mappings[mapping_key];
            if (mapping_key == value) {
              has_mapping = true;
              mapping_function = `${mapping_key}_mapping`;
            }
          }

          if (has_mapping) {
            complex_list.push(`<span class="font-weight-bold">${label}</span>: <%= it.${mapping_function}()[data.${fieldValue}] %> </br>`);
          } else {
            complex_list.push(`<span class="font-weight-bold">${label}</span>: <%= data.${fieldValue} %> </br>`);
          }
        }

        complex_list.unshift('<td>');
        complex_list.push('</td>');
        complex_list = complex_list.join('');

        result.push(complex_list);
      } else if (field_type.startsWith('link')) {
        field_list = field_type.split(':');
        if (field_list.length != 2) {
          result.push('<td>ERROR</td>');
        } else {
          link = field_list[1];
          result.push(`<td><a href='${link}'><%= data.${value_field} %></a></td>`);
        }
      } else {
        switch (field_type) {
          case 'date':
            result.push(
              this.construct_view_row(
                '<%= new Date({{{col_val}}}).toLocaleString("en-us",{month:"short",day:"numeric",year:"numeric"}) %>',
                controller,
                value_field,
                associatedField,
              ),
            );
            break;
          case 'datetime':
            result.push(this.construct_view_row('<%= it.datetime(new Date({{{col_val}}})) %>', controller, value_field, associatedField));
            break;
          case 'timeago':
            result.push(this.construct_view_row('<%= it.timeago({{{col_val}}}) %>', controller, value_field, associatedField));
            break;
          case 'time':
            result.push(this.construct_view_row('<%= it.time_default_mapping()[{{{col_val}}}]', controller, value_field, associatedField));
            break;
          case 'currency':
            result.push(this.construct_view_row('$ <%= it.number_format({{{col_val}}}, 2) %>', controller, value_field, associatedField));
            break;
          case 'percent':
            result.push(this.construct_view_row('<%= it.number_format({{{col_val}}}, 2) %> %', controller, value_field, associatedField));
            break;
          case 'image':
            result.push(
              this.construct_view_row(
                `<div class='mkd-image-container'>
                  <img class='img-fluid modal-image' src='<%= {{{col_val}}} %>' onerror="if (this.src != '/uploads/placeholder.jpg') this.src = '/uploads/placeholder.jpg'"/></div>`,
                controller,
                value_field,
                associatedField,
              ),
            );
            break;
          case 'imagefile':
            result.push(this.construct_view_row('<%= it.image_or_file({{{col_val}}}) %>', controller, value_field, associatedField));
            break;
          case 'boolean':
            result.push(this.construct_view_row('<%= ({{{col_val}}} == 1) ? "xyzYes" : "xyzNo" %>', controller, value_field, associatedField));
            break;
          case 'uppercase':
            result.push(this.construct_view_row('<%= {{{col_val}}}.toUpperCase() %>', controller, value_field, associatedField));
            break;
          case 'lowercase':
            result.push(this.construct_view_row('<%= {{{col_val}}}.toLowerCase() %>', controller, value_field, associatedField));
            break;
          case 'uppercasefirst':
            result.push(this.construct_view_row('<%= it.ucFirst({{{col_val}}}) %>', controller, value_field, associatedField));
            break;
          case 'url':
            result.push(this.construct_view_row(`<a  class='btn-link' target='_blank' href='<%= {{{col_val}}}%>'>xyzView</a>`, controller, value_field, associatedField));
            break;
          case 'json':
            result.push(this.construct_view_row('<%= JSON.stringify(JSON.parse({{{col_val}}})) %>', controller, value_field, associatedField));
            break;
          case 'integer':
          case 'float':
          case 'string':
          case 'text':
          case 'file':
            if (has_mapping) {
              result.push(this.construct_view_row(`<%= it.ucFirst(it.${mapping_function}()[{{{col_val}}}]) %>`, controller, value_field, associatedField));
            } else {
              result.push(this.construct_view_row('<%= {{{col_val}}} %>', controller, value_field, associatedField));
            }
            break;
          default:
            break;
        }
      }
    }

    // actions
    if (controller.is_crud) {
      let actions = [];

      if (controller.is_view) {
        actions.push(
          `<a class="btn btn-link  link-underline text-underline  btn-sm" target="_blank" href="/${controller.portal}${controller.route}-view/<%= data.id %>">View</a>&nbsp;`,
        );
      }

      if (controller.is_edit) {
        actions.push(
          `<a target="_blank" class="btn btn-link  link-underline text-underline  btn-sm" href="/${controller.portal}${controller.route}-edit/<%= data.id %>">Edit</a>&nbsp;`,
        );
      }

      if (controller.is_real_delete) {
        actions.push(
          `<a class="btn btn-link  link-underline text-underline text-danger btn-sm" href="/${controller.portal}${controller.route}-delete/<%= data.id %>">Delete</a>&nbsp;`,
        );
      } else if (controller.is_delete) {
        actions.push(
          `<a class="btn btn-link  link-underline text-underline  text-danger btn-sm" href="/${controller.portal}${controller.route}-delete/<%= data.id %>">Delete</a>&nbsp;`,
        );
      }

      if (actions.length) {
        actions.unshift('<td>');
        actions.push('</td>');

        actions = actions.join('');
        result.push(actions);
      }
    }

    return result.join('\n\n');
  };

  this.output_list_to_json_single = function (listingFields, mapping) {
    const listing_fields = listingFields.map((item) => item);
    let result = '\n\tthis.to_json = function ()\t{\n';
    result += '\t\tlet list = this.get_list();\n\n';
    result += '\t\tlet clean_list = [];\n\n';
    result += '\t\tfor (let key in list) {\n';
    result += `\t\t\tlet value = list[key];\n`;

    for (let mapping_key in mapping) {
      result += `\t\t\tlist[key].${mapping_key} = this.${mapping_key}_mapping()[value.${mapping_key}];\n`;
    }

    result += '\t\t\tlet clean_list_entry = {};\n';

    for (let list_key in listing_fields) {
      result += `\t\t\tclean_list_entry["${listing_fields[list_key]}"] = list[key]['${listing_fields[list_key]}'];\n`;
    }

    result += '\t\t\tclean_list.push(clean_list_entry);\n';

    result += '\t\t}\n\n';

    result += '\t\treturn {\n';
    result += '\t\t\t"page" : 1,\n';
    result += '\t\t\t"num_page" : 1,\n';
    result += '\t\t\t"num_item" : clean_list.length,\n';
    result += '\t\t\t"item" : clean_list\n';
    result += '\t\t};\n\t';
    result += '};\n';

    return result;
  };

  this.output_list_to_json = function (listingFields, mapping) {
    const listing_fields = listingFields.map((item) => item[0]);
    const actualValues = listingFields.map((item) => item[1]);

    let result = '\n\tthis.to_json = function ()\t{\n';
    result += '\t\tlet list = this.get_list();\n\n';
    result += '\t\tlet clean_list = [];\n\n';
    result += '\t\tfor (let key in list) {\n';
    result += `\t\t\tlet value = list[key];\n`;

    for (let mapping_key in mapping) {
      result += `\t\t\tlist[key].${mapping_key} = this.${mapping_key}_mapping()[value.${mapping_key}];\n`;
    }

    result += '\t\t\tlet clean_list_entry = {};\n';

    for (let list_key in listing_fields) {
      const field = listing_fields[list_key];
      const value = actualValues[list_key]
        .split('.')
        .map((item) => '["' + item + '"]')
        .join('');

      if (field !== 'complex') {
        result += `\t\t\tclean_list_entry["${field}"] = value${value};\n`;
      }
    }

    result += '\t\t\tclean_list.push(clean_list_entry);\n';

    result += '\t\t}\n\n';

    result += '\t\treturn {\n';
    result += '\t\t\t"page" : this.get_page(),\n';
    result += '\t\t\t"num_page" : this.get_num_page(),\n';
    result += '\t\t\t"num_item" : this.get_total_rows(),\n';
    result += '\t\t\t"item" : clean_list\n';
    result += '\t\t};\n\t';
    result += '};\n';
    result += this.output_list_to_csv(listingFields, mapping);

    return result;
  };

  this.output_list_to_csv = function (listingFields, mapping) {
    const listing_fields = listingFields.map((item) => item[0]);
    const actualValues = listingFields.map((item) => item[1]);
    let result = '\n\tthis.to_csv = function ()\t{\n';
    result += '\t\tlet list = this.get_list();\n\n';
    result += '\t\tlet clean_list = [];\n\n';
    result += '\t\tfor (let key in list) {\n';
    result += `\t\t\tlet value = list[key];\n`;

    for (let mapping_key in mapping) {
      result += `\t\t\tlist[key].${mapping_key} = this.${mapping_key}_mapping()[value.${mapping_key}];\n`;
    }

    result += '\t\t\tlet clean_list_entry = {};\n';

    for (let list_key in listing_fields) {
      const field = listing_fields[list_key];
      const value = actualValues[list_key]
        .split('.')
        .map((item) => '["' + item + '"]')
        .join('');
      if (field !== 'complex') {
        result += `\t\t\tclean_list_entry["${field}"] = value${value};\n`;
      }
    }

    result += '\t\t\tclean_list.push(clean_list_entry);\n';

    result += '\t\t}\n\n';

    result += '\t\tconst columns = this.get_field_column();\n\n';
    result += '\t\tconst column_fields = this.get_readable_field_column();\n\n';

    result += '\t\tconst fields = column_fields.filter(function(v, index){ \nif (v.length === 0) {columns.splice(index, 1);\n}\nreturn v.length > 0;});\n\n';
    result += '\t\tlet csv = columns.join(",") + "\\n";\n';

    result += '\t\tfor (let i = 0; i < clean_list.length; i++) {\n';
    result += '\t\t\tlet row = clean_list[i];\n';

    result += '\t\t\tlet row_csv = [];\n';
    result += '\t\t\tfor (const key in row) {\n';
    result += '\t\t\t\tlet column = row[key];\n';
    result += '\t\t\t\tif (fields.includes(key)) {\n';
    result += "\t\t\t\t\trow_csv.push('\"' + column + '\"');\n";
    result += '\t\t\t\t}\n';
    result += '\t\t\t}\n';
    result += '\t\t\tcsv = csv + row_csv.join(\',\') + "\\n";\n';
    result += '\t\t}\n';
    result += '\t\treturn csv;\n';
    result += '}\n';

    return result;
  };

  this.make_mapping_fields = function (controller) {
    let mappings = {};
    let models = cloneDeep(this._models);

    let mainModel = models.find((model) => {
      return model.name == controller.model;
    });
    if (mainModel.mapping && Object.keys(mainModel.mapping).length > 0) {
      mappings = { ...mappings, ...mainModel.mapping };
    }
    return mappings;
  };

  this.make_rows_mapping_fields = function (controller) {
    let mappings = {};
    let models = cloneDeep(this._models);

    let mainModel = models.find((model) => {
      return model.name == controller.model;
    });
    let joindModel = models.find((model) => {
      return model.name == controller.join;
    });

    if (mainModel.mapping && Object.keys(mainModel.mapping).length > 0) {
      mappings = { ...mappings, ...mainModel.mapping };
    }
    if (joindModel && joindModel.mapping && Object.keys(joindModel.mapping).length > 0) {
      var key,
        keys = Object.keys(joindModel.mapping);
      var n = keys.length;
      var joindModelMappings = {};
      while (n--) {
        key = keys[n];
        joindModelMappings[`${joindModel.name}.${key}`] = joindModel.mapping[key];
      }
      mappings = { ...mappings, ...joindModelMappings };
    }
    return mappings;
  };
  // this.make_filter_mapping_fields = function (controller) {
  //   let mappings = {};
  //   let models = cloneDeep(this._models);

  //   let mainModel = models.find((model) => {
  //     return model.name == controller.model;
  //   });
  //   let joindModel = models.find((model) => {
  //     return model.name == controller.join;
  //   });

  //   if (mainModel.mapping && Object.keys(mainModel.mapping).length > 0) {
  //     mappings = { ...mappings, ...mainModel.mapping };
  //   }
  //   if (joindModel && joindModel.mapping && Object.keys(joindModel.mapping).length > 0) {
  //     var key,
  //       keys = Object.keys(joindModel.mapping);
  //     var n = keys.length;
  //     var joindModelMappings = {};
  //     while (n--) {
  //       key = keys[n];
  //       joindModelMappings[`${joindModel.name}.${key}`] = joindModel.mapping[key];
  //     }
  //     mappings = { ...mappings, ...joindModelMappings };
  //   }
  //   return mappings;
  // };

  this.output_view_model_mapping = function (mappings) {
    result = '';
    for (let mapping_key in mappings) {
      let mapping_value = mappings[mapping_key];
      result += `\n\tthis.${mapping_key}_mapping = function () {`;
      if (Object.keys(mapping_value).length > 0) {
        result += `\n\t\treturn this._entity.${mapping_key}_mapping();\n`;
      }
      result += '\n\t}\n';
    }

    return result;
    ``;
  };

  this.output_view_model_column_raw = function (controller, fields) {
    let result = [];
    for (let i = 0; i < fields.length; i++) {
      const field = fields[i];
      result.push(`'${field}'`);
    }

    const { is_edit, is_view, is_delete, is_real_delete } = controller;

    if (is_edit || is_view || is_delete || is_real_delete) {
      result.push("'xyzAction'");
    }

    return result.join(',');
  };

  this.output_view_model_readable_columns = function (controller) {
    const listing_headers = [...controller.listing_headers];
    let listing_rows = [...controller.listing_rows];
    listing_rows = listing_rows.map((row, index) => {
      const splitted = row.split('|');
      const name = splitted[0];
      let type = splitted[1];

      if (type.includes('~'));
      {
        const joinSplit = splitted[1].split('~');
        type = joinSplit[0];
        const joinField = joinSplit[1];
      }
      // const type = splitted[1].split('~')[0];
      if (type === 'image' || name === 'complex') {
        listing_headers.splice(index, 1, null);
        return null;
      }
      return name;
    });
    return [JSON.stringify(listing_headers.filter((item) => item !== null)), JSON.stringify(listing_rows.filter((item) => item !== null))];
  };

  this.generate_index_file = function () {
    const controllers = this.get_controllers().map((controller) => {
      return { name: controller.controller, camelCaseName: this.ucFirst(this.toCamelCaseString(controller.controller.replace(/.js$/g, ''))), role: controller.portal };
    });
    let accumulator = ``;
    controllers.forEach((controller) => {
      accumulator += `const ${controller.camelCaseName} = require('./${controller.role}/${controller.name.replace(/.js$/g, '')}');\r\n`;
    });
    let portalsDashboardInfo = this._portals.map((portal) => {
      return { camelCaseName: this.ucFirst(portal.name) + 'Dashboard', name: portal.name };
    });
    portalsDashboardInfo.forEach((portal) => {
      accumulator += `const ${portal.camelCaseName} = require('./${portal.name}/Dashboard');\r\n`;
    });
    accumulator += `module.exports = [${controllers.map((controller) => `${controller.camelCaseName}`)}, ${portalsDashboardInfo.map((portal) => `${portal.camelCaseName}`)}]`;
    this.writeFileSyncRecursiveV2(path.join(__dirname, '../../release/controllers/index.js'), accumulator, { mode: 0775 });
  };

  this.add = function (controller, isView, isAPI) {
    const model = this._models.find((model) => {
      return model.name === controller.model;
    });
    const { getFieldType, getInputValidations } = validation_field_mapper();

    const validation_fields = {};
    const validation_extend_text = {};
    const add_fields = [];

    const getAddFields = controller.add_fields;
    const fields = [...model.field];

    // If a field that is actually not a hardcoded field in the model fields itself but is a foreign key, include them in model fields as well
    if (model.join.length) {
      for (let child of model.join) {
        // But check first if this is included or not. Someone might have hardcoded this in the model fields as well as included in join field
        if (!fields.find((item) => item[0] === child.foreign_key)) {
          fields.push([child.field, 'integer', [], this.ucFirst(child.name) + ' ID', '', '']);
        }
      }
    }

    let drop_down_generators = '';
    let drop_down_generators_holders = '';
    let drop_down_generators_initialize = '';
    let upload_field = null;

    // let isForeign = false;
    let foreignTable;

    getAddFields.forEach((addField) => {
      const splitted = addField.split('~');
      const add_field = splitted[0];
      let field = fields.find((item) => item[0] === add_field.split('|')[0]); // This split is required to be compatible with autocomplete as well

      if (splitted.length > 1) {
        const _foreignTable = splitted[1].split('.');
        const foreignTableName = _foreignTable[0];
        const foreignTableFiled = _foreignTable[1];
        foreignTable = this._config.models.find((_model) => _model.name === foreignTableName);
        field = foreignTable.field.find((item) => item[0] === foreignTableFiled);
      }

      if (field.length) {
        const fieldName = field[0];
        const readableFieldName = this.ucFirst(this.toCamelCaseString(fieldName));

        const add_validation = field[4];
        const strip_validation = this.stripPipe(add_validation);

        const findAddField = getAddFields.find((item) => {
          let actualField = this.stripPipe(item)[0];
          const splitted = actualField.split('~');
          if (splitted.length > 1) {
            actualField = splitted[0];
          }
          return actualField === field[0];
        });

        if (getFieldType(field[1], findAddField).includes('drop_down')) {
          const actualType = getFieldType(field[1], findAddField);
          const name = field[0];

          const dropdown_model_name = actualType.substring(actualType.indexOf(':') + 1, actualType.indexOf(':('));

          const code = `
                   this.set_dropdown_${name} = async () => {
                     this.dropdown_${name} = await db.${dropdown_model_name}.findAll({raw: true})
                   }
                 `;

          drop_down_generators_holders += `\n this.dropdown_${name} = []; \n`;
          drop_down_generators += `\n ${code} \n`;
          drop_down_generators_initialize += `\n await viewModel.set_dropdown_${name}() \n`;
        }

        add_fields.push({
          name: field[0],
          type: this.stripPipe(getFieldType(field[1], findAddField))[0],
          actualType: getFieldType(field[1], findAddField), // FE form validation from add fields
          label: field[3],
          value: '',
          validation: field[4],
        });

        // Prepares an array of name, type, actualType and label for fields
        if (Array.isArray(getAddFields) && getAddFields.includes(fieldName)) {
          strip_validation.forEach((item) => {
            getInputValidations(item, fieldName, readableFieldName, validation_fields, validation_extend_text);
          });
        }
      }
      // }
    });

    // Autocomplete should be enabled no matter if view or API is enabled
    let autocompleteAPIController = '';
    getAddFields.forEach((field) => {
      const splitPipe = field.split('|');
      if (splitPipe.length > 1 && splitPipe[1].startsWith('autocomplete')) {
        autocompleteAPIController = this.generateAutocomplete(field, controller);
      }
    });

    const ucName = this.ucFirst(controller.name);
    const ucModelName = this.ucFirst(controller.model);
    const entity = `db.${controller.model}`;
    const addPageName = controller.model.replace(new RegExp('_', 'g'), ' ');
    const controllerRoute = controller.route.startsWith('/') ? controller.route.slice(1) : controller.route;
    const baseRoute = '/' + controller.portal + '/' + controllerRoute + (controller.paginate ? '/0' : '');
    const actualRoute = '/' + controller.portal + '/' + controllerRoute + '-add';

    // Both views and API will use same view_model
    const viewModelName = controller.name + '_' + controller.portal + '_add_view_model';

    // Initialize the view model
    let add_view_model = fs.readFileSync('../mkdcore/source/portal/Add_view_model.js', {
      encoding: 'utf-8',
    });

    const year = new Date().getFullYear();
    add_view_model = this.inject_substitute(add_view_model, 'year', year);
    add_view_model = this.inject_substitute(add_view_model, 'uc_name', ucName);
    add_view_model = this.inject_substitute(add_view_model, 'route', '/' + controller.portal + controller.route);

    let add_template = '';
    let api_add_template = '';

    add_view_model = this.inject_substitute(add_view_model, 'controller_name', controller.name);

    add_view_model = this.inject_substitute(add_view_model, 'portal', controller.portal);

    if (drop_down_generators.length) {
      add_view_model = this.inject_substitute(add_view_model, 'drop_down_generators', drop_down_generators);

      add_view_model = this.inject_substitute(add_view_model, 'drop_down_generators_holders', drop_down_generators_holders);
    } else {
      add_view_model = this.inject_substitute(add_view_model, 'drop_down_generators', '');

      add_view_model = this.inject_substitute(add_view_model, 'drop_down_generators_holders', '');
    }

    add_view_model = this.inject_substitute(add_view_model, 'mapping', this.output_view_model_mapping(this.make_mapping_fields(controller)));

    const acceptedFields = add_fields.map((item) => item.name).join();

    if (isView) {
      // Load controller template
      add_template = fs.readFileSync('../mkdcore/source/portal/controller_add.js', {
        encoding: 'utf8',
      });

      // Add autocomplete
      add_template = this.inject_substitute(add_template, 'autocomplete', autocompleteAPIController);
      // Additional middleware for POST

      // If Validation fields are empty do not include the validation middleware

      const validationMiddleware =
        Object.keys(validation_fields).length > 0
          ? `ValidationService.validateInput(
              ${JSON.stringify(validation_fields)},
              ${JSON.stringify(validation_extend_text)}
            ),`
          : '';

      add_template = this.inject_substitute(add_template, 'additional_post_middlewares', `${validationMiddleware}`);

      // Additional middleware for GET
      add_template = this.inject_substitute(add_template, 'additional_get_middlewares', ``);

      add_template = this.inject_substitute(add_template, 'portal', controller.portal);
      add_template = this.inject_substitute(add_template, 'uc_name', 'Add_' + ucName);

      add_template = this.inject_substitute(add_template, 'route', controllerRoute + '-add');

      add_template = this.inject_substitute(add_template, 'view_model_name', viewModelName);

      add_template = this.inject_substitute(add_template, 'cc_view_model_name', this.toCamelCaseString(viewModelName));

      add_template = this.inject_substitute(
        add_template,
        'view_model_arguments',
        `db.${controller.model},"xyzAdd ${addPageName}", "","","/${controller.portal + controller.route}"`,
      );

      // set dropdown
      if (drop_down_generators_initialize.length) {
        add_template = this.inject_substitute(add_template, 'initialize_drop_down_generators', drop_down_generators_initialize);
      } else {
        add_template = this.inject_substitute(add_template, 'initialize_drop_down_generators', '');
      }

      if (this._config.packages.permission.active) {
        add_template = this.inject_substitute(add_template, 'permission', `PermissionService.verifyPermission(role, "${controller.portal}"),`);
      } else {
        add_template = this.inject_substitute(add_template, 'permission', '');
      }

      // Render logic for add
      const viewName = controller.portal + '/Add_' + ucName;

      const success_redirect = baseRoute;

      // TODO: We won't use this way
      // const upload_field_setter = upload_field
      //   ? `req.body.${upload_field} = req.file.location;`
      //   : '';

      add_template = this.inject_substitute(add_template, 'add_fields', add_fields.map((item) => `${item.name}`).join(','));

      if (model.name === 'user') {
        var create_resource = `
                const { email, password = '', role_id, ...rest } = viewModel.form_fields;
                const data = await AuthService.register(
                  email,
                  password,
                  role_id,
                  rest,
                );
              `;
      } else {
        var create_resource = `      
                const data =  await {{{entity}}}.insert({ {{{accepted_fields}}}{{{method_add}}} });
              `;
      }

      add_template = this.inject_substitute(add_template, 'create_resource', create_resource);
      add_template = this.inject_substitute(add_template, 'accepted_fields', acceptedFields);
      add_template = this.inject_substitute(add_template, 'entity', entity);
      add_template = this.inject_substitute(add_template, 'viewName', viewName);
      let methodAdd = controller.method_add.length > 0 ? ',' + controller.method_add.join(',\n') : '';
      add_template = this.inject_substitute(add_template, 'method_add', methodAdd);
      add_template = this.inject_substitute(add_template, 'method_add_pre', controller.method_add_pre.join('\n\n'));
      add_template = this.inject_substitute(add_template, 'method_add_success', controller.method_add_success.join('\n\n'));
      add_template = this.inject_substitute(add_template, 'ucModelName', this.ucFirst(addPageName));
      add_template = this.inject_substitute(add_template, 'success_redirect', success_redirect);

      add_view_model = this.inject_substitute(add_view_model, 'form_heading', `this.heading =  "xyzAdd ${addPageName}"`);
      add_view_model = this.inject_substitute(add_view_model, 'form_action', `this.action = "${actualRoute}"`);

      if (controller.activity_log) {
        let activityLog = `
                await db.activity_log.insert({
                  action: 'ADD',
                  name: '${controller.controller}',
                  portal: '${controller.portal}',
                  data: JSON.stringify({${acceptedFields}, ${methodAdd}}),
                });          
              `;
        add_template = this.inject_substitute(add_template, 'activity_log', activityLog);
      } else {
        add_template = this.inject_substitute(add_template, 'activity_log', '');
      }

      const viewModelExtends = extendViewModel.add(controller.model, controller.portal);

      Object.keys(viewModelExtends).map((key) => {
        add_view_model = this.inject_substitute(add_view_model, key, viewModelExtends[key]);
      });

      // View
      let add_view = fs.readFileSync('../mkdcore/source/views/custom/Add.eta', {
        encoding: 'utf-8',
      });

      add_view = this.inject_substitute(add_view, 'submitText', 'xyzSubmit');

      // Generate the form fields

      const form = inputFieldMapper(add_fields, model.mapping, {
        tableName: controller.model,
      });

      add_view = this.inject_substitute(
        add_view,
        'formContent',
        `
              <form action="/${controller.portal}${controller.route}-add" method="POST"  ${upload_field ? 'enctype="multipart/form-data"' : ''}>
              ${form}
               <div class="form-group pl-3">
                   <button type="submit" class="btn btn-primary">xyzSubmit</button>
               </div>
               </form>
              `,
      );

      add_view = this.inject_substitute(add_view, 'role', controller.portal);

      this._render_list['../../release/views/' + viewName + '.eta'] = add_view;
    } else {
      add_view_model = this.inject_substitute(add_view_model, 'form_heading', '');

      add_view_model = this.inject_substitute(add_view_model, 'form_action', '');
    }

    if (isAPI) {
      // Load controller template
      api_add_template = fs.readFileSync('../mkdcore/source/portal/api_controller_add.js', {
        encoding: 'utf8',
      });

      // Add autocomplete
      api_add_template = this.inject_substitute(api_add_template, 'autocomplete', !controller.view ? autocompleteAPIController : '');

      api_add_template = this.inject_substitute(api_add_template, 'portal', controller.portal);

      api_add_template = this.inject_substitute(api_add_template, 'route', controllerRoute + '-add');

      // Additional middleware for POST

      // If Validation fields are empty do not include the validation middleware
      api_add_template = this.inject_substitute(
        api_add_template,
        'additional_post_middlewares',
        Object.keys(validation_fields).length > 0
          ? `ValidationService.validateInput(
                  ${JSON.stringify(validation_fields)},
                  ${JSON.stringify(validation_extend_text)}
                ),`
          : '',
      );

      api_add_template = this.inject_substitute(api_add_template, 'view_model_name', viewModelName);
      api_add_template = this.inject_substitute(api_add_template, 'cc_view_model_name', this.toCamelCaseString(viewModelName));
      api_add_template = this.inject_substitute(api_add_template, 'view_model_arguments', `db.${controller.model}`);

      if (model.name === 'user') {
        var create_resource_api = `
                const { email, password = '', role_id, ...rest } = req.body;
                const data = await AuthService.register(
                  email,
                  password,
                  role_id,
                  rest,
                );
              `;
      } else {
        var create_resource_api = `      
                const data =  await {{{entity}}}.insert({ {{{accepted_fields}}}{{{method_add}}} });
              `;
      }

      api_add_template = this.inject_substitute(api_add_template, 'create_resource_api', create_resource_api);
      api_add_template = this.inject_substitute(api_add_template, 'accepted_fields', acceptedFields);
      api_add_template = this.inject_substitute(api_add_template, 'entity', entity);
      api_add_template = this.inject_substitute(api_add_template, 'ucModelName', this.ucFirst(addPageName));

      let methodAdd = controller.method_add.length > 0 ? ',' + controller.method_add.join(',\n') : '';

      api_add_template = this.inject_substitute(api_add_template, 'method_add', methodAdd);

      api_add_template = this.inject_substitute(api_add_template, 'method_add_pre', controller.method_add_pre.join('\n\n'));
      api_add_template = this.inject_substitute(api_add_template, 'method_add_success', controller.method_add_success.join('\n\n'));

      if (controller.activity_log) {
        let activityLog = `
                await db.activity_log.insert({
                  action: 'ADD',
                  name: '${controller.controller}',
                  portal: '${controller.portal}',
                  data: JSON.stringify({${acceptedFields}, ${methodAdd}}),
                });          
              `;
        api_add_template = this.inject_substitute(api_add_template, 'activity_log', activityLog);
      } else {
        api_add_template = this.inject_substitute(api_add_template, 'activity_log', '');
      }
    }

    const formatFormFields = add_fields.reduce((accumulator, currentValue) => {
      if (!accumulator[currentValue.name]) {
        accumulator[currentValue.name] = currentValue.value;
      }
      return accumulator;
    }, {});

    add_view_model = this.inject_substitute(add_view_model, 'fields', JSON.stringify(formatFormFields));

    this._render_list['../../release/view_models/' + viewModelName + '.js'] = add_view_model;

    return [add_template, api_add_template];
  };

  this.edit = function (controller, isView, isAPI) {
    const model = this._models.find((model) => {
      return model.name === controller.model;
    });

    const { getFieldType, getInputValidations } = validation_field_mapper();

    const validation_fields = {};
    const validation_extend_text = {};
    const edit_fields = [];

    let joinModel = this._models.find((model) => {
      return model.name === controller.join;
    });
    let joinStmnt = joinModel
      ? model.join.find((stmnt) => {
          return stmnt.name == controller.join;
        })
      : null;
    let joinType = joinStmnt ? joinStmnt.type : null;

    const getEditFields = controller.edit_fields;
    const fields = [...model.field];

    // If a field that is actually not a hardcoded field in the model fields itself but is a foreign key, include them in model fields as well
    if (model.join.length) {
      for (let child of model.join) {
        // But check first if this is included or not. Someone might have hardcoded this in the model fields as well as included in join field
        if (!fields.find((item) => item[0] === child.foreign_key)) {
          fields.push([child.foreign_key, 'integer', [], this.ucFirst(child.name) + ' ID', '', '']);
        }
      }
    }

    let drop_down_generators = '';
    let drop_down_generators_holders = '';
    let drop_down_generators_initialize = '';
    let upload_field = null;

    let join_table_fields_reference = [];

    let foreignTable;

    getEditFields.forEach((editField) => {
      const splitted = editField.split('~');
      const edit_field = splitted[0];
      let isMappingField = false;
      let field = fields.find((item) => item[0] === edit_field.split('|')[0]); // This split is required to be compatible with autocomplete as well
      if (splitted.length > 1) {
        const _foreignTable = splitted[1].split('.');
        const foreignTableName = _foreignTable[0];
        const foreignTableFiled = _foreignTable[1];
        join_table_fields_reference.push({ field: splitted[1] });
        foreignTable = this._config.models.find((_model) => _model.name === foreignTableName);
        field = foreignTable.field.find((item) => item[0] === foreignTableFiled);
      }

      if (field.length) {
        const fieldName = field[0];
        const readableFieldName = this.ucFirst(this.toCamelCaseString(fieldName));

        const edit_validation = field[5];
        const strip_validation = this.stripPipe(edit_validation);

        const findEditField = getEditFields.find((item) => {
          let actualField = this.stripPipe(item)[0];
          const splitted = actualField.split('~');
          if (splitted.length > 1) {
            actualField = splitted[0];
          }
          return actualField === field[0];
        });

        if (getFieldType(field[1], findEditField).includes('drop_down')) {
          const actualType = getFieldType(field[1], findEditField);
          const name = field[0];

          const dropdown_model_name = actualType.substring(actualType.indexOf(':') + 1, actualType.indexOf(':('));

          const code = `
                    this.set_dropdown_${name} = async () => {
                      this.dropdown_${name} = await db.${dropdown_model_name}.findAll({raw: true})
                    }
                  `;

          drop_down_generators_holders += `\n this.dropdown_${name} = []; \n`;
          drop_down_generators += `\n ${code} \n`;
          drop_down_generators_initialize += `\n await viewModel.set_dropdown_${name}() \n`;
        }

        edit_fields.push({
          foreignField: splitted.length > 1 ? splitted[1] : false,
          joinType: joinType,
          name: field[0],
          type: this.stripPipe(getFieldType(field[1], findEditField))[0],
          actualType: getFieldType(field[1], findEditField), // FE form validation from edit fields
          label: field[3],
          value: '',
          validation: field[5],
        });

        // Prepares an array of name, type, actualType and label for fields
        if (Array.isArray(getEditFields) && getEditFields.includes(fieldName)) {
          strip_validation.forEach((item) => {
            getInputValidations(item, fieldName, readableFieldName, validation_fields, validation_extend_text);
          });
          // }
        }
      }
    });

    // Autocomplete should be enabled no matter if view or API is enabled
    let autocompleteAPIController = '';
    getEditFields.forEach((field) => {
      const splitPipe = field.split('|');
      if (splitPipe.length > 1 && splitPipe[1].startsWith('autocomplete')) {
        autocompleteAPIController = this.generateAutocomplete(field, controller);
      }
    });

    const ucName = this.ucFirst(controller.name);
    const controllerRoute = controller.route.startsWith('/') ? controller.route.slice(1) : controller.route;
    const baseRoute = '/' + controller.portal + '/' + controllerRoute + (controller.paginate ? '/0' : '');
    const actualRoute = '/' + controller.portal + '/' + controllerRoute + '-edit';
    const editPageName = controller.model.replace(new RegExp('_', 'g'), ' ');
    let update_resource = `db.${controller.model}.edit({ {{{main_update_fields}}} {{{method_edit}}} }, id);`;
    // Both views and API will use same view_model
    const viewModelName = controller.name + '_' + controller.portal + `_edit_view_model`;

    // Initialize the view model
    let edit_view_model = fs.readFileSync('../mkdcore/source/portal/Edit_view_model.js', {
      encoding: 'utf-8',
    });

    const year = new Date().getFullYear();

    edit_view_model = this.inject_substitute(edit_view_model, 'year', year);
    edit_view_model = this.inject_substitute(edit_view_model, 'uc_name', ucName);
    edit_view_model = this.inject_substitute(edit_view_model, 'route', '/' + controller.portal + controller.route);

    let edit_template = '';
    let api_edit_template = '';

    edit_view_model = this.inject_substitute(edit_view_model, 'controller_name', controller.name);

    edit_view_model = this.inject_substitute(edit_view_model, 'portal', controller.portal);

    if (drop_down_generators.length) {
      edit_view_model = this.inject_substitute(edit_view_model, 'drop_down_generators', drop_down_generators);

      edit_view_model = this.inject_substitute(edit_view_model, 'drop_down_generators_holders', drop_down_generators_holders);
    } else {
      edit_view_model = this.inject_substitute(edit_view_model, 'drop_down_generators', '');

      edit_view_model = this.inject_substitute(edit_view_model, 'drop_down_generators_holders', '');
    }

    edit_view_model = this.inject_substitute(edit_view_model, 'mapping', this.output_view_model_mapping(this.make_mapping_fields(controller)));

    const acceptedFields = edit_fields
      .map((item) => {
        if (item.foreignField) {
          return item.foreignField.replace(/\./g, '_');
        } else {
          return item.name;
        }
      })
      .join();

    if (isView) {
      // Load controller template
      edit_template = fs.readFileSync('../mkdcore/source/portal/controller_edit.js', {
        encoding: 'utf8',
      });

      edit_template = this.inject_substitute(edit_template, 'joined_table', joinModel ? `viewModel.${joinModel.name} = db.${joinModel.name}` : '');
      // Add autocomplete
      edit_template = this.inject_substitute(edit_template, 'autocomplete', autocompleteAPIController);
      // Additional middleware for POST

      // If Validation fields are empty do not include the validation middleware

      const validationMiddleware =
        Object.keys(validation_fields).length > 0
          ? `ValidationService.validateInput(
              ${JSON.stringify(validation_fields)},
              ${JSON.stringify(validation_extend_text)}
            ),`
          : '';

      edit_template = this.inject_substitute(edit_template, 'additional_post_middlewares', `${validationMiddleware}`);

      edit_template = this.inject_substitute(edit_template, 'additional_get_middlewares', ``);
      edit_template = this.inject_substitute(
        edit_template,
        'get_resource_method',
        !foreignTable ? `db.${controller.model}.getByPK(id);` : `db.${model.name}.get_${model.name}_${foreignTable.name}(id, db);`,
      );
      edit_template = this.inject_substitute(
        edit_template,
        'joined_table_fields',
        !foreignTable
          ? ''
          : join_table_fields_reference
              .map((item) => {
                if (joinType == 'N:1') {
                  return `if (field === '${item.field}') {                      
                            viewModel.form_fields[field] = values['${joinStmnt.as}'].map((item) => item.${item.field.split('.')[1]});
                            return;
                          }`;
                } else {
                  return `if (field === '${item.field}') {
                            viewModel.form_fields[field] = values["${joinStmnt.as}"]["${item.field.split('.')[1]}"];
                            return;
                          }`;
                }
              })
              .join('\t\n\t'),
      );

      edit_template = this.inject_substitute(edit_template, 'portal', controller.portal);
      edit_template = this.inject_substitute(edit_template, 'uc_name', ucName);

      edit_template = this.inject_substitute(edit_template, 'route', controllerRoute + '-edit');

      edit_template = this.inject_substitute(edit_template, 'view_model_name', viewModelName);

      edit_template = this.inject_substitute(edit_template, 'cc_view_model_name', this.toCamelCaseString(viewModelName));
      edit_template = this.inject_substitute(
        edit_template,
        'view_model_arguments',
        `db.${controller.model},"xyzEdit ${editPageName}", "","","/${controller.portal + controller.route}"`,
      );

      if (this._config.packages.permission.active) {
        edit_template = this.inject_substitute(edit_template, 'permission', `PermissionService.verifyPermission(role, "${controller.portal}"),`);
      } else {
        edit_template = this.inject_substitute(edit_template, 'permission', '');
      }

      const viewName = controller.portal + '/Edit_' + ucName;

      edit_template = this.inject_substitute(edit_template, 'ucModelName', this.ucFirst(editPageName));
      edit_template = this.inject_substitute(edit_template, 'ucName', ucName);
      edit_template = this.inject_substitute(edit_template, 'update_resource', update_resource);
      edit_template = this.inject_substitute(edit_template, 'base_route', baseRoute);

      edit_template = this.inject_substitute(edit_template, 'method_edit_pre', controller.method_edit_pre.join('\n\n'));
      let methodEdit = controller.method_edit.length > 0 ? ',' + controller.method_edit.join(',\n') : '';
      edit_template = this.inject_substitute(edit_template, 'method_edit', methodEdit);
      edit_template = this.inject_substitute(edit_template, 'method_edit_success', controller.method_edit_success.join('\n\n'));

      edit_template = this.inject_substitute(edit_template, 'viewName', viewName);

      // set dropdown
      if (drop_down_generators_initialize.length) {
        edit_template = this.inject_substitute(edit_template, 'initialize_drop_down_generators', drop_down_generators_initialize);
      } else {
        edit_template = this.inject_substitute(edit_template, 'initialize_drop_down_generators', '');
      }

      if (controller.activity_log) {
        let activityLog = `
                await db.activity_log.insert({
                  action: 'EDIT',
                  name: '${controller.controller}',
                  portal: '${controller.portal}',
                  data: JSON.stringify({${acceptedFields}, ${methodEdit}}),
                });          
              `;
        edit_template = this.inject_substitute(edit_template, 'activity_log', activityLog);
      } else {
        edit_template = this.inject_substitute(edit_template, 'activity_log', '');
      }

      const success_redirect = baseRoute;

      var mainUpdateFields = [],
        joinedUpdateFields = [],
        joinedUpdateFieldsSingluar = [];

      for (let i = 0; i < edit_fields.length; i++) {
        let item = edit_fields[i];
        if (!item.foreignField) {
          mainUpdateFields.push(item.name);
        } else {
          continue;
        }
      }

      for (let i = 0; i < edit_fields.length; i++) {
        if (edit_fields[i].foreignField) {
          let item = edit_fields[i];
          if (joinType == 'N:1') {
            joinedUpdateFields.push(`'${item.name}': ${item.foreignField.replace(/\./g, '_')}[index]`);
            joinedUpdateFieldsSingluar.push(`'${item.name}': ${item.foreignField.replace(/\./g, '_')}`);
          } else {
            joinedUpdateFields.push(`'${item.name}': ${item.foreignField.replace(/\./g, '_')}`);
          }
        } else {
          continue;
        }
      }
      mainUpdateFields = mainUpdateFields.join();
      joinedUpdateFields = joinedUpdateFields.join();
      joinedUpdateFieldsSingluar = joinedUpdateFieldsSingluar.join();

      var joinedTableUpdateTemplate = '';
      if (joinModel && joinStmnt) {
        if (joinType == 'N:1') {
          joinedTableUpdateTemplate += `
               if (resourceExists.${joinStmnt.as}) {
                 if (resourceExists.${joinStmnt.as}.length == 1) {
                   resourceExists.${joinStmnt.as}.forEach(async (item) => {
                     data = await db.${joinStmnt.name}.edit(helpers.filterEmptyFields({ ${joinedUpdateFieldsSingluar} }), item.id);
                     if (!data) {
                       viewModel.error = 'Something went wrong';
                       return res.render('${viewName}', viewModel);
                     }
                   });
                 } else {
                  resourceExists.${joinStmnt.as}.forEach(async (item, index) => {
                    data = await db.${joinStmnt.name}.edit(helpers.filterEmptyFields({ ${joinedUpdateFields} }), item.id);
                    if (!data) {
                      viewModel.error = 'Something went wrong';
                      return res.render('${viewName}', viewModel);
                    }
                  });
                 }
               }
                `;
        } else {
          joinedTableUpdateTemplate += `
                if (resourceExists.${joinStmnt.as}) {
                  data = await db.${joinStmnt.name}.edit(helpers.filterEmptyFields({ ${joinedUpdateFields} }), resourceExists.${joinStmnt.as}.id);
                  if (!data) {
                    viewModel.error = 'Something went wrong';
                    return res.render('${viewName}', viewModel);
                  }
                }
                `;
        }
      }

      //need to check if the joind table is of join type N:1
      //if yes we'll do foreach loop edit eeverything back

      edit_template = this.inject_substitute(edit_template, 'main_update_fields', mainUpdateFields);
      edit_template = this.inject_substitute(edit_template, 'acceptedFields', acceptedFields);
      edit_template = this.inject_substitute(edit_template, 'joined_model_update_action', joinedTableUpdateTemplate);

      let editFormFields = edit_fields
        .map((item) => {
          if (item.foreignField) {
            return `'${item.foreignField}': ${item.foreignField.replace(/\./g, '_')}`;
          } else {
            return `${item.name}`;
          }
        })
        .join(',');
      edit_template = this.inject_substitute(edit_template, 'form_fields', editFormFields);

      edit_template = this.inject_substitute(edit_template, 'success_redirect', success_redirect);

      edit_view_model = this.inject_substitute(edit_view_model, 'form_heading', `this.heading = "xyzEdit ${editPageName}"`);

      edit_view_model = this.inject_substitute(edit_view_model, 'form_action', `this.action = "${actualRoute}"`);

      // View (Uses same view of add)
      let edit_view = fs.readFileSync('../mkdcore/source/views/custom/Edit.eta', {
        encoding: 'utf-8',
      });

      // Generate the form fields
      //TODO: add foreign table mappings
      let mappings = model.mapping;
      let foreignTableMappings = {};
      if (joinModel) {
        var key,
          keys = Object.keys(joinModel.mapping);
        var n = keys.length;
        while (n--) {
          key = keys[n];
          foreignTableMappings[`${joinModel.name}.${key}`] = joinModel.mapping[key];
        }
      }
      mappings = { ...mappings, ...foreignTableMappings };
      const form = inputFieldMapper(edit_fields, mappings, {
        tableName: controller.model,
      });

      edit_view = this.inject_substitute(
        edit_view,
        'formContent',
        `
              <form action="/${controller.portal}${controller.route}-edit/<%= it.form_fields['id'] %>" method="POST"  ${upload_field ? 'enctype="multipart/form-data"' : ''}>
              ${form}
               <div class="form-group pl-3">
                   <button type="submit" class="btn btn-primary">xyzSubmit</button>
                 </div>
               </form>
              `,
      );

      edit_view = this.inject_substitute(edit_view, 'role', controller.portal);

      this._render_list['../../release/views/' + viewName + '.eta'] = edit_view;
    } else {
      edit_view_model = this.inject_substitute(edit_view_model, 'form_heading', ``);

      edit_view_model = this.inject_substitute(edit_view_model, 'form_action', '');
    }

    if (isAPI) {
      // Load controller template
      api_edit_template = fs.readFileSync('../mkdcore/source/portal/api_controller_edit.js', {
        encoding: 'utf8',
      });
      // Add autocomplete
      api_edit_template = this.inject_substitute(api_edit_template, 'autocomplete', !controller.view ? autocompleteAPIController : '');

      api_edit_template = this.inject_substitute(api_edit_template, 'portal', controller.portal);

      api_edit_template = this.inject_substitute(api_edit_template, 'route', controllerRoute + '-edit');

      // If Validation fields are empty do not include the validation middleware
      api_edit_template = this.inject_substitute(
        api_edit_template,
        'additional_put_middlewares',
        Object.keys(validation_fields).length > 0
          ? `ValidationService.validateInput(
                  ${JSON.stringify(validation_fields)},
                  ${JSON.stringify(validation_extend_text)}
                ),`
          : '',
      );

      api_edit_template = this.inject_substitute(api_edit_template, 'view_model_name', viewModelName);
      api_edit_template = this.inject_substitute(api_edit_template, 'cc_view_model_name', this.toCamelCaseString(viewModelName));
      api_edit_template = this.inject_substitute(api_edit_template, 'view_model_arguments', `db.${controller.model}`);
      api_edit_template = this.inject_substitute(api_edit_template, 'lc_model_name', controller.model);
      api_edit_template = this.inject_substitute(api_edit_template, 'update_resource', update_resource);
      api_edit_template = this.inject_substitute(api_edit_template, 'main_update_fields', mainUpdateFields);
      api_edit_template = this.inject_substitute(api_edit_template, 'acceptedFields', acceptedFields);
      api_edit_template = this.inject_substitute(api_edit_template, 'ucModelName', this.ucFirst(editPageName));
      let methodEdit = controller.method_edit.length > 0 ? ',' + controller.method_edit.join(',\n') : '';
      api_edit_template = this.inject_substitute(api_edit_template, 'method_edit_pre', controller.method_edit_pre.join('\n\n'));
      api_edit_template = this.inject_substitute(api_edit_template, 'method_edit', methodEdit);
      api_edit_template = this.inject_substitute(api_edit_template, 'method_edit_success', controller.method_edit_success.join('\n\n'));

      if (controller.activity_log) {
        let activityLog = `
                await db.activity_log.insert({
                  action: 'EDIT',
                  name: '${controller.controller}',
                  portal: '${controller.portal}',
                  data: JSON.stringify({${acceptedFields}, ${methodEdit}}),
                });          
              `;
        api_edit_template = this.inject_substitute(api_edit_template, 'activity_log', activityLog);
      } else {
        api_edit_template = this.inject_substitute(api_edit_template, 'activity_log', '');
      }
    }

    const formatFormFields = edit_fields.reduce((accumulator, currentValue) => {
      if (!accumulator[currentValue.name]) {
        if (currentValue.foreignField) {
          accumulator[currentValue.foreignField] = currentValue.value;
        } else {
          accumulator[currentValue.name] = currentValue.value;
        }
      }
      return accumulator;
    }, {});

    // adding id for form fields for selecting from table using the id to update record
    formatFormFields.id = '';

    edit_view_model = this.inject_substitute(edit_view_model, 'fields', JSON.stringify(formatFormFields));

    this._render_list['../../release/view_models/' + viewModelName + '.js'] = edit_view_model;

    return [edit_template, api_edit_template];
  };

  // View means detail here
  this.view = function (controller, isView, isAPI) {
    const model = this._models.find((model) => {
      return model.name === controller.model;
    });

    const ucName = this.ucFirst(controller.name);
    const controllerRoute = controller.route.startsWith('/') ? controller.route.slice(1) : controller.route;
    const fields = [...model.field];
    const viewPageName = controller.model.replace(new RegExp('_', 'g'), ' ');

    // If a field that is actually not a hardcoded field in the model fields itself but is a foreign key, include them in model fields as well
    if (model.join.length > 0) {
      for (let child of model.join) {
        // But check first if this is included or not. Someone might have hardcoded this in the model fields as well as included in join field
        if (!fields.find((item) => item[0] === child.foreign_key)) {
          fields.push([child.foreign_key, 'integer', [], this.ucFirst(child.name) + ' ID', '', '']);
        }
      }
    }
    const detail_fields = [];
    const controllerViewFields = controller.view_fields;

    let foreignTable;
    let foreignTableFields = [];
    let foreignTableMappings = {};
    let joinObject = null;
    if (controller.join.length > 0) {
      joinObject = model.join.find((stmnt) => {
        return stmnt.name == controller.join;
      });
    }

    let joinType = joinObject ? joinObject.type : null;

    controllerViewFields.forEach((viewField) => {
      const splitted = viewField.split('~');
      const view_field = splitted[0];
      let isMappingField = false;
      let field = fields.find((item) => item[0] === view_field);
      if (splitted.length > 1) {
        const _foreignTable = splitted[1].split('.');
        const foreignTableName = _foreignTable[0];
        const foreignTableField = _foreignTable[1];
        foreignTableFields.push(foreignTableField);
        foreignTable = this._models.find((_model) => _model.name === foreignTableName);
        field = foreignTable.field.find((item) => item[0] === foreignTableField);
        var key,
          keys = Object.keys(foreignTable.mapping);
        var n = keys.length;
        while (n--) {
          key = keys[n];
          foreignTableMappings[`${foreignTable.name}.${key}`] = foreignTable.mapping[key];
        }
        if (Object.keys(foreignTable.mapping).includes(field[0])) isMappingField = true;
      }

      detail_fields.push({
        joinType: joinType,
        isMappingField: isMappingField,
        foreignField: splitted.length > 1 ? splitted[1] : false,
        name: field[0],
        type: field[1],
        label: field[3],
        value: '',
      });
    });

    // Both views and API will use same view_model
    const viewModelName = controller.name + '_' + controller.portal + '_detail_view_model';

    // Initialize the view model
    let detail_view_model = fs.readFileSync('../mkdcore/source/portal/View_view_model.js', {
      encoding: 'utf-8',
    });

    const year = new Date().getFullYear();
    detail_view_model = this.inject_substitute(detail_view_model, 'year', year);
    detail_view_model = this.inject_substitute(detail_view_model, 'uc_name', ucName);

    detail_view_model = this.inject_substitute(detail_view_model, 'route', '/' + controller.portal + controller.route);

    let detail_template = '';
    let api_detail_template = '';

    if (isView) {
      detail_template = fs.readFileSync('../mkdcore/source/portal/controller_view.js', {
        encoding: 'utf8',
      });

      detail_template = this.inject_substitute(detail_template, 'uc_name', 'View_' + ucName);
      detail_template = this.inject_substitute(
        detail_template,
        'retrieve_method',
        foreignTable ? `db.${model.name}.get_${model.name}_${foreignTable.name}(id, db)` : `db.${model.name}.getByPK(id);`,
      );

      detail_template = this.inject_substitute(detail_template, 'portal', controller.portal);

      detail_template = this.inject_substitute(detail_template, 'route', controllerRoute + '-view');

      detail_template = this.inject_substitute(detail_template, 'view_model_name', viewModelName);
      detail_template = this.inject_substitute(detail_template, 'cc_view_model_name', this.toCamelCaseString(viewModelName));
      detail_template = this.inject_substitute(
        detail_template,
        'view_model_arguments',
        `db.${controller.model},"${this.ucFirst(viewPageName)} xyzdetails","","","/${controller.portal + controller.route}"`,
      );

      const mappings_fields = Object.keys(model.mapping);

      let mappings_str = '';

      mappings_str += mappings_fields.map((field) => `data.${field} = db.${model.name}.${field}_mapping()[data.${field}];`).join('\n');
      mappings_str +=
        '\n' +
        Object.keys(foreignTableMappings)
          .map((key) => {
            if (foreignTable) {
              if (joinType == 'N:1') {
                return `data['${key}'] = data.${joinObject.as}.map(item => {
                     return db.${key}_mapping()[item.${key.split('.')[1]}];
                   })`;
              } else {
                return `data['${key}'] = db.${key}_mapping()[data.${key}];`;
              }
            } else {
              return `data['${key}'] = db.${key}_mapping()[data.${key.split('.')[1]}];`;
            }
          })
          .join('\n');
      detail_template = this.inject_substitute(detail_template, 'retrieve_resource_mapping', mappings_str);

      if (this._config.packages.permission.active) {
        detail_template = this.inject_substitute(detail_template, 'permission', `PermissionService.verifyPermission(role, "${controller.portal}"),`);
      } else {
        detail_template = this.inject_substitute(detail_template, 'permission', '');
      }

      detail_template = this.inject_substitute(detail_template, 'ucModelName', this.ucFirst(viewPageName));
      detail_template = this.inject_substitute(
        detail_template,
        'detail_fields',
        detail_fields
          .map((item) => {
            if (item.foreignField) {
              return `'${item.foreignField}':"N/A"`;
            } else {
              return `${item.name}:"N/A"`;
            }
          })
          .join(),
      );

      detail_template = this.inject_substitute(
        detail_template,
        'success_detail_fields',
        detail_fields
          .map((viewField) => {
            const viewFieldName = viewField.name;
            if (joinObject && viewField.foreignField) {
              if (joinType && joinType == 'N:1' && !viewField.isMappingField) {
                return `'${joinObject.name}.${viewFieldName}': data["${joinObject.as}"].length > 0 ? data["${joinObject.as}"].map(item => {return item.${viewFieldName}}) : ""`;
              } else if (viewField.isMappingField) {
                return `'${joinObject.name}.${viewFieldName}': data["${joinObject.name}.${viewFieldName}"] || "N/A"`;
              } else {
                return `'${joinObject.name}.${viewFieldName}': data["${joinObject.as}"]["${viewFieldName}"] || "N/A"`;
              }
            } else {
              return `${viewFieldName}:data["${viewFieldName}"] || "N/A"`;
            }
          })
          .join(),
      );

      detail_template = this.inject_substitute(
        detail_template,
        'detail_fields_error',
        detail_fields
          .map((item) => {
            if (item.foreignField) {
              return `'${item.foreignField}':"N/A"`;
            } else {
              return `${item.name}:"N/A"`;
            }
          })
          .join(),
      );

      detail_template = this.inject_substitute(detail_template, 'method_view_pre', controller.method_view_pre.join('\n\n'));

      detail_template = this.inject_substitute(detail_template, 'method_view', controller.method_view.length > 0 ? ',' + controller.method_view.join(',\n') : '');

      detail_template = this.inject_substitute(detail_template, 'method_view_success', controller.method_view_success.join('\n\n'));

      detail_view_model = this.inject_substitute(detail_view_model, 'detail_heading', 'this.heading = "' + this.ucFirst(viewPageName) + ' xyzdetails' + '"');

      const detail_list = detail_fields.reduce((accumulator, currentValue) => {
        if (!accumulator[currentValue]) {
          if (currentValue.foreignField) {
            accumulator[currentValue.foreignField] = currentValue.value;
          } else {
            accumulator[currentValue.name] = currentValue.value;
          }
        }
        return accumulator;
      }, {});

      detail_view_model = this.inject_substitute(detail_view_model, 'fields', JSON.stringify(detail_list));

      // View
      let detail_view = fs.readFileSync('../mkdcore/source/views/custom/View.eta', {
        encoding: 'utf-8',
      });

      const detail_view_content = detail_fields.map((viewField) => {
        const viewFieldName = viewField.name;
        if (joinObject && viewField.foreignField) {
          if (joinType && joinType == 'N:1') {
            if (viewField.type == 'image') {
              return `
                          <tr>
                            <th>${viewField.label}:</th>
                            <td>                  
                              <% if(it.detail_fields["${joinObject.name}.${viewFieldName}"].length > 0) {%>
                                <% it.detail_fields["${joinObject.name}.${viewFieldName}"].forEach(item => {%>
                                   <img src="<%= item %>" style="width:300px; height:auto; max-height:400px; object-fit: contain; border-radius:4px;"></br>
                                <% }) %>
                              <% } else {%>
                                  N/A
                              <% } %>
                            </td>
                          </tr>
                      `;
            } else {
              return `
                          <tr>
                            <th>${viewField.label}:</th>
                            <td>                  
                              <% if(it.detail_fields["${joinObject.name}.${viewFieldName}"].length > 0) {%>
                                <% it.detail_fields["${joinObject.name}.${viewFieldName}"].forEach(item => {%>
                                    <%= item %></br>
                                <% }) %>
                              <% } else {%>
                                  N/A
                              <% } %>
                            </td>
                          </tr>
                      `;
            }
          } else {
            if (viewField.type == 'image') {
              return `
                      <tr>
                        <th>${viewField.label}:</th>
                        <td>
                         <img src="<%= it.detail_fields['${joinObject.name}.${viewFieldName}'] %>" style="width:300px; height:auto; max-height:400px; object-fit: contain; border-radius:4px;">                       
                        </td>
                      </tr>
                      `;
            } else {
              return `
                      <tr>
                        <th>${viewField.label}:</th>
                        <td>
                          <%= it.detail_fields["${joinObject.name}.${viewFieldName}"] %>
                        </td>
                      </tr>
                      `;
            }
          }
        } else {
          if (viewField.type == 'image') {
            return `
                      <tr>
                        <th>${viewField.label}:</th>
                        <td>
                        <img src="<%= it.detail_fields['${viewFieldName}'] %>" style="width:300px; height:auto; max-height:400px; object-fit: contain; border-radius:4px;">  
                        </td>
                      </tr>
                  `;
          } else {
            return `
                      <tr>
                        <th>${viewField.label}:</th>
                        <td>
                          <%= it.detail_fields["${viewFieldName}"] %>
                        </td>
                      </tr>
                  `;
          }
        }
      });

      detail_view = this.inject_substitute(detail_view, 'detail_items', detail_view_content.join('\n'));
      detail_view = this.inject_substitute(detail_view, 'role', controller.portal);

      this._render_list['../../release/views/' + controller.portal + '/' + 'View_' + ucName + '.eta'] = detail_view;
    } else {
      detail_view_model = this.inject_substitute(detail_view_model, 'detail_heading', '');
    }
    if (isAPI) {
      // Load controller template
      api_detail_template = fs.readFileSync('../mkdcore/source/portal/api_controller_view.js', {
        encoding: 'utf8',
      });
      api_detail_template = this.inject_substitute(api_detail_template, 'portal', controller.portal);

      api_detail_template = this.inject_substitute(api_detail_template, 'route', controllerRoute);

      api_detail_template = this.inject_substitute(api_detail_template, 'view_model_name', viewModelName);
      api_detail_template = this.inject_substitute(api_detail_template, 'cc_view_model_name', this.toCamelCaseString(viewModelName));
      api_detail_template = this.inject_substitute(api_detail_template, 'view_model_arguments', `db.${controller.model}`);

      api_detail_template = this.inject_substitute(api_detail_template, 'ucModelName', this.ucFirst(viewPageName));

      api_detail_template = this.inject_substitute(
        api_detail_template,
        'detail_fields',
        detail_fields
          .map((item) => {
            const findField = foreignTableFields.find((field) => field === item.name);
            const itemName = item.name;
            return `${itemName}:data${foreignTable && findField ? "['" + foreignTable.name + "']" : ''}["${itemName}"] || ""`;
          })
          .join(),
      );

      api_detail_template = this.inject_substitute(
        api_detail_template,
        'retrieve_method',
        foreignTable ? `db.${model.name}.get_${model.name}_${foreignTable.name}(id, db)` : `db.${model.name}.getByPK(id);`,
      );

      api_detail_template = this.inject_substitute(api_detail_template, 'method_view_pre', controller.method_view_pre.join('\n\n'));
      api_detail_template = this.inject_substitute(api_detail_template, 'method_view', controller.method_view.join(',\n'));

      api_detail_template = this.inject_substitute(api_detail_template, 'method_view_success', controller.method_view_success.join('\n\n'));
    }
    this._render_list['../../release/view_models/' + viewModelName + '.js'] = detail_view_model;

    return [detail_template, api_detail_template];
  };

  this.delete = function (controller, isView, isAPI) {
    const ucName = this.ucFirst(controller.name);
    const ucModelName = this.ucFirst(controller.model);
    const lcModelName = controller.model;
    const controllerRoute = controller.route.startsWith('/') ? controller.route.slice(1) : controller.route;
    const baseRoute = '/' + controller.portal + '/' + controllerRoute + (controller.paginate ? '/0' : '');
    const viewPageName = controller.model.replace(new RegExp('_', 'g'), ' ');
    const viewModelName = controller.name + '_' + controller.portal + '_delete_view_model';

    let delete_view_model = fs.readFileSync('../mkdcore/source/portal/Delete_view_model.js', {
      encoding: 'utf-8',
    });

    const year = new Date().getFullYear();

    delete_view_model = this.inject_substitute(delete_view_model, 'year', year);

    delete_view_model = this.inject_substitute(delete_view_model, 'uc_name', ucName);

    delete_view_model = this.inject_substitute(delete_view_model, 'controller_name', controller.name);

    delete_view_model = this.inject_substitute(delete_view_model, 'portal', controller.portal);

    // Replace with real_delete if is_real_delete
    delete_view_model = this.inject_substitute(delete_view_model, 'delete_type', controller.is_real_delete ? 'realDelete' : 'delete');

    this._render_list['../../release/view_models/' + viewModelName + '.js'] = delete_view_model;

    let delete_template = '';
    let api_delete_template = '';

    if (isView) {
      delete_template = fs.readFileSync('../mkdcore/source/portal/controller_delete.js', {
        encoding: 'utf8',
      });

      delete_template = this.inject_substitute(delete_template, 'portal', controller.portal);

      delete_template = this.inject_substitute(delete_template, 'uc_name', ucName);

      delete_template = this.inject_substitute(delete_template, 'lc_model_name', lcModelName);

      delete_template = this.inject_substitute(delete_template, 'delete_type', controller.is_real_delete ? 'realDelete' : 'delete');

      if (controller.activity_log) {
        let activityLog = `
                await db.activity_log.insert({
                  action: 'DELETE',
                  name: '${controller.controller}',
                  portal: '${controller.portal}',
                  data: JSON.stringify(exists),
                });          
              `;
        delete_template = this.inject_substitute(delete_template, 'activity_log', activityLog);
      } else {
        delete_template = this.inject_substitute(delete_template, 'activity_log', '');
      }

      delete_template = this.inject_substitute(delete_template, 'route', controllerRoute);

      delete_template = this.inject_substitute(delete_template, 'view_model_name', viewModelName);

      delete_template = this.inject_substitute(delete_template, 'cc_view_model_name', this.toCamelCaseString(viewModelName));

      delete_template = this.inject_substitute(delete_template, 'viewModel_arguments', `db.${controller['model']}`);

      if (this._config.packages.permission.active) {
        delete_template = this.inject_substitute(delete_template, 'permission', `PermissionService.verifyPermission(role, "${controller.portal}"),`);
      } else {
        delete_template = this.inject_substitute(delete_template, 'permission', '');
      }

      const paginate_based_route = baseRoute;

      delete_template = this.inject_substitute(delete_template, 'ucModelName', this.ucFirst(viewPageName));
      delete_template = this.inject_substitute(delete_template, 'paginate_based_route', paginate_based_route);
      delete_template = this.inject_substitute(delete_template, 'method_delete_pre', controller.method_delete_pre.join('\n\n'));

      delete_template = this.inject_substitute(delete_template, 'method_delete_success', controller.method_delete_success.join('\n\n'));
    }

    if (isAPI) {
      api_delete_template = fs.readFileSync('../mkdcore/source/portal/api_controller_delete.js', {
        encoding: 'utf8',
      });
      api_delete_template = this.inject_substitute(api_delete_template, 'portal', controller.portal);

      api_delete_template = this.inject_substitute(api_delete_template, 'uc_name', ucName);

      api_delete_template = this.inject_substitute(api_delete_template, 'lc_model_name', lcModelName);

      api_delete_template = this.inject_substitute(api_delete_template, 'delete_type', controller.is_real_delete ? 'realDelete' : 'delete');

      api_delete_template = this.inject_substitute(api_delete_template, 'route', controllerRoute + '-delete');

      api_delete_template = this.inject_substitute(api_delete_template, 'view_model_name', viewModelName);

      api_delete_template = this.inject_substitute(api_delete_template, 'cc_view_model_name', this.toCamelCaseString(viewModelName));

      api_delete_template = this.inject_substitute(api_delete_template, 'viewModel_arguments', `db.${controller.model}`);

      api_delete_template = this.inject_substitute(api_delete_template, 'ucModelName', this.ucFirst(viewPageName));

      api_delete_template = this.inject_substitute(api_delete_template, 'method_delete_pre', controller.method_delete_pre.join('\n\n'));

      api_delete_template = this.inject_substitute(api_delete_template, 'method_delete_success', controller.method_delete_success.join('\n\n'));

      if (controller.activity_log) {
        let activityLog = `
                await db.activity_log.insert({
                  action: 'DELETE',
                  name: '${controller.controller}',
                  portal: '${controller.portal}',
                  data: JSON.stringify(exists),
                });          
              `;
        api_delete_template = this.inject_substitute(api_delete_template, 'activity_log', activityLog);
      } else {
        api_delete_template = this.inject_substitute(api_delete_template, 'activity_log', '');
      }
    }

    return [delete_template, api_delete_template];
  };

  this.bulk_delete = function (controller, isView, isAPI) {
    const controllerRoute = controller.route.startsWith('/') ? controller.route.slice(1) : controller.route;

    const viewModelName = controller.name + '_' + controller.portal + '_delete_view_model';
    const baseRoute = '/' + controller.portal + '/' + controllerRoute + (controller.paginate ? '/0' : '');

    let bulk_delete_template = '';
    let api_bulk_delete_template = '';

    if (isView) {
      bulk_delete_template = fs.readFileSync('../mkdcore/source/portal/bulk_delete_controller.js', {
        encoding: 'utf8',
      });

      bulk_delete_template = this.inject_substitute(bulk_delete_template, 'portal', controller.portal);

      bulk_delete_template = this.inject_substitute(bulk_delete_template, 'route', controllerRoute);

      bulk_delete_template = this.inject_substitute(bulk_delete_template, 'view_model_name', viewModelName);

      if (controller.bulk_delete) {
        const bulkDeleteTemplate = `await db.${controller.model}.${controller.is_real_delete ? 'realDelete' : 'delete'}(idArray);`;
        bulk_delete_template = this.inject_substitute(bulk_delete_template, 'bulk_delete_function', bulkDeleteTemplate);
      } else {
        bulk_delete_template = this.inject_substitute(bulk_delete_template, 'bulk_delete_function', '');
      }

      bulk_delete_template = this.inject_substitute(bulk_delete_template, 'cc_view_model_name', this.toCamelCaseString(viewModelName));

      bulk_delete_template = this.inject_substitute(bulk_delete_template, 'viewModel_arguments', `db.${controller['model']}`);

      const paginate_based_route = baseRoute;

      bulk_delete_template = this.inject_substitute(bulk_delete_template, 'paginate_based_route', paginate_based_route);
      bulk_delete_template = this.inject_substitute(bulk_delete_template, 'bulk_delete_pre', controller.method_bulk_delete_pre.join('\n'));
      bulk_delete_template = this.inject_substitute(bulk_delete_template, 'bulk_delete_success', controller.method_bulk_delete_success.join('\n'));

      if (controller.activity_log) {
        let activityLog = `
                await db.activity_log.insert({
                  action: 'BULK_DELETE',
                  name: '${controller.controller}',
                  portal: '${controller.portal}',
                  data: JSON.stringify({ids}),
                });          
              `;
        bulk_delete_template = this.inject_substitute(bulk_delete_template, 'activity_log', activityLog);
      } else {
        bulk_delete_template = this.inject_substitute(bulk_delete_template, 'activity_log', '');
      }

      // TODO Bulk Delete API: ?? BULK delete may not be required for API
    }

    return [bulk_delete_template, api_bulk_delete_template];
  };

  this.generateFileUpload = function (controller, isView, isAPI) {
    const model = this._models.find((model) => {
      return model.name === controller.model;
    });

    const extractedImageFileFields = model.field.filter((field) => {
      const fieldType = field[1];
      return fieldType === 'image' || fieldType === 'file';
    });

    if (extractedImageFileFields.length) {
    } else {
      return '';
    }
  };

  this.generateAutocomplete = function (field, controller) {
    const pipeSplitted = field.split('|');
    const fieldName = pipeSplitted[0];
    const rightPipeFields = pipeSplitted[1].split(':');
    const tableName = rightPipeFields[1];
    const searchFields = rightPipeFields[2].split('+');

    const autocompleteRoute = '/' + controller.portal + '/' + tableName + '/' + searchFields.join('_') + '-autocomplete';

    // If that autocomplete already exists in the controller do not generate
    if (this._autocomplete_routes.includes(autocompleteRoute)) {
      return '';
    }

    this._autocomplete_routes.push(autocompleteRoute);

    // Generate the javascript file
    let autoCompleteJS = fs.readFileSync('../mkdcore/source/custom/autocomplete.js', 'utf-8');

    autoCompleteJS = this.inject_substitute(autoCompleteJS, 'className', controller.model + '-' + searchFields.join('_') + '-' + 'autocomplete');
    autoCompleteJS = this.inject_substitute(autoCompleteJS, 'URL', `"${this._config.config.base_url + autocompleteRoute}"`);

    autoCompleteJS = this.inject_substitute(autoCompleteJS, 'DisplayText', searchFields.map((item) => `item.${item}`).join('+'));

    if (this._autocompleteJS.includes('$(document).ready(function () {')) {
      this._autocompleteJS = this._autocompleteJS.replace('$(document).ready(function () {', ['$(document).ready(function () {', autoCompleteJS].join('\n'));
    } else {
      this._autocompleteJS = '$(document).ready(function () {' + autoCompleteJS + '})';
    }
    this._render_list['../../release/public/js/autocomplete.js'] = this._autocompleteJS;

    let autoCompleteControllerTemplate = fs.readFileSync('../mkdcore/source/portal/Autocomplete_controller.js', 'utf-8');

    autoCompleteControllerTemplate = this.inject_substitute(autoCompleteControllerTemplate, 'portal', controller.portal);

    autoCompleteControllerTemplate = this.inject_substitute(autoCompleteControllerTemplate, 'tableName', tableName);

    autoCompleteControllerTemplate = this.inject_substitute(autoCompleteControllerTemplate, 'searchField', searchFields.join('_'));
    autoCompleteControllerTemplate = this.inject_substitute(autoCompleteControllerTemplate, 'returnFields', searchFields.map((_field) => `"${_field}"`).join());

    autoCompleteControllerTemplate = this.inject_substitute(
      autoCompleteControllerTemplate,
      'SELECT_OR',
      searchFields
        .map(
          (f) => `{${f}:{
                        [Sequelize.Op.like]: "%" + term + "%"
                      }}`,
        )
        .join(','),
    );
    return autoCompleteControllerTemplate;
  };
};
