const app = require('express').Router();
const { Op } = require('sequelize');
const db = require('../../models');
const { protect } = require('../../middlewares/auth_middleware.js');
const UploadService = require('../../services/UploadService');
const mailService = require('../../services/MailService');
const upload = UploadService.upload('files/file');
const sgMail = require('@sendgrid/mail');
const { updateAlias } = require('../../services/ForwardEmailService');
const { sendSMS } = require('../../services/TelnyxService');
const StripeService = require('../../services/StripeApi');

app.post('/api/user/invite', protect, async function (req, res, next) {
  try {
    const user = req.user;
    const { email, phone, cohort_id, name } = req.body;
    if (!email && !phone) {
      return res.status(400).json({
        status: 'fail',
        message: 'Please provide email or phone',
      });
    }
    const belongToCohort = await db.cohort.findOne({
      where: {
        [Op.and]: {
          parent_1: user.id,
          parent_2: null,
        },
        id: cohort_id,
      },
    });
    if (!belongToCohort) {
      return res.status(404).json({
        status: 'fail',
        message: 'Cohort not found',
      });
    }
    const invite_code = `${Date.now()}`;
    const invite = await db.invite.insert({ invite_code, cohort_id, from: user.id, status: 1 });
    sgMail.setApiKey('*********************************************************************');

    const mailTemplate = await mailService.template('cohort-invite');
    mailService.initialize({
      from: '<EMAIL>',
      to: email,
    });
    const injectedMailTemplate = mailService.inject(
      {
        body: mailTemplate.html,
        subject: mailTemplate.subject,
      },
      {
        receiver: name,
        name: belongToCohort.name,
        invite_code,
        first_name: user.first_name,
        last_name: user.last_name,
      },
    );
    await sgMail.send(injectedMailTemplate);
    await sendSMS({
      to: phone,
      text: `${user.first_name} has invited you to join Co-Parent Hub to better manage communication about your child. Check your email for details. `,
    });
    res.status(200).json({
      status: 'success',
      message: `You have invited ${invite_code} `,
    });
  } catch (error) {
    return res.status(400).json({
      error: true,
      message: error,
    });
  }
});

app.post('/api/user/join', protect, async function (req, res, next) {
  try {
    const user = req.user;
    const { invite_code } = req.body;
    if (!invite_code) {
      return res.status(400).json({
        status: 'fail',
        message: 'Please provide an invite code',
      });
    }

    const invite = await db.invite.findOne({
      where: { invite_code },
      include: [
        {
          model: db.user,
          required: false,
          as: 'user',
        },
      ],
    });
    console.log(invite);
    if (!invite) {
      return res.status(404).json({
        status: 'fail',
        message: 'Invalid invite code',
      });
    }
    const cohort = await db.cohort.findOne({
      where: {
        [Op.or]: [
          {
            [Op.and]: [
              {
                parent_1: invite.from,
              },
              { parent_2: user.id },
            ],
            [Op.and]: [{ parent_1: user.id }, { parent_2: invite.from }],
          },
        ],
      },
    });
    if (cohort || invite.from === user.id) {
      return res.status(400).json({
        status: 'fail',
        message: 'Can not join this cohort',
      });
    }
    const cohorts = await db.cohort.findAll({
      where: {
        [db.Sequelize.Op.or]: [{ parent_1: req.user.id }, { parent_2: req.user.id }],
      },
    });

    if(req.user.subscription){
    const { subscription: subObj } = req.user;
    const { itemId, quantity } = subObj;

    let addedQty = 1;
    if (cohorts.length === 0) {
      addedQty = 0;
    }
    if (quantity == 0) {
      addedQty = 1;
    }

    const subscription = await StripeService.stripeType('subscription_update', {
      subscriptionId: req.user.subscription.id,
      params: {
        items: [
          {
            id: itemId,
            quantity: quantity + addedQty,
          },
        ],
      },
    });
  }

    await db.cohort.edit(
      {
        parent_2: user.id,
      },
      invite.cohort_id,
    );
    const cohortObj = await db.cohort.getByPK(invite.cohort_id);
    const parent1 = await db.user.get_user_credential(cohortObj.parent_1, db);
    const parent2 = await db.user.get_user_credential(cohortObj.parent_2, db);
    const recipients = `${parent1?.credential?.email || ''} ${parent2?.credential?.email || ''}`;
    if (cohortObj.shared_email) {
      await updateAlias({ name: cohortObj.shared_email.split('@')[0], recipients });
    }
    await invite.destroy();
    res.status(200).json({
      status: 'success',
      message: `Joined cohort`,
    });
  } catch (error) {
    console.log(error);
    return res.status(400).json({
      error: true,
      message: error.message,
    });
  }
});

module.exports = app;
