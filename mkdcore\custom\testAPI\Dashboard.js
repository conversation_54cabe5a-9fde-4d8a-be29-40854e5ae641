const SessionService = require('../../services/SessionService');
const app = require('express').Router();
const DB = require('../../models');
const cohort = require('../../models/cohort');

const role = 2;

app.get('/member/dashboard', SessionService.verifySessionMiddleware(role, 'member'), async function (req, res, next) {
  res.render('member/Dashboard', {
    get_page_name: () => 'Dashboard',
    _base_url: '/member/dashboard',
  });
});

app.post('/cohort/add', async function (req, res, next) {
  try {
    const parent1 = await DB.user.findOne({ where: { id: req.body.parent1 } });
    const parent2 = await DB.user.getByPK(req.body.parent2);

    console.log('Parents Data');
    console.log(parent1);

    if (!parent1 || !parent2) return res.status(400).json({ error: "Parent doesn't exists" });

    const cohort = await DB.cohort.insert({
      parent_1: req.body.parent1,
      parent_2: req.body.parent2,
      name: req.body.name,
      shared_email: req.body.shared_email,
      shared_phone: req.body.shared_phone,
      status: '1',
    });
    if (cohort) return res.status(200).json({ data: cohort });
    else throw new Error('cohort not added!');
  } catch (error) {
    console.log(error);
    return res.status(400).json({
      error: true,
      message: error.message,
    });
  }
});

app.get('/cohort/get/:parentId', async function (req, res, next) {
  try {
    let parentId = req.params.parentId;
    const cohorts = await DB.cohort.findAll({
      where: {
        [DB.Sequelize.Op.or]: [{ parent_1: parentId }, { parent_2: parentId }],
      },
    });

    let parentIds = [];
    parentIds.push(parentId);
    cohorts.forEach((element) => {
      parentIds.push(element.parent_1 == parentId ? element.parent_2 : element.parent_1);
    });

    const parents = await DB.user.findAll({
      where: {
        id: parentIds,
      },
    });

    await cohorts.forEach(async (cohort) => {
      console.log(cohort.dataValues)
      cohort.dataValues.parent_1_info = await parents.find((parent) => parent.id == cohort.dataValues.parent_1);
      cohort.dataValues.parent_2_info = await parents.find((parent) => parent.id == cohort.dataValues.parent_2);
    });

    // console.log('Cohorts', cohorts);

    if (cohorts) return res.status(200).json({ data: cohorts });
    else throw new Error('cohort not added!');
  } catch (error) {
    console.log(error);
    return res.status(400).json({
      error: true,
      message: error.message,
    });
  }
});

app.get('/cohort/expense/:cohortId/:parentId', async function (req, res, next) {
  try {
    
    let parentId = req.params.parentId;
    let cohortId = req.params.cohortId;

    const cohorts = await DB.cohort.getByPK(cohortId);
    const expenses = await DB.expense.findAll({where: {
      cohort_id : cohortId
    }});
    

    let parentIds = [];
    parentIds.push(parentId);
    cohorts.forEach((element) => {
      parentIds.push(element.parent_1 == parentId ? element.parent_2 : element.parent_1);
    });

    const parents = await DB.user.findAll({
      where: {
        id: parentIds,
      },
    });

    await cohorts.forEach(async (cohort) => {
      console.log(cohort.dataValues)
      cohort.dataValues.parent_1_info = await parents.find((parent) => parent.id == cohort.dataValues.parent_1);
      cohort.dataValues.parent_2_info = await parents.find((parent) => parent.id == cohort.dataValues.parent_2);
    });

    // console.log('Cohorts', cohorts);

    if (cohorts) return res.status(200).json({ data: cohorts });
    else throw new Error('cohort not added!');
  } catch (error) {
    console.log(error);
    return res.status(400).json({
      error: true,
      message: error.message,
    });
  }
});

module.exports = app;
