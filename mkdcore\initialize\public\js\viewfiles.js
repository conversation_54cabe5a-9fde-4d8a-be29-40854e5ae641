var $$ = Dom7;

async function getCohortFiles() {
  try {
    app.preloader.show();

    const user = localStorage.getItem('user') ? JSON.parse(localStorage.getItem('user')) : {};
    const cohort_id = $$('#files-cohort-drop-filter').val();
    if (!cohort_id) {
      return app.preloader.hide();
    }
    let response = await fetch(`/api/user/files/${cohort_id}`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
    });

    let res = await response.json();

    let html = '';
    if (!res.files || res.files.length == 0) {
      html += `
          <div class="card">
            <div class="block-title text-align-center padding-vertical">No Co-Files Found</div>
          </div>
        `;
      app.preloader.hide();
      $$('#expenses-tabs').html(html);
    } else {
      res.files.forEach((files) => {
        var date = new Date(files.created_at);
        html += `
                    <div class="card singleExpense">
                      <div class="card-header singleExpenseCardHeader">${files.label}</div>
                      <div class="card-content card-content-padding expenseInfoContainer">
                        <div class="row no-margin">
                          <div class="col-60 viewFiles-infoCol">
                            <div class="block-title text-align-left no-margin-vertical infoFiles">
                              ${files.notes}
                            </div>
                            <div class="block-title text-align-left no-margin-vertical infoSwap">
                              Date: ${date.toLocaleString('en-us', { month: 'short', day: 'numeric', year: 'numeric' })}
                            </div>
                            <div class="block-title text-align-left no-margin-vertical infoSwap">
                              <b> Uploaded By : </b> ${files.user.first_name} ${files.user.last_name}
                            </div>
                          </div>
                          <div class="col-40 expenseInfoCallToAction">
                            ${
                              files.upload_by === user.id
                                ? `<button onclick="handleDelete(${files.id})" class="button button-raised button-fill color-red">
                                <a href="#" class="color-white">
                                  Delete
                                </a>
                              </button>`
                                : ''
                            }
          <a  target='_blank' href="${files.file_url}" download class="external button button-raised button-fill">
          Download
                              </a>
                          </div>
                        </div>
                      </div>
                    </div>
                  `;
      });
      app.preloader.hide();
      $$('#expenses-tabs').html(html);
    }
  } catch (error) {
    app.preloader.hide();
    // app.dialog.alert(error);
    console.log(error);
  }
}

async function uploadFile() {
  try {
    var mainview = app.view.main;

    const fileLabel = $$('#file-label').val();
    const fileNotes = $$('#file-notes').val();
    const fileUpload = $$('#addfiles-upload-files-button')[0].files[0];
    if (validateFileInput(fileNotes, fileUpload, fileLabel)) {
      const formData = new FormData();
      formData.append('file', fileUpload);
      formData.append('notes', fileNotes);
      formData.append('label', fileLabel);

      const cohort_id = $$('#files-cohort-drop-add').val();
      app.preloader.show();
      let res = await fetch(`/api/user/files/${cohort_id}`, {
        method: 'POST',
        body: formData,
      });
      app.preloader.hide();
      mainview.router.navigate({ name: 'files' });
    } else {
      throw 'Something went wrong, Please try again';
    }
  } catch (error) {
    app.preloader.hide();
    app.dialog.alert(error);
    // console.log(error);
  }
}

function validateFileInput(fileNotes, fileUpload, fileLabel) {
  var AllFieldsValidated = true;
  var errortoThrow = 'These field(s) are required: <br>';
  if (fileNotes.trim() == '') {
    errortoThrow += 'Co-Files Notes <br>';
    AllFieldsValidated = false;
  }
  if (fileUpload == undefined) {
    errortoThrow += 'Co-Files Upload File <br>';
    AllFieldsValidated = false;
  }
  if (fileLabel.trim() == '') {
    errortoThrow += 'Co-Files Label <br>';
    AllFieldsValidated = false;
  }
  if (AllFieldsValidated == false) {
    throw errortoThrow;
  }
  return AllFieldsValidated;
}

async function handleDelete(id) {
  try {
    app.preloader.show();

    let res = await fetch(`/api/user/files/${id}`, {
      method: 'DELETE',
    });
    app.preloader.hide();
    await getCohortFiles();
  } catch (error) {
    app.preloader.hide();
    console.log(error);
  }
}
