/*Powered By: Manaknightdigital Inc. https://manaknightdigital.com/ Year: 2020*/
/**
 * Role builder
 * @copyright 2020 Manaknightdigital Inc.
 * @link https://manaknightdigital.com
 * @license Proprietary Software licensing
 * <AUTHOR>
 *
 */

const fs = require('fs');
const path = require('path');
const Builder = require('./Builder');

module.exports = function (config) {
  this._config = config;
  this._roles = [];
  Builder.call(this);

  this.set_roles = function (roles) {
    this._roles = roles;
  };

  this.build = function () {
    for (let i = 0; i < this._roles.length; i++) {
      const role = this._roles[i];
      try {
        let folderName = '../../release/controllers/' + role.name.toLowerCase();
        if (fs.existsSync(folderName)) {
          // fs.readdir(folderName, (err, files) => {
          //   if (err) throw err;
          //   for (const file of files) {
          //     if (fs.existsSync(path.join(folderName, file))) {
          //       fs.unlink(path.join(folderName, file), (err) => {
          //         if (err) throw err;
          //       });
          //     }
          //   }
          // });
        } else {
          this.createDirectoriesRecursive(folderName);
        }
        let viewFolderName = '../../release/views/' + role.name.toLowerCase();
        if (fs.existsSync(viewFolderName)) {
          // fs.readdir(viewFolderName, (err, files) => {
          //   if (err) throw err;
          //   for (const file2 of files) {
          //     if (fs.existsSync(path.join(viewFolderName, file2))) {
          //       fs.unlink(path.join(viewFolderName, file2), (err) => {
          //         if (err) throw err;
          //       });
          //     }
          //   }
          // });
        } else {
          this.createDirectoriesRecursive(viewFolderName);
        }
      } catch (err) {
        console.error('Role Builder Build Error', err);
      }
    }
  };

  this.destroy = function () {
    for (let i = 0; i < this._roles.length; i++) {
      const role = this._roles[i];
      try {
        let folderName = path.join(
          __dirname,
          '../../release/controllers/',
          role.name.toLowercase(),
        );
        if (fs.existsSync(folderName)) {
          fs.readdir(folderName, (err, files) => {
            if (err) throw err;
            for (const file of files) {
              fs.unlinkSync(path.join(folderName, file));
            }
          });
          fs.unlinkSync(path.join(folderName, file));
        } else {
          fs.mkdirSync(folderName);
        }
      } catch (err) {
        console.error('Role Builder Build Error', err);
      }
    }
  };
};
