<!DOCTYPE html>
<html>
  <head>
    <!-- Required meta tags-->
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no, viewport-fit=cover" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <!-- Color theme for statusbar (Android only) -->
    <meta name="theme-color" content="#2196f3" />
    <!-- Your app title -->
    <title>Co-Parent</title>
  </head>

  <body>
    <!-- App root element -->
    <div id="app">
      <div class="page">
        <!-- Top Navbar -->
        <div class="navbar">
          <div class="navbar-bg"></div>
          <div class="navbar-inner">
            <div class="left"></div>
            <div class="title text-align-center">Invite Co-Parent</div>
            <div class="right"></div>
          </div>
        </div>

        <!-- Scrollable page content -->
        <div class="page-content no-swipe-panel">
          <form>
            <div class="list no-hairlines">
              <ul>
                <li class="item-content item-input item-input-outline">
                  <div class="item-inner">
                    <div class="item-title item-label">Co-Parent's Name</div>
                    <div class="item-input-wrap">
                      <input id="inviteCoparent-name" type="text" placeholder="Co-Parent's Name" required validate data-validate-on-blur="true" />
                      <span class="input-clear-button"></span>
                    </div>
                  </div>
                </li>
                <li class="item-content item-input item-input-outline">
                  <div class="item-inner">
                    <div class="item-title item-label">Co-Parent's Email</div>
                    <div class="item-input-wrap">
                      <input id="inviteCoparent-email" type="text" placeholder="Co-Parent's Email" required validate data-validate-on-blur="true" />
                      <span class="input-clear-button"></span>
                    </div>
                  </div>
                </li>

                <li class="item-content item-input item-input-outline">
                  <div class="item-inner">
                    <div class="item-title item-label">Co-Parent's Phone</div>
                    <div class="item-input-wrap">
                      <input
                        id="inviteCoparent-phone"
                        type="tel"
                        placeholder="************"
                        required
                        validate
                        data-validate-on-blur="true"
                        data-error-message="Please enter number in the format 'xxx xxx xxxx'"
                        pattern="\(?(\d{3})\)?[-\.\s]?(\d{3})[-\.\s]?(\d{4})"
                      />
                      <span class="input-clear-button"></span>
                    </div>
                  </div>
                </li>

                <li>
                  <a target="_blanks" onclick="inviteCopatent()" class="inviteCoParentbutton">
                    <button class="button button-raised button-fill margin-vertical">Invite Co-Parent</button>
                  </a>
                </li>

                <li>
                  <a href="/sharedPhone/" class="inviteCoParentbutton">
                    <button class="button button-raised button-fill margin-vertical">Invite Later</button>
                  </a>
                </li>
              </ul>
            </div>
          </form>
        </div>
      </div>
    </div>
  </body>
</html>
