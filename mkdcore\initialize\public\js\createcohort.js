//Script to Populate The Phone Numbers Available.
async function createCohortGetPhoneNumber() {
    try {
        let response = await fetch('/api/user/available_phones', {
            method: 'GET',
            headers: {
                Accept: 'application/json',
                'Content-Type': 'application/json',
            }
        });
        console.log(response);
        if (response.status == 200) {
            res = await response.json();
            console.log(res.phones);
            res.phones.forEach(phone => {
                html = `
                <option class="phone-selection-item" value="${phone.phone_number}">${phone.phone_number}</option>
                `
                $$('#create-cohort-phone-selection').append(html);
            });
        }
        if (response.status == 400) {
            throw "Couldn't fetch phone number, Please try again"
        }
    } catch (error) {
        app.dialog.alert(error);
    }
}
