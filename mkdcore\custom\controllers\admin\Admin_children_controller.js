'use strict';

const app = require('express').Router();
const Sequelize = require('sequelize');
const logger = require('../../services/LoggingService');
let pagination = require('../../services/PaginationService');
let SessionService = require('../../services/SessionService');
let JwtService = require('../../services/JwtService');
const ValidationService = require('../../services/ValidationService');
const PermissionService = require('../../services/PermissionService');
const UploadService = require('../../services/UploadService');
const AuthService = require('../../services/AuthService');
const db = require('../../models');
const helpers = require('../../core/helpers');

const role = 1;

app.get('/admin/children/:num', SessionService.verifySessionMiddleware(role, 'admin'), async function (req, res, next) {
  try {
    let session = req.session;
    let paginateListViewModel = require('../../view_models/children_admin_list_paginate_view_model');

    var viewModel = new paginateListViewModel(db.child, 'Children', session.success, session.error, '/admin/children');

    const format = req.query.format ? req.query.format : 'view';
    const direction = req.query.direction ? req.query.direction : 'ASC';
    const per_page = req.query.per_page ? req.query.per_page : 10;
    let order_by = req.query.order_by ? req.query.order_by : viewModel.get_field_column()[0];
    let orderAssociations = [];
    viewModel.set_order_by(order_by);
    let joins = order_by.includes('.') ? order_by.split('.') : [];
    order_by = order_by.includes('.') ? joins[joins.length - 1] : order_by;
    if (joins.length > 0) {
      for (let i = joins.length - 1; i > 0; i--) {
        orderAssociations.push(`${joins[i - 1]}`);
      }
    }
    // Check for flash messages
    const flashMessageSuccess = req.flash('success');
    if (flashMessageSuccess && flashMessageSuccess.length > 0) {
      viewModel.success = flashMessageSuccess[0];
    }
    const flashMessageError = req.flash('error');
    if (flashMessageError && flashMessageError.length > 0) {
      viewModel.error = flashMessageError[0];
    }

    viewModel.set_id(req.query.id ? req.query.id : '');
    viewModel.set_cohort_name(req.query.cohort_name ? req.query.cohort_name : '');
    viewModel.set_name(req.query.name ? req.query.name : '');
    viewModel.set_age(req.query.age ? req.query.age : '');

    let where = helpers.filterEmptyFields({
      id: viewModel.get_id(),
      name: { [Sequelize.Op.like]: `${viewModel.get_name()}%` },
      age: viewModel.get_age(),
    });

    let associatedWhere = helpers.filterEmptyFields({
      name: viewModel.get_cohort_name(),
    });
    const isAssociationRequired = Object.keys(associatedWhere).length > 0 ? true : false;

    const count = await db.child._count(where, [{ model: db.cohort, where: associatedWhere, required: isAssociationRequired, as: 'cohort' }]);

    let sort_url = '';

    if (req.originalUrl.includes('?')) {
      if (req.originalUrl.includes('order_by')) {
        let url_query = req.originalUrl.split('?')[1];
        sort_url = `${url_query.split('order_by')[0]}`;
      } else {
        sort_url = `${req.originalUrl.split('?')[1]}`;
      }
    }
    viewModel.set_total_rows(count);
    viewModel.set_per_page(+per_page);
    viewModel.set_page(+req.params.num);
    viewModel.set_query(req.query);
    viewModel.set_sort_base_url(`/admin/children/${+req.params.num}?${sort_url}`);
    viewModel.set_sort(direction);

    const list = await db.child.get_cohort_paginated(
      db,
      associatedWhere,
      viewModel.get_page() - 1 < 0 ? 0 : viewModel.get_page(),
      viewModel.get_per_page(),
      where,
      order_by,
      direction,
      orderAssociations,
    );

    viewModel.set_list(list);

    viewModel.cohort = await db.cohort;

    if (format == 'csv') {
      const csv = viewModel.to_csv();
      return res
        .set({
          'Content-Type': 'text/csv',
          'Content-Disposition': 'attachment; filename="export.csv"',
        })
        .send(csv);
    }

    // if (format != 'view') {
    //   res.json(viewModel.to_json());
    // } else {
    // }

    return res.render('admin/Children', viewModel);
  } catch (error) {
    console.error(error);
    viewModel.error = error.message || 'Something went wrong';
    return res.render('admin/Children', viewModel);
  }
});

app.get('/admin/children-add', SessionService.verifySessionMiddleware(role, 'admin'), async function (req, res, next) {
  if (req.session.csrf === undefined) {
    req.session.csrf = SessionService.randomString(100);
  }

  const childrenAdminAddViewModel = require('../../view_models/children_admin_add_view_model');

  const viewModel = new childrenAdminAddViewModel(db.child, 'Add child', '', '', '/admin/children');

  res.render('admin/Add_Children', viewModel);
});

app.post(
  '/admin/children-add',
  SessionService.verifySessionMiddleware(role, 'admin'),
  ValidationService.validateInput(
    { cohort_id: 'required', name: 'required', age: 'required' },
    { 'cohort_id.required': 'CohortId is required', 'name.required': 'Name is required', 'age.required': 'Age is required' },
  ),
  async function (req, res, next) {
    if (req.session.csrf === undefined) {
      req.session.csrf = SessionService.randomString(100);
    }
    const childrenAdminAddViewModel = require('../../view_models/children_admin_add_view_model');

    const viewModel = new childrenAdminAddViewModel(db.child, 'Add child', '', '', '/admin/children');

    // TODO use separate controller for image upload
    //  {{{upload_field_setter}}}

    const { cohort_id, name, age } = req.body;

    viewModel.form_fields = {
      ...viewModel.form_fields,
      cohort_id,
      name,
      age,
    };

    try {
      if (req.validationError) {
        viewModel.error = req.validationError;
        return res.render('admin/Add_Children', viewModel);
      }

      viewModel.session = req.session;

      const data = await db.child.insert({ cohort_id, name, age });

      if (!data) {
        viewModel.error = 'Something went wrong';
        return res.render('admin/Add_Children', viewModel);
      }

      await db.activity_log.insert({
        action: 'ADD',
        name: 'Admin_children_controller.js',
        portal: 'admin',
        data: JSON.stringify({ cohort_id, name, age }),
      });

      req.flash('success', 'Child created successfully');
      return res.redirect('/admin/children/0');
    } catch (error) {
      console.error(error);
      viewModel.error = error.message || 'Something went wrong';
      return res.render('admin/Add_Children', viewModel);
    }
  },
);

app.get('/admin/children-edit/:id', SessionService.verifySessionMiddleware(role, 'admin'), async function (req, res, next) {
  let id = req.params.id;
  if (req.session.csrf === undefined) {
    req.session.csrf = SessionService.randomString(100);
  }
  const childrenAdminEditViewModel = require('../../view_models/children_admin_edit_view_model');

  const viewModel = new childrenAdminEditViewModel(db.child, 'Edit child', '', '', '/admin/children');

  try {
    const exists = await db.child.getByPK(id);

    if (!exists) {
      req.flash('error', 'Child not found');
      return res.redirect('/admin/children/0');
    }
    const values = exists;
    Object.keys(viewModel.form_fields).forEach((field) => {
      viewModel.form_fields[field] = values[field] || '';
    });
    viewModel.cohort = db.cohort;
    return res.render('admin/Edit_Children', viewModel);
  } catch (error) {
    console.error(error);
    viewModel.error = error.message || 'Something went wrong';
    return res.render('admin/Edit_Children', viewModel);
  }
});

app.post(
  '/admin/children-edit/:id',
  SessionService.verifySessionMiddleware(role, 'admin'),
  ValidationService.validateInput(
    { cohort_id: 'required', name: 'required', age: 'required' },
    { 'cohort_id.required': 'CohortId is required', 'name.required': 'Name is required', 'age.required': 'Age is required' },
  ),
  async function (req, res, next) {
    let id = req.params.id;
    if (req.session.csrf === undefined) {
      req.session.csrf = SessionService.randomString(100);
    }

    const childrenAdminEditViewModel = require('../../view_models/children_admin_edit_view_model');

    const viewModel = new childrenAdminEditViewModel(db.child, 'Edit child', '', '', '/admin/children');

    const { cohort_id, name, age } = req.body;

    viewModel.form_fields = {
      ...viewModel.form_fields,
      cohort_id,
      name,
      age,
    };

    delete viewModel.form_fields.id;

    try {
      if (req.validationError) {
        viewModel.error = req.validationError;
        return res.render('admin/Edit_Children', viewModel);
      }

      const resourceExists = await db.child.getByPK(id);
      if (!resourceExists) {
        req.flash('error', 'Child not found');
        return res.redirect('/admin/children/0');
      }

      viewModel.session = req.session;

      let data = await db.child.edit({ cohort_id, name, age }, id);
      if (!data) {
        viewModel.error = 'Something went wrong';
        return res.render('admin/Edit_Children', viewModel);
      }

      if (resourceExists.cohort) {
        data = await db.cohort.edit(helpers.filterEmptyFields({}), resourceExists.cohort.id);
        if (!data) {
          viewModel.error = 'Something went wrong';
          return res.render('admin/Edit_Children', viewModel);
        }
      }

      await db.activity_log.insert({
        action: 'EDIT',
        name: 'Admin_children_controller.js',
        portal: 'admin',
        data: JSON.stringify({ cohort_id, name, age }),
      });

      req.flash('success', 'Child edited successfully');

      return res.redirect('/admin/children/0');
    } catch (error) {
      console.error(error);
      viewModel.error = error.message || 'Something went wrong';
      return res.render('admin/Edit_Children', viewModel);
    }
  },
);

app.get(
  '/admin/children-view/:id',
  SessionService.verifySessionMiddleware(role, 'admin'),

  async function (req, res, next) {
    try {
      let id = req.params.id;

      const childrenAdminDetailViewModel = require('../../view_models/children_admin_detail_view_model');

      var viewModel = new childrenAdminDetailViewModel(db.child, 'Child details', '', '', '/admin/children');

      const data = await db.child.getByPK(id);

      if (!data) {
        viewModel.error = 'Child not found';
        viewModel.detail_fields = { ...viewModel.detail_fields, id: 'N/A', cohort_id: 'N/A', name: 'N/A', age: 'N/A' };
      } else {
        viewModel.detail_fields = {
          ...viewModel.detail_fields,
          id: data['id'] || 'N/A',
          cohort_id: data['cohort_id'] || 'N/A',
          name: data['name'] || 'N/A',
          age: data['age'] || 'N/A',
        };
      }

      res.render('admin/View_Children', viewModel);
    } catch (error) {
      console.error(error);
      viewModel.error = error.message || 'Something went wrong';
      viewModel.detail_fields = { ...viewModel.detail_fields, id: 'N/A', cohort_id: 'N/A', name: 'N/A', age: 'N/A' };
      res.render('admin/View_Children', viewModel);
    }
  },
);

app.get('/admin/children-delete/:id', SessionService.verifySessionMiddleware(role, 'admin'), async function (req, res, next) {
  let id = req.params.id;

  const childrenAdminDeleteViewModel = require('../../view_models/children_admin_delete_view_model');

  const viewModel = new childrenAdminDeleteViewModel(db.child);

  try {
    const exists = await db.child.getByPK(id);

    if (!exists) {
      req.flash('error', 'Child not found');
      return res.redirect('/admin/children/0');
    }

    viewModel.session = req.session;

    await db.child.realDelete(id);

    await db.activity_log.insert({
      action: 'DELETE',
      name: 'Admin_children_controller.js',
      portal: 'admin',
      data: JSON.stringify(exists),
    });

    req.flash('success', 'Child deleted successfully');

    return res.redirect('/admin/children/0');
  } catch (error) {
    console.error(error);
    req.flash('error', error.message || 'Something went wrong');
    return res.redirect('/admin/children/0');
  }
});

// APIS

app.get('/admin/api/children', JwtService.verifyTokenMiddleware(role), async function (req, res, next) {
  try {
    const user_id = req.user_id;
    const session = req.session;
    let listViewModel = require('../../view_models/children_admin_list_paginate_view_model');
    let viewModel = new listViewModel(db.child, 'Children', session.success, session.error, '/admin/children');
    const direction = req.query.direction ? req.query.direction : 'ASC';
    const page = req.query.page ? parseInt(req.query.page) : 1;
    const limit = req.query.limit ? parseInt(req.query.limit) : 10;
    const offset = (page - 1) * limit;
    let order_by = req.query.order_by ? req.query.order_by : viewModel.get_field_column()[0];
    let orderAssociations = [];
    viewModel.set_order_by(order_by);
    let joins = order_by.includes('.') ? order_by.split('.') : [];
    order_by = order_by.includes('.') ? joins[joins.length - 1] : order_by;
    if (joins.length > 0) {
      for (let i = joins.length - 1; i > 0; i--) {
        orderAssociations.push(`${joins[i - 1]}`);
      }
    }

    viewModel.set_id(req.query.id ? req.query.id : '');
    viewModel.set_cohort_name(req.query.cohort_name ? req.query.cohort_name : '');
    viewModel.set_name(req.query.name ? req.query.name : '');
    viewModel.set_age(req.query.age ? req.query.age : '');

    let where = helpers.filterEmptyFields({
      id: viewModel.get_id(),
      name: { [Sequelize.Op.like]: `${viewModel.get_name()}%` },
      age: viewModel.get_age(),
    });

    let associatedWhere = helpers.filterEmptyFields({
      name: viewModel.get_cohort_name(),
    });
    const isAssociationRequired = Object.keys(associatedWhere).length > 0 ? true : false;

    let include = [{ model: db.cohort, where: associatedWhere, required: isAssociationRequired, as: 'cohort' }];

    const { rows: allItems, count } = await db.child.findAndCountAll({
      where: where,
      limit: limit == 0 ? null : limit,
      offset: offset,
      include: include,
      distinct: true,
    });

    const response = {
      items: allItems,
      page,
      nextPage: count > offset + limit ? page + 1 : false,
      retrievedCount: allItems.length,
      fullCount: count,
    };

    return res.status(201).json({ success: true, data: response });
  } catch (error) {
    console.error(error);
    return res.status(500).json({ success: false, message: error.message || 'Something went wrong' });
  }
});

app.post(
  '/admin/api/children-add',
  JwtService.verifyTokenMiddleware(role),
  ValidationService.validateInput(
    { cohort_id: 'required', name: 'required', age: 'required' },
    { 'cohort_id.required': 'CohortId is required', 'name.required': 'Name is required', 'age.required': 'Age is required' },
  ),
  async function (req, res, next) {
    const childrenAdminAddViewModel = require('../../view_models/children_admin_add_view_model');

    const viewModel = new childrenAdminAddViewModel(db.child);

    const { cohort_id, name, age } = req.body;
    try {
      if (req.validationError) {
        return res.status(500).json({ success: false, message: req.validationError });
      }

      const data = await db.child.insert({ cohort_id, name, age });

      if (!data) {
        return res.status(500).json({ success: false, message: 'Something went wrong' });
      }

      await db.activity_log.insert({
        action: 'ADD',
        name: 'Admin_children_controller.js',
        portal: 'admin',
        data: JSON.stringify({ cohort_id, name, age }),
      });

      return res.status(201).json({ success: true, message: 'Child created successfully' });
    } catch (error) {
      return res.status(500).json({ success: false, message: 'Something went wrong' });
    }
  },
);

app.put(
  '/admin/api/children-edit/:id',
  JwtService.verifyTokenMiddleware(role),
  ValidationService.validateInput(
    { cohort_id: 'required', name: 'required', age: 'required' },
    { 'cohort_id.required': 'CohortId is required', 'name.required': 'Name is required', 'age.required': 'Age is required' },
  ),
  async function (req, res, next) {
    let id = req.params.id;

    const childrenAdminEditViewModel = require('../../view_models/children_admin_edit_view_model');

    const viewModel = new childrenAdminEditViewModel(db.child);

    const { cohort_id, name, age } = req.body;

    try {
      if (req.validationError) {
        return res.status(500).json({ success: false, message: req.validationError });
      }

      const resourceExists = await db.child.getByPK(id);
      if (!resourceExists) {
        return res.status(404).json({ success: false, message: 'Child not found' });
      }

      const data = await db.child.edit({ cohort_id, name, age }, id);

      if (!data) {
        return res.status(500).json({ success: false, message: 'Something went wrong' });
      }

      await db.activity_log.insert({
        action: 'EDIT',
        name: 'Admin_children_controller.js',
        portal: 'admin',
        data: JSON.stringify({ cohort_id, name, age }),
      });

      return res.json({ success: true, message: 'Child edited successfully' });
    } catch (error) {
      return res.status(500).json({ success: false, message: 'Something went wrong' });
    }
  },
);

app.get('/admin/api/children-view/:id', JwtService.verifyTokenMiddleware(role), async function (req, res, next) {
  let id = req.params.id;

  const childrenAdminDetailViewModel = require('../../view_models/children_admin_detail_view_model');

  const viewModel = new childrenAdminDetailViewModel(db.child);

  try {
    const data = await db.child.getByPK(id);

    if (!data) {
      return res.status(404).json({ message: 'Child not found', data: null });
    } else {
      const fields = { ...viewModel.detail_fields, id: data['id'] || '', cohort_id: data['cohort_id'] || '', name: data['name'] || '', age: data['age'] || '' };
      return res.status(200).json({ data: fields });
    }
  } catch (error) {
    return res.status(404).json({ message: 'Something went wrong', data: null });
  }
});

app.delete('/admin/api/children-delete/:id', JwtService.verifyTokenMiddleware(role), async function (req, res, next) {
  let id = req.params.id;

  const childrenAdminDeleteViewModel = require('../../view_models/children_admin_delete_view_model');

  const viewModel = new childrenAdminDeleteViewModel(db.child);

  try {
    const exists = await db.child.getByPK(id);

    if (!exists) {
      return res.status(404).json({ success: false, message: 'Child not found' });
    }

    await db.child.realDelete(id);

    await db.activity_log.insert({
      action: 'DELETE',
      name: 'Admin_children_controller.js',
      portal: 'admin',
      data: JSON.stringify(exists),
    });

    return res.status(200).json({ success: true, message: 'Child deleted successfully' });
  } catch (error) {
    return res.status(500).json({ success: false, message: 'Something went wrong' });
  }
});

module.exports = app;
