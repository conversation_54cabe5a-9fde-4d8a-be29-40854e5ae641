<!DOCTYPE html>
<html>

<head>
  <!-- Required meta tags-->
  <meta charset="utf-8" />
  <meta name="viewport"
    content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no, viewport-fit=cover" />
  <meta name="apple-mobile-web-app-capable" content="yes" />
  <!-- Color theme for statusbar (Android only) -->
  <meta name="theme-color" content="#2196f3" />
  <!-- Your app title -->
  <title>Co-Parent</title>
</head>

<body>
  <!-- App root element -->
  <div id="app">
    <div class="page" data-name="cohortedit1">
      <!-- Top Navbar -->
      <div class="navbar">
        <div class="navbar-bg"></div>
        <div class="navbar-inner">
          <div class="left">
            <a class="link back color-black">
              <i class="icon icon-back"></i>
              <span class="if-not-md">Back</span>
            </a>
          </div>
          <div class="title text-align-center">Edit Co-Hort</div>
          <div class="right">
            <a href="" onclick="updateCohort()"><i class="f7-icons color-black">checkmark_alt</i></a>
          </div>
        </div>
      </div>

      <!-- Scrollable page content -->
      <div class="page-content no-swipe-panel">
        <div class="list no-hairlines">
          <ul>
            <li class="item-content item-input item-input-outline">
              <div class="item-inner">
                <div class="item-title item-label">Co-Hort Name</div>
                <div class="item-input-wrap">
                  <input placeholder="Enter Cohort Name" type="text" name="cohort-name" id="edit-cohort-cohort-name" />
                  <span class="input-clear-button"></span>
                </div>
              </div>
            </li>

            <li class="item-content item-input item-input-outline">
              <div class="item-inner">
                <div class="item-title item-label" id="editCohortParent1">Parent 1 Default Split</div>
                <div class="item-input-wrap">
                  <input placeholder="Parent One Split" type="number" pattern="[0-9]*" inputmode="numeric" name="name"
                    id="edit-cohort-parent-one-split" min="0" max="100" maxlength="3" />
                  <span class="input-clear-button"></span>
                  <span class="percentagesymbol">%</span>
                </div>
              </div>
            </li>

            <li class="item-content item-input item-input-outline">
              <div class="item-inner">
                <div class="item-title item-label" id="editCohortParent2">Parent 2 Default Split</div>
                <div class="item-input-wrap">
                  <input placeholder="Parent Two Split" type="number" pattern="[0-9]*" inputmode="numeric" name="name"
                    id="edit-cohort-parent-two-split" min="0" max="100" maxlength="3" />
                  <span class="input-clear-button"></span>
                  <span class="percentagesymbol">%</span>
                </div>
              </div>
            </li>
            <li class="item-content item-input item-input-outline">
              <div class="item-inner">
                <button onclick="removeCohort()" class="button button-fill color-red">Remove Co-Hort</button>
              </div>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</body>

</html>