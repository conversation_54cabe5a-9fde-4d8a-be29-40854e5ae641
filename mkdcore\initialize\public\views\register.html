<!DOCTYPE html>
<html>
  <head>
    <!-- Required meta tags-->
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no, viewport-fit=cover" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <!-- Color theme for statusbar (Android only) -->
    <meta name="theme-color" content="#2196f3" />
    <!-- Your app title -->
    <title>Co-Parent</title>
  </head>

  <body>
    <!-- App root element -->
    <div id="app">
      <div class="page no-navbar no-toolbar no-swipeback login-screen-page no-swipe-panel">
        <div class="page-content login-screen-content">
          <div class="login-screen-title">
            <h3>Co-Parent Hub</h3>
            <p>Register</p>
          </div>
          <form>
            <div class="list">
              <ul>
                <li class="item-content item-input item-input-outline">
                  <div class="item-inner">
                    <div class="item-title item-label">First Name</div>
                    <div class="item-input-wrap">
                      <input type="text" name="firstName" id="register-firstName" placeholder="First Name" required />
                      <span class="input-clear-button"></span>
                    </div>
                  </div>
                </li>
                <li class="item-content item-input item-input-outline">
                  <div class="item-inner">
                    <div class="item-title item-label">Last Name</div>
                    <div class="item-input-wrap">
                      <input type="text" name="lastName" id="register-lastName" placeholder="Last Name" required />
                      <span class="input-clear-button"></span>
                    </div>
                  </div>
                </li>
                <li class="item-content item-input item-input-outline">
                  <div class="item-inner">
                    <div class="item-title item-label">Email</div>
                    <div class="item-input-wrap">
                      <input type="email" id="register-email" name="register-email" placeholder="Your Email" required validate data-validate-on-blur="true" />
                      <span class="input-clear-button"></span>
                    </div>
                  </div>
                </li>
                <li class="item-content item-input item-input-outline">
                  <div class="item-inner">
                    <div class="item-title item-label">Phone</div>
                    <div class="item-input-wrap">
                      <input
                        type="tel"
                        id="register-phone"
                        name="register-phone"
                        placeholder="************"
                        required
                        validate
                        data-validate-on-blur="true"
                        data-error-message="Please enter number in the format 'xxx xxx xxxx'"
                        pattern="\(?(\d{3})\)?[-\.\s]?(\d{3})[-\.\s]?(\d{4})"
                      />
                      <span class="input-clear-button"></span>
                    </div>
                  </div>
                </li>
                <li class="item-content item-input item-input-outline">
                  <div class="item-inner">
                    <div class="item-title item-label">Password</div>
                    <div class="item-input-wrap">
                      <input type="password" id="register-password" name="password" placeholder="Your Password" required />
                      <span class="input-clear-button"></span>
                    </div>
                  </div>
                </li>
                <li class="item-content item-input item-input-outline">
                  <div class="item-inner">
                    <div class="item-title item-label">Confirm Password</div>
                    <div class="item-input-wrap">
                      <input type="password" id="register-cPassword" name="cPassword" placeholder="Confirm Password" required />
                      <span class="input-clear-button"></span>
                    </div>
                  </div>
                </li>
              </ul>
            </div>
            <div class="list">
              <ul>
                <li>
                  <a target="_blank" onclick="registerAPI()">
                    <button class="button button-raised button-fill margin-vertical" id="registerButton">Register</button>
                  </a>
                </li>
                <li>
                  <a href="/">
                    <button class="button button-raised button-fill margin-vertical" id="backtoLogin">Already have an account? Sign in</button>
                  </a>
                </li>
              </ul>
            </div>
          </form>
        </div>
      </div>
    </div>
  </body>
</html>
