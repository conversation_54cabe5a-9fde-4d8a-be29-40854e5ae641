const axios = require('axios');
require('dotenv').config();

const accountSid = '**********************************';
const authToken = '75a36786da8db0a4866f118deeebb0ba';
const client = require('twilio')(accountSid, authToken);

let config = {
  auth: { username: accountSid, password: authToken },
};

const twilioUrl = `https://api.twilio.com/2010-04-01/Accounts/${accountSid}`;

exports.getAvailableNumbers = async (AreaCode) => {
  const numbers = await axios.get(`${twilioUrl}/AvailablePhoneNumbers/US/TollFree.json?AreaCode=${AreaCode}`, config);
  return numbers;
};
exports.sendSMS = async ({ to, text }) => {
  const message = await client.messages.create({ body: text, from: '+***********', to });
};

exports.orderNumber = async (phoneNumber, req) => {
  const res = await client.incomingPhoneNumbers.create({
    phoneNumber,
    smsUrl: `https://${req.get('host')}/api/user/message`,
    voiceUrl: `https://${req.get('host')}/api/user/calling`,
    statusCallback: `https://${req.get('host')}/api/user/calling/status`,
  });
  return res.data;
};

exports.getRecordings = async (CallId) => {
  const res = await axios.get(`${twilioUrl}/Calls/${CallId}/Recordings.json`, config);
  return res.data.recordings;
};

exports.releaseNumber = async (phoneNumber) => {
  try {
    let { data } = await axios.get(`${twilioUrl}/IncomingPhoneNumbers.json?PhoneNumber=${phoneNumber}`, config);
    const psid = data.incoming_phone_numbers[0].sid;
    data = await axios.delete(`${twilioUrl}/IncomingPhoneNumbers/${psid}.json`, config);
  } catch (error) {}
};
