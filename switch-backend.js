#!/usr/bin/env node

/**
 * Backend Switcher Script
 * 
 * This script helps you easily switch between local development backend
 * and live production backend without breaking your app.
 * 
 * Usage:
 * node switch-backend.js local    # Switch to localhost:3017
 * node switch-backend.js live     # Switch to https://app.coparenthub.com
 * node switch-backend.js status   # Check current backend setting
 */

const fs = require('fs');
const path = require('path');

const CONFIG_FILE = path.join(__dirname, 'server', 'public', 'js', 'config.js');

function getCurrentMode() {
  try {
    const content = fs.readFileSync(CONFIG_FILE, 'utf8');
    const modeMatch = content.match(/MODE:\s*['"`](\w+)['"`]/);
    return modeMatch ? modeMatch[1] : 'unknown';
  } catch (error) {
    console.error('Error reading config file:', error.message);
    return 'unknown';
  }
}

function switchMode(newMode) {
  try {
    let content = fs.readFileSync(CONFIG_FILE, 'utf8');
    
    // Replace the MODE value
    content = content.replace(
      /MODE:\s*['"`]\w+['"`]/,
      `MODE: '${newMode}'`
    );
    
    fs.writeFileSync(CONFIG_FILE, content);
    console.log(`✅ Successfully switched to ${newMode} backend!`);
    
    if (newMode === 'live') {
      console.log('🌐 Now using: https://app.coparenthub.com');
      console.log('⚠️  Make sure the live backend is accessible and running');
    } else {
      console.log('🏠 Now using: http://localhost:3017');
      console.log('⚠️  Make sure your local server is running on port 3017');
    }
    
  } catch (error) {
    console.error('❌ Error switching backend:', error.message);
  }
}

function showStatus() {
  const currentMode = getCurrentMode();
  console.log(`📊 Current backend mode: ${currentMode}`);
  
  if (currentMode === 'live') {
    console.log('🌐 Using: https://app.coparenthub.com');
  } else if (currentMode === 'local') {
    console.log('🏠 Using: http://localhost:3017');
  } else {
    console.log('❓ Unknown or invalid configuration');
  }
}

function showHelp() {
  console.log(`
🔧 Backend Switcher for Co-Parent Hub

Usage:
  node switch-backend.js local    # Switch to localhost:3017
  node switch-backend.js live     # Switch to https://app.coparenthub.com  
  node switch-backend.js status   # Check current backend setting
  node switch-backend.js help     # Show this help message

Examples:
  # Switch to live backend for production testing
  node switch-backend.js live
  
  # Switch back to local development
  node switch-backend.js local
  
  # Check which backend is currently active
  node switch-backend.js status
`);
}

// Main execution
const command = process.argv[2];

switch (command) {
  case 'local':
    switchMode('local');
    break;
  case 'live':
    switchMode('live');
    break;
  case 'status':
    showStatus();
    break;
  case 'help':
  case '--help':
  case '-h':
    showHelp();
    break;
  default:
    console.log('❌ Invalid command. Use "help" to see available options.');
    showHelp();
}
