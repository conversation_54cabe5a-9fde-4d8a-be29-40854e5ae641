/*Powered By: Manaknightdigital Inc. https://manaknightdigital.com/ Year: 2020*/
/**
 * Graphql builder
 * @copyright 2020 Manaknightdigital Inc.
 * @link https://manaknightdigital.com
 * @license Proprietary Software licensing
 * <AUTHOR>
 *
 */

const fs = require('fs');
const Builder = require('./Builder');

module.exports = function (config) {
  this._config = config;
  this._translations = {};
  this._render_list = {};

  Builder.call(this);

  this.init = function () {
    /**
     * Steps:
     * 1.Loop through all model, make schema.graphql
     * 2.Make Model Files
     * 3.Make Resolvers files
     * 4.Generate index.js(resolver)
     * 5.Generate index.js(models)
     *
     */
    let ignore_model_list = [];
    let ignore_id_field_list = [];
    let role = '';
    let graphql = [];

    if (this._config['graphql']) {
      graphql = this._config['graphql'];
    }

    if (graphql['ignore_models']) {
      ignore_model_list = graphql['ignore_models'];
    }

    if (graphql['ignore_id_field']) {
      ignore_id_field_list = graphql['ignore_id_field'];
    }

    let model_list = [];
    let schema_list = [];
    let pagination_list = [];
    let query_list = [];
    let mutation_list = [];
    let import_files = [];
    let resolver_query = [];
    let resolver_mutation = [];
    let resolver_type = [];
    let resolver_relation = [];
    const today = new Date();
    const year = today.getFullYear();

    // let default_model_template = fs.readFileSync(
    //   "../mkdcore/source/graphql/model.js",
    //   "utf8"
    // );
    let default_resolve_all_template = fs.readFileSync('../mkdcore/source/graphql/resolver_all.js', 'utf8');
    let default_resolve_single_template = fs.readFileSync('../mkdcore/source/graphql/resolver_single.js', 'utf8');
    let default_resolve_delete_template = fs.readFileSync('../mkdcore/source/graphql/resolver_delete.js', 'utf8');
    let default_resolve_edit_template = fs.readFileSync('../mkdcore/source/graphql/resolver_edit.js', 'utf8');
    let default_resolve_add_template = fs.readFileSync('../mkdcore/source/graphql/resolver_add.js', 'utf8');

    let default_resolve_relation_template = fs.readFileSync('../mkdcore/source/graphql/resolver_relation.js', 'utf8');

    let default_resolve_type_template = fs.readFileSync('../mkdcore/source/graphql/resolver_type.js', 'utf8');

    resolve_index_template = fs.readFileSync('../mkdcore/source/graphql/resolver_index.js', 'utf8');

    for (model_key in this._config['models']) {
      // model_template = default_model_template;
      const model = this._config['models'][model_key];
      upper_case_model = this.ucFirst(this._config['models'][model_key]['name']);
      model_name = this._config['models'][model_key]['name'];
      model_list.push(this._config['models'][model_key]['name']);
      // model_template = this.inject_substitute(
      //   model_template,
      //   "subclass_prefix",
      //   this._config["config"]["subclass_prefix"]
      // );
      // model_template = this.inject_substitute(
      //   model_template,
      //   "model",
      //   model_name
      // );
      // model_template = this.inject_substitute(
      //   model_template,
      //   "associations",
      //   "".implode(
      //     "\n",
      //     this.generate_model_association(
      //       this._config["models"][model_key]["field"],
      //       upper_case_model,
      //       ignore_id_field_list
      //     )
      //   )
      // );
      // model_template = this.inject_substitute(
      //   model_template,
      //   "mapping",
      //   this.generate_model_mapping(
      //     this._config["models"][model_key]["mapping"],
      //     upper_case_model
      //   )
      // );
      // model_template = this.inject_substitute(
      //   model_template,
      //   "fields",
      //   "      ".implode(
      //     ",\n      ",
      //     this.generate_model_field(
      //       this._config["models"][model_key]["field"],
      //       this._config["models"][model_key]["timestamp"]
      //     )
      //   )
      // );
      // model_template = this.inject_substitute(
      //   model_template,
      //   "uppercase_model",
      //   upper_case_model
      // );

      // this._render_list['../graphql/src/models/' . model_name . '.js'] = model_template;

      if (ignore_model_list.includes(this._config['models'][model_key]['name'])) {
        continue;
      }

      resolve_all_template = default_resolve_all_template;
      resolve_single_template = default_resolve_single_template;
      resolve_delete_template = default_resolve_delete_template;
      resolve_edit_template = default_resolve_edit_template;
      resolve_add_template = default_resolve_add_template;
      resolve_type_template = default_resolve_type_template;

      resolve_all_template = this.inject_substitute(resolve_all_template, 'model_name', model_name);
      resolve_single_template = this.inject_substitute(resolve_single_template, 'model_name', model_name);

      resolve_delete_template = this.inject_substitute(resolve_delete_template, 'model_name', model_name);

      // EDIT TEMPLATE

      resolve_edit_template = this.inject_substitute(resolve_edit_template, 'model_name', model_name);

      resolve_edit_template = this.inject_substitute(
        resolve_edit_template,
        'update_fields',
        '   ' + this.generate_create_field_list(this._config['models'][model_key]['field'], this._config['models'][model_key]['graphql_edit_fields']).join(',\n   '),
      );

      resolve_edit_template = this.inject_substitute(
        resolve_edit_template,
        'fields',
        '   ' + this.generate_edit_field_list(this._config['models'][model_key]['field'], this._config['models'][model_key]['graphql_edit_fields']).join(',\n   '),
      );

      resolve_edit_template = this.inject_substitute(
        resolve_edit_template,
        'fields_validation',
        '   ' + this.generate_edit_field_validation_list(this._config['models'][model_key]['field'], this._config['models'][model_key]['graphql_edit_fields']).join(',\n   '),
      );

      //  ADD TEMPLATE
      resolve_add_template = this.inject_substitute(resolve_add_template, 'model_name', model_name);

      resolve_add_template = this.inject_substitute(
        resolve_add_template,
        'create_fields',
        '   ' + this.generate_create_field_list(this._config['models'][model_key]['field'], this._config['models'][model_key]['graphql_add_fields']).join(',\n   '),
      );

      resolve_add_template = this.inject_substitute(
        resolve_add_template,
        'fields',
        '   ' + this.generate_add_field_list(this._config['models'][model_key]['field'], this._config['models'][model_key]['graphql_add_fields']).join(',\n   '),
      );

      resolve_add_template = this.inject_substitute(
        resolve_add_template,
        'fields_validation',
        '   ' + this.generate_add_field_validation_list(this._config['models'][model_key]['field'], this._config['models'][model_key]['graphql_add_fields']).join(',\n   '),
      );

      resolve_type_template = this.inject_substitute(resolve_type_template, 'types', '   ' + this.generate_field_types(model_name, this._config['models'][model_key]['field']));

      resolve_all_template = this.inject_substitute(resolve_all_template, 'year', year);
      resolve_single_template = this.inject_substitute(resolve_single_template, 'year', year);
      resolve_delete_template = this.inject_substitute(resolve_delete_template, 'year', year);
      resolve_edit_template = this.inject_substitute(resolve_edit_template, 'year', year);

      resolve_add_template = this.inject_substitute(resolve_add_template, 'year', year);

      schema_list.push(this.generate_schema(model, this._config['models'][model_key]['field'], this._config['models'][model_key]['timestamp'], ignore_id_field_list));

      pagination_list.push(this.generate_schema_pagination(model_name));

      query_list.push(this.generate_schema_query(model_name));
      import_files.push(this.generate_import_files(model_name));
      resolver_query = resolver_query.concat(this.generate_resolver_query(model_name));
      resolver_mutation = resolver_mutation.concat(this.generate_resolver_mutation(model_name));

      resolver_type = resolver_type.concat(this.generate_resolver_types(model_name));
      // resolver_relation = array_merge(resolver_relation,  this.generate_resolver_relation(model_name, value['field']));

      mutation_list.push(this.generate_schema_mutation(model, this._config['models'][model_key]['field']));

      this._render_list['../../releaseGraphql/resolvers/all/all' + this.ucFirst(this.toCamelCaseString(model_name)) + '.js'] = resolve_all_template;
      this._render_list['../../releaseGraphql/resolvers/single/single' + this.ucFirst(this.toCamelCaseString(model_name)) + '.js'] = resolve_single_template;
      this._render_list['../../releaseGraphql/resolvers/delete/delete' + this.ucFirst(this.toCamelCaseString(model_name)) + '.js'] = resolve_delete_template;
      this._render_list['../../releaseGraphql/resolvers/update/update' + this.ucFirst(this.toCamelCaseString(model_name)) + '.js'] = resolve_edit_template;
      this._render_list['../../releaseGraphql/resolvers/create/create' + this.ucFirst(this.toCamelCaseString(model_name)) + '.js'] = resolve_add_template;

      // TYPES
      this._render_list['../../releaseGraphql/resolvers/type/type' + this.ucFirst(this.toCamelCaseString(model_name)) + '.js'] = resolve_type_template;

      relations = this.get_relation_list(model_name, this._config['models'][model_key]['field']);

      for (relation_key in relations) {
        resolve_relation_template = default_resolve_relation_template;
        resolve_relation_template = this.inject_substitute(resolve_relation_template, 'model_name', relations[relation_key]);

        resolve_relation_template = this.inject_substitute(resolve_relation_template, 'upper_model_name', this.ucFirst(this.toCamelCaseString(relations[relation_key])));
        this._render_list['../../releaseGraphql/resolvers/relation/relation' + this.ucFirst(this.toCamelCaseString(relations[relation_key])) + '.js'] = resolve_relation_template;
      }
    }

    resolve_index_template = this.inject_substitute(resolve_index_template, 'import_files', import_files.join('\n'));
    resolve_index_template = this.inject_substitute(resolve_index_template, 'querys', '  ' + resolver_query.join(',\n  '));
    resolve_index_template = this.inject_substitute(resolve_index_template, 'mutations', '  ' + resolver_mutation.join(',\n  '));
    resolve_index_template = this.inject_substitute(resolve_index_template, 'relations', '');

    resolve_index_template = this.inject_substitute(resolve_index_template, 'types', '  ' + resolver_type.join(',\n  '));
    // resolve_index_template = this.inject_substitute(resolve_index_template, 'relations', implode(",\n", resolver_relation));

    this._render_list['../../releaseGraphql/types/schema.graphql'] =
      `scalar Date \n\n` + schema_list.join('\n') + this.schema_pagination_all(pagination_list) + this.schema_query_all(query_list) + this.schema_mutation_all(mutation_list);
    this._render_list['../../releaseGraphql/resolvers/index.js'] = resolve_index_template;
  };

  this.schema_pagination_all = function (model_name) {
    pagination = model_name.join('\n\n');

    code = `
     # ------------------------ PAGINATION ------------------------
     ${pagination}
     \n
     type PageInfo {
       endCursor: String
       hasNextPage: Boolean
     }
     \n`;

    return code;
  };

  this.schema_query_all = function (query_list) {
    queries = query_list.join('\n\n');

    code = `
 
     # ------------------------ QUERY ------------------------
     type Query {
       ${queries}
     }\n`;

    return code;
  };

  this.schema_mutation_all = function (mutation_list) {
    mutations = mutation_list.join('\n\n');

    code = `
     # ------------------------ MUTATION ------------------------
     type Mutation {
       ${mutations}
     }\n`;

    return code;
  };

  this.generate_model_association = function (fields, upper_case_model, ignore_id_field_list) {
    let result = [];
    for (key in fields) {
      field_name = fields[key][0];
      if (field_name.indexOf('_id') > -1 && !ignore_id_field_list.includes(field_name)) {
        let upper_field_name = this.ucFirst(field_name);
        let other_model = field_name.replace('_id', '');
        result.push(`
 ${upper_case_model}.associate = models => {
     ${upper_case_model}.belongsTo(models.${other_model}, {
       foreignKey: "${field_name}",
       constraints: false
     });
 };
 `);
      }
    }
    return result;
  };

  this.generate_model_mapping = function (mapping, upper_case_model) {
    let result = [];
    for (key in mapping) {
      let pair_str = [];
      for (pair_key in mapping[key]) {
        let pair_value = mapping[key][pair_key];
        pair_str.push(`\"${pair_key}\": \"${pair_value}\"`);
      }

      pair_str_final = '        ' + pair_str.join(',\n        ');

      result.push(`
       ${key}: {
 ${pair_str_final}
       }
 `);
    }

    result_str = result.join(',\n');
    final = `
 ${upper_case_model}.getMapping = () => {
    return {
 result_str
    };
 };
 `;
    return final;
  };

  this.generate_model_field = function (fields, timestamp) {
    result = [];
    for (key in fields) {
      field_name = fields[key][0];
      field_type = '';
      switch (fields[key][1]) {
        case 'number':
        case 'integer':
          field_type = 'INTEGER';
          break;
        case 'float':
        case 'double':
          field_type = 'FLOAT';
          break;
        case 'string':
        case 'password':
        case 'image':
          field_type = 'STRING';
          break;
        case 'text':
        case 'image':
        case 'file':
          field_type = 'TEXT';
          break;
        case 'date':
          field_type = 'DATEONLY';
          break;
        case 'datetime':
          field_type = 'DATE';
          break;

        default:
          if (fields[key][1].indexOf('image') > -1) {
            field_type = 'STRING';
          }

          break;
      }
      if (field_name == 'id') {
        continue;
      }

      result.push(`${field_name}: DataTypes.${field_type}`);
    }

    if (timestamp) {
      result.push('created_at: DataTypes.DATEONLY');
      result.push('updated_at: DataTypes.DATE');
    }

    return result;
  };

  this.generate_update_field_list = function (fields, edit_fields) {
    const update_fields_list = [];

    fields.forEach((field) => {
      const fieldName = field[0];

      if (edit_fields.includes(fieldName)) {
        update_fields_list.push(fieldName);
      }
    });

    return update_fields_list;
  };

  this.generate_edit_field_list = function (fields, edit_fields) {
    const edit_fields_list = [];

    fields.forEach((field) => {
      const fieldName = field[0];
      const fieldValidation = field[4];

      if (edit_fields.includes(fieldName) && fieldValidation.length) {
        edit_fields_list.push(`${fieldName}: args.${fieldName}`);
      }
    });

    return edit_fields_list;
  };

  this.generate_edit_field_validation_list = function (fields, edit_fields) {
    // TODO: map validation fields

    const fields_validation_list = [];

    fields.forEach((field) => {
      const fieldName = field[0];
      const fieldValidation = field[5];

      if (edit_fields.includes(fieldName) && fieldValidation.length) {
        fields_validation_list.push(`${fieldName}: "${fieldValidation}"`);
      }
    });

    return fields_validation_list;
  };

  this.generate_create_field_list = function (fields, add_fields) {
    const create_fields_list = [];

    fields.forEach((field) => {
      const fieldName = field[0];

      if (add_fields.includes(fieldName)) {
        create_fields_list.push(fieldName);
      }
    });

    return create_fields_list;
  };

  this.generate_add_field_list = function (fields, add_fields) {
    const add_fields_list = [];

    fields.forEach((field) => {
      const fieldName = field[0];
      const fieldValidation = field[4];

      if (add_fields.includes(fieldName) && fieldValidation.length) {
        add_fields_list.push(`${fieldName}: args.${fieldName}`);
      }
    });

    return add_fields_list;
  };

  this.generate_add_field_validation_list = function (fields, add_fields) {
    // TODO: map validation fields
    const fields_validation_list = [];

    fields.forEach((field) => {
      const fieldName = field[0];
      const fieldValidation = field[4];

      if (add_fields.includes(fieldName) && fieldValidation.length) {
        fields_validation_list.push(`${fieldName}: "${fieldValidation}"`);
      }
    });

    return fields_validation_list;
  };

  this.generate_schema = function (model, fields, timestamp, ignore_id_field_list) {
    const model_name = model.name;

    let model_camel = this.ucFirst(this.toCamelCaseString(model_name));

    let result = [];
    for (key in fields) {
      let field_name = fields[key][0];
      let field_type = '';

      switch (fields[key][1]) {
        case 'number':
        case 'integer':
          field_type = 'Int';
          break;
        case 'float':
        case 'double':
          field_type = 'Float';
          break;
        case 'boolean':
          field_type = 'Boolean';
        case 'string':
        case 'password':
        case 'image':
        case 'text':
        case 'file':
          field_type = 'String';
          break;
        case 'date':
        case 'datetime':
          field_type = 'Date';
          break;
        default:
          if (fields[key][1].indexOf('image') > -1) {
            field_type = 'String';
          }
          break;
      }
      if (field_name == 'id') {
        continue;
      }

      result.push(`${field_name}: ${field_type}`);

      field_name = fields[key][0];

      if (field_name.indexOf('_id') > -1) {
        if (field_name.indexOf('_file_id') > -1) {
          if (field_name.indexOf('_image_id') > -1) {
            if (field_name.indexOf('_holder_id') > -1) {
              if (!ignore_id_field_list.includes(field_name)) {
                let other_model = field_name.replace('_id', '');
                let other_model_camel = this.ucFirst(this.toCamelCaseString(other_model));
                result.push(`${other_model}: ${other_model_camel}`);
              }
            }
          }
        }
      }
    }

    if (timestamp) {
      result.push('created_at: Date');
      result.push('updated_at: Date');
    }

    fields_list = result.join('\n    ');

    // map relations
    let relations = [];

    fields.forEach((field) => {
      let fieldName = field[0];

      if (fieldName === '_id' || fieldName === 'role_id' || fieldName === 'image_id' || fieldName === 'referrer_user_id' || fieldName === 'stripe_id' || fieldName === 'file_id') {
      } else if (fieldName.includes('_id')) {
        fieldName = fieldName.replace('_id', '');
        fieldNameCamelCase = this.ucFirst(this.toCamelCaseString(fieldName));

        return relations.push(`${fieldName}: ${fieldNameCamelCase}`);
      }
    });

    const relation_list = relations.join('\n   ');

    code = `
     type ${model_camel} {
     id: ID!
     ${fields_list}
     \n
     ${relation_list}
     }
     `;

    return code;
  };

  this.generate_schema_pagination = function (model_name) {
    const model_name_camel_case = this.ucFirst(this.toCamelCaseString(model_name));

    const code = `
     # ${model_name.toUpperCase()}
     type ${model_name_camel_case}Edge {
       cursor: String
       node: ${model_name_camel_case}
     }
     
     type ${model_name_camel_case}s {
       edges: [${model_name_camel_case}Edge]
       pageInfo: PageInfo
     }`;

    return code;
  };

  this.generate_schema_mutation = function (model, fields) {
    const model_name = model.name;
    const model_name_camel_case = this.ucFirst(this.toCamelCaseString(model_name));

    const add = [];
    const edit = [];

    // Create
    model.graphql_add_fields.forEach((field) => {
      let model_field = model.field.find((item) => item[0] === field);
      if (!model_field) {
        const isJoin = model.join.find((item) => item.field === field);
        if (!isJoin) {
          throw new Error('Unknown graphql_add field ' + field + ' in model ' + model.name);
        }
        model_field = [field, 'integer', [], '', '', ''];
      }
      if (model_field) {
        let field_type = 'String';
        switch (model_field[1]) {
          case 'number':
          case 'integer':
            field_type = 'Int';
            break;
          case 'float':
          case 'double':
            field_type = 'Float';
            break;
          case 'boolean':
            field_type = 'Boolean';
            break;
          case 'string':
          case 'password':
          case 'text':
          case 'image':
          case 'file':
            field_type = 'String';
            break;
          case 'date':
          case 'datetime':
            field_type = 'Date';
            break;
          default:
            field_type = 'String';
            break;
        }
        if (model_field[4].includes('required')) {
          field_type = field_type + '!';
        }
        add.push(field + ':' + field_type);
      }
    });

    // Update
    model.graphql_edit_fields.forEach((field) => {
      let model_field = model.field.find((item) => item[0] === field);
      if (!model_field) {
        const isJoin = model.join.find((item) => item.field === field);
        if (!isJoin) {
          throw new Error('Unknown graphql_edit field ' + field + 'in model' + model.name);
        }
        model_field = [field, 'integer', [], '', '', ''];
      }
      if (model_field) {
        let field_type = 'String';
        switch (model_field[1]) {
          case 'number':
          case 'integer':
            field_type = 'Int';
            break;
          case 'float':
          case 'double':
            field_type = 'Float';
            break;
          case 'boolean':
            field_type = 'Boolean';
            break;
          case 'string':
          case 'password':
          case 'text':
          case 'image':
          case 'file':
            field_type = 'String';
            break;
          case 'date':
          case 'datetime':
            field_type = 'Date';
            break;
          default:
            field_type = 'String';
            break;
        }
        if (model_field[5].includes('required')) {
          field_type = field_type + '!';
        }
        edit.push(field + ':' + field_type);
      }
    });

    const final = [`# ${model_name.toUpperCase()}`];
    final.push(`create${model_name_camel_case}${add.length > 0 ? '(' + add.join(',') + ')' : ''}: ${model_name_camel_case}!`);
    final.push(`update${model_name_camel_case}(id: ID! ${edit.length > 0 ? ',' + edit.join(',') : ''}): [Int!]!`);
    final.push(`delete${model_name_camel_case}(id: ID!): Int!`);

    return final.join('\n');
  };

  this.generate_schema_query = function (model_name) {
    const model_name_camel_case = this.ucFirst(this.toCamelCaseString(model_name));

    let code = `
     # ${model_name.toUpperCase()}
     ${model_name_camel_case}(id: ID!): ${model_name_camel_case}
     ${model_name_camel_case}s(first: Int, after: ID): ${model_name_camel_case}s
     `;

    return code;
  };

  this.generate_import_files = function (model_name) {
    let model_camel = this.ucFirst(this.toCamelCaseString(model_name));

    let final = `
 const all${model_camel}Resolver = require("./all/all${model_camel}");
 const single${model_camel}Resolver = require("./single/single${model_camel}");
 const create${model_camel}Resolver = require("./create/create${model_camel}");
 const update${model_camel}Resolver = require("./update/update${model_camel}");
 const delete${model_camel}Resolver = require("./delete/delete${model_camel}");
 const type${model_camel}Resolver = require("./type/type${model_camel}");
 `;
    return final;
  };

  this.generate_resolver_query = function (model_name) {
    let model_camel = this.ucFirst(this.toCamelCaseString(model_name));

    return [` ${model_camel}s: all${model_camel}Resolver`, ` ${model_camel}: single${model_camel}Resolver`];
  };

  this.generate_resolver_mutation = function (model_name) {
    let model_camel = this.ucFirst(this.toCamelCaseString(model_name));

    return [` create${model_camel}: create${model_camel}Resolver`, ` update${model_camel}: update${model_camel}Resolver`, ` delete${model_camel}: delete${model_camel}Resolver`];
  };

  this.generate_resolver_types = function (model_name) {
    let model_camel = this.ucFirst(this.toCamelCaseString(model_name));

    return [` ${model_camel}: type${model_camel}Resolver`];
  };

  this.generate_resolver_relation = function (model_name, fields) {
    let model_camel = this.ucFirst(this.toCamelCaseString(model_name));
    let result = [];

    for (key in fields) {
      field_name = fields[key][0];
      if (field_name.indexOf('_id') > -1) {
        if (field_name.indexOf('_file_id') > -1 || field_name.indexOf('_image_id') > -1 || field_name.indexOf('_holder_id') > -1) {
          continue;
        }
        let other_model = field_name.replace('_id', '');
        let upper_other_model = this.ucFirst(this.toCamelCaseString(other_model));
        result.push(`${other_model}: relation${upper_other_model}Resolver`);
      }
    }

    if (result.length > 0) {
      other_str = result.join(',\n\t ');
      let final = `
     ${model_camel}: {
      ${other_str}
     }`;
      return [final];
    }
    return [];
  };

  this.get_relation_list = function (model_name, fields) {
    let result = [];

    for (key in fields) {
      field_name = fields[key][0];
      if (field_name.indexOf('_id') > -1) {
        if (field_name === '_id' || field_name.indexOf('_file_id') > -1 || field_name.indexOf('_image_id') > -1 || field_name.indexOf('_holder_id') > -1) {
          continue;
        }

        let other_model = field_name.replace('_id', '');
        result.push(other_model);
      }
    }

    return result;
  };

  this.build = function () {
    try {
      this.init();
      for (key in this._render_list) {
        this.writeFileSyncRecursive(key, this._render_list[key], {
          mode: 0775,
        });
      }
    } catch (err) {
      console.error('Graphql Builder Build Error', err);
    }
  };

  this.destroy = function () {
    // for (const key in this._copy) {
    //   if (this._copy.hasOwnProperty(key)) {
    //     let value = this._copy[key];
    //     try {
    //       if (fs.existsSync(key)) {
    //         fs.unlinkSync(value);
    //       }
    //     } catch (err) {
    //       console.error("Copy Builder Destroy Error", err);
    //     }
    //   }
    // }
  };

  this.generate_field_types = function (model_name, fields) {
    const fieldTypes = [];

    fields.forEach((field) => {
      let fieldName = field[0];

      if (fieldName === '_id' || fieldName === 'role_id' || fieldName === 'image_id' || fieldName === 'referrer_user_id' || fieldName === 'stripe_id' || fieldName === 'file_id') {
      } else if (fieldName.includes('_id')) {
        fieldName = fieldName.replace('_id', '');
        // fieldNameCamelCase = this.ucFirst(this.toCamelCaseString(fieldName));

        return fieldTypes.push(fieldName);
      }
    });

    let code = '';

    fieldTypes.forEach((field) => {
      code += `async ${field}({ ${field}_id }, _, { db }, info) {
         try {
           const attributes = db.${field}.intersection(graphqlFields(info));
     
           return await db.${field}.getByPK(${field}_id, { attributes });
         } catch (error) {
           console.log('${field} -> error', error);
           return new ApolloError('InternalServerError');
         }
       },`;
    });

    return code;
  };
};
