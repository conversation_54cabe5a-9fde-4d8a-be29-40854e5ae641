async function registerAPI() {
  var mainview = app.view.main;
  let registerFirstName = document.getElementById('register-firstName').value;
  let registerLastName = document.getElementById('register-lastName').value;
  let registerEmail = document.getElementById('register-email').value;
  let registerPhoneIn = '+1' + document.getElementById('register-phone').value;
  let registerPhone = registerPhoneIn.replace(/\s+/g, '');
  let registerPassword = document.getElementById('register-password').value;
  let cpassword = document.getElementById('register-cPassword').value;
  app.preloader.show();
  try {
    if (document.getElementById('register-phone').classList.contains('input-invalid')) {
      throw 'Please enter a valid number';
    }

    if (registerValidatefields(registerFirstName, registerLastName, registerEmail, registerPassword, cpassword, registerPhone)) {
      let body = {
        first_name: registerFirstName,
        last_name: registerLastName,
        email: registerEmail,
        password: registerPassword,
        phone_number: register<PERSON><PERSON>,
      };
      let response = await fetch('/api/user/register', {
        method: 'POST',
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
      });

      let resJSON = await response.json();

      if (response.status == 200) {
        app.preloader.hide();
        localStorage.setItem('user', JSON.stringify(resJSON.user));
        this.store.dispatch('setParentID', resJSON.user.id);
        mainview.router.navigate({ name: 'subscriptionPage' });
      }
      if (response.status == 400) {
        app.preloader.hide();
        throw resJSON.message;
      }
    }
  } catch (error) {
    app.preloader.hide();
    app.dialog.alert(error);
  }
}

function registerValidatefields(fName, lName, email, password, cpassword, registerPhone) {
  var AllFieldsValidated = true;
  var errortoThrow = 'These field(s) are required: <br>';
  if (fName == '') {
    errortoThrow += 'First Name<br>';
    AllFieldsValidated = false;
  }
  if (lName == '') {
    errortoThrow += 'Last Name<br>';
    AllFieldsValidated = false;
  }
  if (email == '') {
    errortoThrow += 'Email Address<br>';
    AllFieldsValidated = false;
  }
  if (registerPhone == '') {
    errortoThrow += 'Email Address<br>';
    AllFieldsValidated = false;
  }
  if (password == '') {
    errortoThrow += 'Password<br>';
    AllFieldsValidated = false;
  }
  if (cpassword == '') {
    errortoThrow += 'Confirm Password<br>';
    AllFieldsValidated = false;
  }
  if (AllFieldsValidated == false) {
    throw errortoThrow;
  }
  if (cpassword !== password) {
    errortoThrow = "Passwords don't match";
    throw errortoThrow;
  }
  return AllFieldsValidated;
}
