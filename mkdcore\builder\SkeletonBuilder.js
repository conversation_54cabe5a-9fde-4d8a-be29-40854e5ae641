/*Powered By: Manaknightdigital Inc. https://manaknightdigital.com/ Year: 2020*/
/**
 * Skeleton builder
 * @copyright 2020 Manaknightdigital Inc.
 * @link https://manaknightdigital.com
 * @license Proprietary Software licensing
 * <AUTHOR>
 *
 */

const fs = require('fs');
const path = require('path');
const Builder = require('./Builder');

module.exports = function (config) {
  this._config = config;
  this._roles = {};
  this._translations = [];
  this._render_list = [];
  Builder.call(this);

  this.set_roles = function (roles) {
    this._roles = roles;
  };

  this.build = function () {
    let appJs = fs.readFileSync(
      '../mkdcore/source/skeleton/controller.js',
      'utf8',
    );
    const roles = this._roles.map((role) => role.name.toLowerCase());
    roles.push('guest');
    let routes = '';
    let initializeApi = '';
    for (let i = 0; i < roles.length; i++) {
      const role = roles[i];
      routes += `//const ${role}Routes = require("./${role}/index");\n`;
      initializeApi += `//\tapp = ${role}Routes.initializeApi(app);\n`;
    }
    appJs = this.inject_substitute(appJs, 'routes', routes);
    appJs = this.inject_substitute(appJs, 'initializeApi', initializeApi);

    this.createDirectoriesRecursive('../../release/controllers/index.js');
    fs.writeFileSync(
      path.join(__dirname, '../../release/controllers/index.js'),
      appJs,
      {
        mode: 0775,
      },
    );
  };

  this.destroy = function () {};
};
