'use strict';

const app = require('express').Router();
const Sequelize = require('sequelize');
const logger = require('../../services/LoggingService');
let pagination = require('../../services/PaginationService');
let SessionService = require('../../services/SessionService');
let JwtService = require('../../services/JwtService');
const ValidationService = require('../../services/ValidationService');
const PermissionService = require('../../services/PermissionService');
const UploadService = require('../../services/UploadService');
const AuthService = require('../../services/AuthService');
const db = require('../../models');
const helpers = require('../../core/helpers');
const upload = UploadService.upload('files/file');

const role = 1;

app.get('/admin/files/:num', SessionService.verifySessionMiddleware(role, 'admin'), async function (req, res, next) {
  try {
    let session = req.session;
    let paginateListViewModel = require('../../view_models/files_admin_list_paginate_view_model');

    var viewModel = new paginateListViewModel(db.file, 'Files', session.success, session.error, '/admin/files');

    const format = req.query.format ? req.query.format : 'view';
    const direction = req.query.direction ? req.query.direction : 'ASC';
    const per_page = req.query.per_page ? req.query.per_page : 10;
    let order_by = req.query.order_by ? req.query.order_by : viewModel.get_field_column()[0];
    let orderAssociations = [];
    viewModel.set_order_by(order_by);
    let joins = order_by.includes('.') ? order_by.split('.') : [];
    order_by = order_by.includes('.') ? joins[joins.length - 1] : order_by;
    if (joins.length > 0) {
      for (let i = joins.length - 1; i > 0; i--) {
        orderAssociations.push(`${joins[i - 1]}`);
      }
    }
    // Check for flash messages
    const flashMessageSuccess = req.flash('success');
    if (flashMessageSuccess && flashMessageSuccess.length > 0) {
      viewModel.success = flashMessageSuccess[0];
    }
    const flashMessageError = req.flash('error');
    if (flashMessageError && flashMessageError.length > 0) {
      viewModel.error = flashMessageError[0];
    }

    viewModel.set_id(req.query.id ? req.query.id : '');
    viewModel.set_upload_by(req.query.upload_by ? req.query.upload_by : '');
    viewModel.set_cohort_id(req.query.cohort_id ? req.query.cohort_id : '');
    viewModel.set_file_url(req.query.file_url ? req.query.file_url : '');

    let where = helpers.filterEmptyFields({
      id: viewModel.get_id(),
      upload_by: viewModel.get_upload_by(),
      cohort_id: viewModel.get_cohort_id(),
      file_url: viewModel.get_file_url(),
    });

    const count = await db.file._count(where, []);

    viewModel.set_total_rows(count);
    viewModel.set_per_page(+per_page);
    viewModel.set_page(+req.params.num);
    viewModel.set_query(req.query);
    viewModel.set_sort_base_url(`/admin/files/${+req.params.num}`);
    viewModel.set_sort(direction);

    const list = await db.file.getPaginated(viewModel.get_page() - 1 < 0 ? 0 : viewModel.get_page(), viewModel.get_per_page(), where, order_by, direction, orderAssociations);

    viewModel.set_list(list);

    if (format == 'csv') {
      const csv = viewModel.to_csv();
      return res
        .set({
          'Content-Type': 'text/csv',
          'Content-Disposition': 'attachment; filename="export.csv"',
        })
        .send(csv);
    }

    // if (format != 'view') {
    //   res.json(viewModel.to_json());
    // } else {
    // }

    return res.render('admin/Files', viewModel);
  } catch (error) {
    console.error(error);
    viewModel.error = error.message || 'Something went wrong';
    return res.render('admin/Files', viewModel);
  }
});

app.get('/admin/files-add', SessionService.verifySessionMiddleware(role, 'admin'), async function (req, res, next) {
  if (req.session.csrf === undefined) {
    req.session.csrf = SessionService.randomString(100);
  }

  const filesAdminAddViewModel = require('../../view_models/files_admin_add_view_model');

  const viewModel = new filesAdminAddViewModel(db.file, 'Add file', '', '', '/admin/files');

  res.render('admin/Add_Files', viewModel);
});

app.post(
  '/admin/files-add',
  SessionService.verifySessionMiddleware(role, 'admin'),
  upload.single('file'),
  ValidationService.validateInput(
    { upload_by: 'required', cohort_id: 'required', file_url: 'required' },
    { 'upload_by.required': 'UploadBy is required', 'cohort_id.required': 'CohortId is required', 'file_url.required': 'FileUrl is required' },
  ),
  async function (req, res, next) {
    if (req.session.csrf === undefined) {
      req.session.csrf = SessionService.randomString(100);
    }
    const filesAdminAddViewModel = require('../../view_models/files_admin_add_view_model');

    const viewModel = new filesAdminAddViewModel(db.file, 'Add file', '', '', '/admin/files');

    // TODO use separate controller for image upload
    //  {{{upload_field_setter}}}

    let { upload_by, cohort_id, file_url } = req.body;

    if (req.file) {
      file_url = req.file.filename;
    }

    viewModel.form_fields = {
      ...viewModel.form_fields,
      upload_by,
      cohort_id,
      file_url,
    };

    try {
      if (req.validationError) {
        viewModel.error = req.validationError;
        return res.render('admin/Add_Files', viewModel);
      }

      viewModel.session = req.session;

      const data = await db.file.insert({ upload_by, cohort_id, file_url });

      if (!data) {
        viewModel.error = 'Something went wrong';
        return res.render('admin/Add_Files', viewModel);
      }

      await db.activity_log.insert({
        action: 'ADD',
        name: 'Admin_files_controller.js',
        portal: 'admin',
        data: JSON.stringify({ upload_by, cohort_id, file_url }),
      });

      req.flash('success', 'File created successfully');
      return res.redirect('/admin/files/0');
    } catch (error) {
      console.error(error);
      viewModel.error = error.message || 'Something went wrong';
      return res.render('admin/Add_Files', viewModel);
    }
  },
);

app.get('/admin/files-edit/:id', SessionService.verifySessionMiddleware(role, 'admin'), async function (req, res, next) {
  let id = req.params.id;
  if (req.session.csrf === undefined) {
    req.session.csrf = SessionService.randomString(100);
  }
  const filesAdminEditViewModel = require('../../view_models/files_admin_edit_view_model');

  const viewModel = new filesAdminEditViewModel(db.file, 'Edit file', '', '', '/admin/files');

  try {
    const exists = await db.file.getByPK(id);

    if (!exists) {
      req.flash('error', 'File not found');
      return res.redirect('/admin/files/0');
    }
    const values = exists;
    Object.keys(viewModel.form_fields).forEach((field) => {
      viewModel.form_fields[field] = values[field] || '';
    });

    return res.render('admin/Edit_Files', viewModel);
  } catch (error) {
    console.error(error);
    viewModel.error = error.message || 'Something went wrong';
    return res.render('admin/Edit_Files', viewModel);
  }
});

app.post(
  '/admin/files-edit/:id',
  SessionService.verifySessionMiddleware(role, 'admin'),
  ValidationService.validateInput(
    { upload_by: 'required', cohort_id: 'required', file_url: 'required' },
    { 'upload_by.required': 'UploadBy is required', 'cohort_id.required': 'CohortId is required', 'file_url.required': 'FileUrl is required' },
  ),
  async function (req, res, next) {
    let id = req.params.id;
    if (req.session.csrf === undefined) {
      req.session.csrf = SessionService.randomString(100);
    }

    const filesAdminEditViewModel = require('../../view_models/files_admin_edit_view_model');

    const viewModel = new filesAdminEditViewModel(db.file, 'Edit file', '', '', '/admin/files');

    const { upload_by, cohort_id, file_url } = req.body;

    viewModel.form_fields = {
      ...viewModel.form_fields,
      upload_by,
      cohort_id,
      file_url,
    };

    delete viewModel.form_fields.id;

    try {
      if (req.validationError) {
        viewModel.error = req.validationError;
        return res.render('admin/Edit_Files', viewModel);
      }

      const resourceExists = await db.file.getByPK(id);
      if (!resourceExists) {
        req.flash('error', 'File not found');
        return res.redirect('/admin/files/0');
      }

      viewModel.session = req.session;

      let data = await db.file.edit({ upload_by, cohort_id, file_url }, id);
      if (!data) {
        viewModel.error = 'Something went wrong';
        return res.render('admin/Edit_Files', viewModel);
      }

      await db.activity_log.insert({
        action: 'EDIT',
        name: 'Admin_files_controller.js',
        portal: 'admin',
        data: JSON.stringify({ upload_by, cohort_id, file_url }),
      });

      req.flash('success', 'File edited successfully');

      return res.redirect('/admin/files/0');
    } catch (error) {
      console.error(error);
      viewModel.error = error.message || 'Something went wrong';
      return res.render('admin/Edit_Files', viewModel);
    }
  },
);

app.get(
  '/admin/files-view/:id',
  SessionService.verifySessionMiddleware(role, 'admin'),

  async function (req, res, next) {
    try {
      let id = req.params.id;

      const filesAdminDetailViewModel = require('../../view_models/files_admin_detail_view_model');

      var viewModel = new filesAdminDetailViewModel(db.file, 'File details', '', '', '/admin/files');

      const data = await db.file.getByPK(id);

      if (!data) {
        viewModel.error = 'File not found';
        viewModel.detail_fields = { ...viewModel.detail_fields, id: 'N/A', upload_by: 'N/A', cohort_id: 'N/A', file_url: 'N/A' };
      } else {
        viewModel.detail_fields = {
          ...viewModel.detail_fields,
          id: data['id'] || 'N/A',
          upload_by: data['upload_by'] || 'N/A',
          cohort_id: data['cohort_id'] || 'N/A',
          file_url: data['file_url'] || 'N/A',
        };
      }

      res.render('admin/View_Files', viewModel);
    } catch (error) {
      console.error(error);
      viewModel.error = error.message || 'Something went wrong';
      viewModel.detail_fields = { ...viewModel.detail_fields, id: 'N/A', upload_by: 'N/A', cohort_id: 'N/A', file_url: 'N/A' };
      res.render('admin/View_Files', viewModel);
    }
  },
);

app.get('/admin/files-delete/:id', SessionService.verifySessionMiddleware(role, 'admin'), async function (req, res, next) {
  let id = req.params.id;

  const filesAdminDeleteViewModel = require('../../view_models/files_admin_delete_view_model');

  const viewModel = new filesAdminDeleteViewModel(db.file);

  try {
    const exists = await db.file.getByPK(id);

    if (!exists) {
      req.flash('error', 'File not found');
      return res.redirect('/admin/files/0');
    }

    viewModel.session = req.session;

    await db.file.realDelete(id);

    await db.activity_log.insert({
      action: 'DELETE',
      name: 'Admin_files_controller.js',
      portal: 'admin',
      data: JSON.stringify(exists),
    });

    req.flash('success', 'File deleted successfully');

    return res.redirect('/admin/files/0');
  } catch (error) {
    console.error(error);
    req.flash('error', error.message || 'Something went wrong');
    return res.redirect('/admin/files/0');
  }
});

// APIS

app.get('/admin/api/files', JwtService.verifyTokenMiddleware(role), async function (req, res, next) {
  try {
    const user_id = req.user_id;
    const session = req.session;
    let listViewModel = require('../../view_models/files_admin_list_paginate_view_model');
    let viewModel = new listViewModel(db.file, 'Files', session.success, session.error, '/admin/files');
    const direction = req.query.direction ? req.query.direction : 'ASC';
    const page = req.query.page ? parseInt(req.query.page) : 1;
    const limit = req.query.limit ? parseInt(req.query.limit) : 10;
    const offset = (page - 1) * limit;
    let order_by = req.query.order_by ? req.query.order_by : viewModel.get_field_column()[0];
    let orderAssociations = [];
    viewModel.set_order_by(order_by);
    let joins = order_by.includes('.') ? order_by.split('.') : [];
    order_by = order_by.includes('.') ? joins[joins.length - 1] : order_by;
    if (joins.length > 0) {
      for (let i = joins.length - 1; i > 0; i--) {
        orderAssociations.push(`${joins[i - 1]}`);
      }
    }

    viewModel.set_id(req.query.id ? req.query.id : '');
    viewModel.set_upload_by(req.query.upload_by ? req.query.upload_by : '');
    viewModel.set_cohort_id(req.query.cohort_id ? req.query.cohort_id : '');
    viewModel.set_file_url(req.query.file_url ? req.query.file_url : '');

    let where = helpers.filterEmptyFields({
      id: viewModel.get_id(),
      upload_by: viewModel.get_upload_by(),
      cohort_id: viewModel.get_cohort_id(),
      file_url: viewModel.get_file_url(),
    });

    let include = [];

    const { rows: allItems, count } = await db.file.findAndCountAll({
      where: where,
      limit: limit == 0 ? null : limit,
      offset: offset,
      include: include,
      distinct: true,
    });

    const response = {
      items: allItems,
      page,
      nextPage: count > offset + limit ? page + 1 : false,
      retrievedCount: allItems.length,
      fullCount: count,
    };

    return res.status(201).json({ success: true, data: response });
  } catch (error) {
    console.error(error);
    return res.status(500).json({ success: false, message: error.message || 'Something went wrong' });
  }
});

app.post(
  '/admin/api/files-add',
  JwtService.verifyTokenMiddleware(role),
  ValidationService.validateInput(
    { upload_by: 'required', cohort_id: 'required', file_url: 'required' },
    { 'upload_by.required': 'UploadBy is required', 'cohort_id.required': 'CohortId is required', 'file_url.required': 'FileUrl is required' },
  ),
  async function (req, res, next) {
    const filesAdminAddViewModel = require('../../view_models/files_admin_add_view_model');

    const viewModel = new filesAdminAddViewModel(db.file);

    const { upload_by, cohort_id, file_url } = req.body;
    try {
      if (req.validationError) {
        return res.status(500).json({ success: false, message: req.validationError });
      }

      const data = await db.file.insert({ upload_by, cohort_id, file_url });

      if (!data) {
        return res.status(500).json({ success: false, message: 'Something went wrong' });
      }

      await db.activity_log.insert({
        action: 'ADD',
        name: 'Admin_files_controller.js',
        portal: 'admin',
        data: JSON.stringify({ upload_by, cohort_id, file_url }),
      });

      return res.status(201).json({ success: true, message: 'File created successfully' });
    } catch (error) {
      return res.status(500).json({ success: false, message: 'Something went wrong' });
    }
  },
);

app.put(
  '/admin/api/files-edit/:id',
  JwtService.verifyTokenMiddleware(role),
  ValidationService.validateInput(
    { upload_by: 'required', cohort_id: 'required', file_url: 'required' },
    { 'upload_by.required': 'UploadBy is required', 'cohort_id.required': 'CohortId is required', 'file_url.required': 'FileUrl is required' },
  ),
  async function (req, res, next) {
    let id = req.params.id;

    const filesAdminEditViewModel = require('../../view_models/files_admin_edit_view_model');

    const viewModel = new filesAdminEditViewModel(db.file);

    const { upload_by, cohort_id, file_url } = req.body;

    try {
      if (req.validationError) {
        return res.status(500).json({ success: false, message: req.validationError });
      }

      const resourceExists = await db.file.getByPK(id);
      if (!resourceExists) {
        return res.status(404).json({ success: false, message: 'File not found' });
      }

      const data = await db.file.edit({ upload_by, cohort_id, file_url }, id);

      if (!data) {
        return res.status(500).json({ success: false, message: 'Something went wrong' });
      }

      await db.activity_log.insert({
        action: 'EDIT',
        name: 'Admin_files_controller.js',
        portal: 'admin',
        data: JSON.stringify({ upload_by, cohort_id, file_url }),
      });

      return res.json({ success: true, message: 'File edited successfully' });
    } catch (error) {
      return res.status(500).json({ success: false, message: 'Something went wrong' });
    }
  },
);

app.get('/admin/api/files-view/:id', JwtService.verifyTokenMiddleware(role), async function (req, res, next) {
  let id = req.params.id;

  const filesAdminDetailViewModel = require('../../view_models/files_admin_detail_view_model');

  const viewModel = new filesAdminDetailViewModel(db.file);

  try {
    const data = await db.file.getByPK(id);

    if (!data) {
      return res.status(404).json({ message: 'File not found', data: null });
    } else {
      const fields = { ...viewModel.detail_fields, id: data['id'] || '', upload_by: data['upload_by'] || '', cohort_id: data['cohort_id'] || '', file_url: data['file_url'] || '' };
      return res.status(200).json({ data: fields });
    }
  } catch (error) {
    return res.status(404).json({ message: 'Something went wrong', data: null });
  }
});

app.delete('/admin/api/files-delete/:id', JwtService.verifyTokenMiddleware(role), async function (req, res, next) {
  let id = req.params.id;

  const filesAdminDeleteViewModel = require('../../view_models/files_admin_delete_view_model');

  const viewModel = new filesAdminDeleteViewModel(db.file);

  try {
    const exists = await db.file.getByPK(id);

    if (!exists) {
      return res.status(404).json({ success: false, message: 'File not found' });
    }

    await db.file.realDelete(id);

    await db.activity_log.insert({
      action: 'DELETE',
      name: 'Admin_files_controller.js',
      portal: 'admin',
      data: JSON.stringify(exists),
    });

    return res.status(200).json({ success: true, message: 'File deleted successfully' });
  } catch (error) {
    return res.status(500).json({ success: false, message: 'Something went wrong' });
  }
});

module.exports = app;
