<!DOCTYPE html>
<html>

<head>
  <!-- Required meta tags-->
  <meta charset="utf-8" />
  <meta name="viewport"
    content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no, viewport-fit=cover" />
  <meta name="apple-mobile-web-app-capable" content="yes" />
  <!-- Color theme for statusbar (Android only) -->
  <meta name="theme-color" content="#2196f3" />
  <!-- Your app title -->
  <title>Extra Minutes</title>
</head>

<body>
  <!-- App root element -->
  <div id="app">
    <div class="page" data-name="extra-minutes-plan">
      <!-- Top Navbar -->
      <div class="navbar">
        <div class="navbar-bg"></div>
        <div class="navbar-inner">
          <div class="left">
            <a href="#" class="panel-toggle color-black">&#9776;</a>
          </div>
          <div class="title text-align-center">Extra Minutes</div>
          <div class="right"></div>
        </div>
      </div>

      <!-- Scrollable page content -->
      <div class="page-content display-flex flex-direction-column justify-content-center">
        <div class="block expenses-tabs" id="extra-minutes-buttons">
          <button>Hello</button>
        </div>
      </div>
    </div>
  </div>
</body>

</html>