const { Validator, addCustomMessages } = require('node-input-validator');

const throwError = (next, error = {}, status = 500) => {
  const _error = new Error();
  _error.error = error;
  _error.status = status;
  return next(_error);
};

module.exports = {
  validate: (validationObject = {}, _extendMessages = {}) => async (
    req,
    res,
    next,
  ) => {
    const validation = new Validator(req.body, validationObject);
    addCustomMessages(_extendMessages);

    try {
      const isValid = await validation.check();
      if (!isValid) {
        return throwError(next, validation.errors, 400);
      }
      return next();
    } catch (error) {
      console.log(error);
      return throwError(next, null, 500);
    }
  },
};
