<% if(it.layout_clean_mode) {%>
<% layout("../layouts/admin/Clean") %>
<% } else {%>
<% layout("../layouts/admin/Main") %>
<%}%>


<div class="tab-content mx-4 my-4" id="nav-tabContent">

<%~ includeFile("../partials/admin/GlobalResponse.eta", it) %>

<section>
<div class="row">
    <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
        <div class="card" id="stripe_invoice_filter_listing">
            <div class="card-body">
              <h5 class="primaryHeading2 text-md-left">
                    <%= it.get_heading() %> Search
              </h5>
                <form action="/admin/stripe-invoices/0" method="get" accept-charset="utf-8">
                    <div class="row">
                    
<div class="col-xl-4 col-lg-4 col-md-4 col-sm-12 col-12">

<div class="form-group">

	<label for="User First Name">User First Name</label>

	<input type="text" class="form-control" id="user_first_name" name="user_first_name" value="<%= it.get_user_first_name() %>"/>

</div>

</div>

<div class="col-xl-4 col-lg-4 col-md-4 col-sm-12 col-12">

<div class="form-group">

	<label for="Status">Status</label>

	<select name="status" class="form-control">

		<option value="">All</option>

		<% Object.keys(it.status_mapping()).forEach(function(value, key){ %>

			<option value="<%= key %>" <%= (it.get_status() == key && it.get_status() != '') ? 'selected' : '' %> > <%= it.status_mapping()[value]%> </option>
		<%}); %>

	</select>

</div>

</div>

<div class="col-xl-4 col-lg-4 col-md-4 col-sm-12 col-12">

<div class="form-group">

	<label for="Refunded">Refunded</label>

	<select name="refunded" class="form-control">

		<option value="">All</option>

		<% Object.keys(it.refunded_mapping()).forEach(function(value, key){ %>

			<option value="<%= key %>" <%= (it.get_refunded() == key && it.get_refunded() != '') ? 'selected' : '' %> > <%= it.refunded_mapping()[value]%> </option>
		<%}); %>

	</select>

</div>

</div>

                    <div style="width:100%;height:10px;display:block;float:none;"></div>
                        <div class="col-xl-4 col-lg-4 col-md-4 col-sm-12 col-12">
                           <div class="form-group">
                                <input type="submit" name="submit" class="btn btn-primary" value="Search">
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</section>



<div class="d-flex align-items-center justify-content-between">
  <h5 class="primaryHeading2 d-flex justify-content-between mt-2 my-4">
    <%= it.get_heading() %>
  </h5>

  <div class="d-flex align-items-center">
     
    
    <span class="add-part d-flex justify-content-md-end   ml-1"><a class="btn btn-info btn-sm ml-1" onclick='mkd_export_table(window.location.href);return false;'><i class="fas fa-file-download" style="color:white;"></i></a></span>
  </div>
</div>


<section class="table-placeholder bg-white mb-5 p-3 pl-4 pr-4 pt-4" style='height:auto;'>
    <div class="mb-2 d-flex align-items-center justify-content-between d-none">
      <div>
        <small class="d-flex align-items-baseline">
          Show <select name="page_length" class="form-control form-control-sm mx-2" style="max-width: 60px;" onchange="window.location='0?per_page='+this.value">
            <option <%= it.get_per_page() == 10 ? 'selected' : '' %> >10</option>
            <option <%= it.get_per_page() == 25 ? 'selected' : '' %> >25</option>
            <option <%= it.get_per_page() == 50 ? 'selected' : '' %> >50</option>
            <option <%= it.get_per_page() == 100 ? 'selected' : '' %> >100</option>
          </select>
           entries
        </small>
      </div>      

      <div class="d-flex align-items-center">
        
      </div>
    </div>
    <div class="table-responsive">
    <table class="table table-mh br w-100 table-bordered table-striped">
        <thead class='thead-white text-nowrap'>
          
          <% it.get_column().forEach(function(data, index) { %>
            <% if (it.get_order_by().length < 1 || it.get_field_column()[index] == '' || it.get_field_column()[index] == undefined) { %>
              <th scope="col" class="paragraphText text-left"><%= data %></th>
            <% } else { %>
              <% if (it.get_order_by() == it.get_field_column()[index]) { %>
                <% if (it.get_sort() == 'ASC') { %>
                  <th scope='col' class='paragraphText text-left'>
                    <a class="text-dark" href="<%= it.get_sort_base_url() + '?order_by=' + it.get_field_column()[index] + '&direction=DESC' %>"><%= data %>
                      <% /* <i class='fas fa-sort-up' style='vertical-align: -0.35em;'></i> */ %>
                      <i style="margin-top: 0.45rem;float:right;color: #aaa;font-size: small;" class="fas fa-long-arrow-alt-down"></i>                    
                      <i style="margin-top: 0.45rem;float:right;font-size: small;" class="fas fa-long-arrow-alt-up"></i>
                    </a>
                  </th>
                <% } else { %>
                  <th scope='col' class='paragraphText text-left'>
                    <a class="text-dark" href="<%= it.get_sort_base_url() + '?order_by=' + it.get_field_column()[index] + '&direction=ASC' %>"><%= data %>                      
                    <i style="margin-top: 0.45rem;float:right;font-size: small;" class="fas fa-long-arrow-alt-down"></i>
                    <i style="margin-top: 0.45rem;float:right;color: #aaa;font-size: small;" class="fas fa-long-arrow-alt-up"></i>
                    </a>
                  </th>
                <% } %>
            <% } else { %>
                <th scope='col' class='paragraphText text-left'>
                  <a class="text-dark" href="<%= it.get_sort_base_url() + '?order_by=' + it.get_field_column()[index] +  '&direction=ASC' %>"><%= data %>
                    <i style="margin-top: 0.45rem;float:right;color: #aaa;font-size: small;" class="fas fa-long-arrow-alt-down"></i>
                    <i style="margin-top: 0.45rem;float:right;color: #aaa;font-size: small;" class="fas fa-long-arrow-alt-up "></i>
                  </a>
                </th>
              <% } %>
            <% } %>
          <% }) %> 
          </thead>
        <tbody class="tbody-light">
          <% it.get_list().forEach(function(data) { %>
              <tr>
                
                <td><span class="font-weight-bold">First name</span>: <%= data?.user?.first_name %> </br><span class="font-weight-bold">Last name</span>: <%= data?.user?.last_name %> </br><span class="font-weight-bold">Phone</span>: <%= data?.user?.phone %> </br></td>


           <td> 
           <%= data.currency.toUpperCase() %>
           </td>
         


           <td> 
           $ <%= data.amount_due !== null ?  it.number_format(data.amount_due/100, 2) : 'N/A' %>
           </td>
         


           <td> 
           $ <%= data.amount_due !== null ? it.number_format(data.amount_paid*1, 2) : 'N/A' %>
           </td>
         


           <td> 
           $ <%=  data.amount_due !== null ? it.number_format(data.amount_total*1, 2) : 'N/A' %>
           </td>
         


           <td> 
           <%= it.ucFirst(it.refunded_mapping()[data.refunded]) %>
           </td>
         


           <td> 
           <%= it.ucFirst(it.status_mapping()[data.status]) %>
           </td>
         

<td>
<a class="btn btn-link  link-underline text-underline  btn-sm" target="_blank" href="/admin/stripe-invoices-view/<%= data.id %>">View</a>
<a class="btn btn-link  link-underline text-underline text-danger btn-sm" href="/admin/stripe-invoices-delete/<%= data.id %>">Delete</a>
  <% if(data.refunded == 1){%>
  <a class="btn btn-link  link-underline text-underline text-warning btn-sm" href="#">Refunded</a>
  <% }else{%>
  <a class="btn btn-link  link-underline text-underline text-danger btn-sm" href="/admin/invoices-refund/<%= data.id %>">Refund</a>
  <% }%>
</td>
              </tr>
          <% }) %>
        </tbody>
    </table>
    <p class="pagination_custom"><%~ it.get_links() %></p>
    </div>
</section>
</div>
