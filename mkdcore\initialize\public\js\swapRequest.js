var $$ = Dom7;

let parent2 = {};

async function fillSwapDropdown(id, container) {
  const user = localStorage.getItem('user') ? JSON.parse(localStorage.getItem('user')) : {};

  try {
    app.preloader.show();
    let response = await fetch('/api/cohort/', {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
    });
    let resJSON = await response.json();
    if (resJSON.data.length == 0) {
      throw "You don't have any active Co-Hort";
    }
    var dataCount = 0;
    resJSON.data.forEach((cohort) => {
      dataCount += 1;
      if (dataCount == 1) {
        html = `<option selected value='${cohort.id}'>${cohort.name}</option>`;
        $$(`${container} .item-inner .item-after`).html('');
        $$(`${container} .item-inner .item-after`).html(cohort.name);
      } else {
        html = `<option value='${cohort.id}'>${cohort.name}</option>`;
      }
      app.preloader.hide();
      $$(id).append(html);
    });
    if (container == '.swapChoseCohortSelect') {
      getSwaps();
    }
    if (container == '.filesChoseCohortSelect') {
      getCohortFiles();
    }
  } catch (error) {
    app.preloader.hide();
    // app.dialog.alert(error);
    console.log(error);
  }
}

function validateSwapRequestField(cohort, date, time_type, quantity, notes) {
  var AllFieldsValidated = true;
  var errortoThrow = 'These field(s) are required: <br>';
  if (cohort == '') {
    errortoThrow += 'Co-Hort Name <br>';
    AllFieldsValidated = false;
  }
  if (date == '') {
    errortoThrow += 'Co-Swap Date <br>';
    AllFieldsValidated = false;
  }
  if (time_type == '') {
    errortoThrow += 'Co-Swap Time <br>';
    AllFieldsValidated = false;
  }

  if (AllFieldsValidated == false) {
    throw errortoThrow;
  }
  return AllFieldsValidated;
}

async function requestSwap(e) {
  const user = localStorage.getItem('user') ? JSON.parse(localStorage.getItem('user')) : {};

  try {
    app.preloader.show();

    var mainview = app.view.main;

    const cohort = $$('#swap-cohort-drop').val();
    const children = $$('#requestswap-children').val();
    const date = $$('#requestswap-date').val();
    const time_type = $$('#request-timetype').val();
    const quantity = $$('#requestswap-quantity').val();
    const notes = $$('#request-notes').val();
    if (validateSwapRequestField(cohort, date, time_type, quantity, notes)) {
      let res = await fetch(`/api/user/request-swap`, {
        method: 'POST',
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ cohort, children, date, time_type, quantity, notes }),
      });
      app.preloader.hide();
      if (res.status === 400) {
        return app.dialog.alert('Cannot request a Co-Swap on the selected date');
      } else if (res.status === 404) {
        return app.dialog.alert('Please fill out all the fields');
      } else if (res.ok) {
        app.dialog.alert('Co-Swap request created');
      }

      mainview.router.navigate({ name: 'swap' });
    } else {
      throw 'Something went wrong, Please try again';
    }
  } catch (error) {
    app.preloader.hide();
    app.dialog.alert(error);
  }
}
const time_types = ['Weekday Day', 'Weekday Night', 'Weekend Day', 'Weekend Night', 'Holiday Day', 'Holiday Night', 'Hours'];

const renderSwap = (id, swaps, type) => {
  const cohort_id = $$('#swap-cohort-drop-filter').val();

  const user = localStorage.getItem('user') ? JSON.parse(localStorage.getItem('user')) : {};

  let html = '';
  if (swaps.length && type === 'pending') {
    html = `<div class="center-box">
    <button class="button button-fill disabled  swap-apporoved-check-button" disabled id="approve-btn" onclick="approveCheck()">Approve Checked</button>
    </div>`;
  }
  swaps.forEach((swap) => {
    if (type === 'pending') {
      html += ` 
      <div class="card singleSwap">
      <div class="card-header singleSwapCardHeader"><b>Co-Swap Date: </b>${new Date(swap.date).toLocaleString('en-us', {
        timeZone: 'UTC',
        month: 'short',
        day: 'numeric',
        year: 'numeric',
      })}</div>
      <div class="card-content card-content-padding swapInfoContainer">
        <div class="row no-margin">
          <div class="col-60 viewFiles-infoCol">
            <div class="block-title text-align-left info infoSwap infoSwapTimeType">
            Time Type : ${time_types[swap.time_type]}
            </div>
            ${
              swap.time_type === 7
                ? ` <div class="block-title text-align-left info infoSwap">
             Quantity : ${swap.quantity} 
            </div>`
                : ''
            }
            <div class="block-title text-align-left info">
            Requested by : ${swap.request_from === user.id ? user.first_name : parent2.first_name}
            </div>
            <div class="block-title text-align-left info">
            Requested At : ${new Date(swap.created_at).toLocaleString('en-us', {
              timeZone: 'UTC',
              month: 'short',
              day: 'numeric',
              year: 'numeric',
            })}
            </div>
            <div class="block-title text-align-left info">
            Gaining Co-Parent : ${swap.request_from === user.id ? user.first_name : parent2.first_name}
            </div>
            <div class="block-title text-align-left info">
            Losing Co-Parent : ${swap.request_from !== user.id ? user.first_name : parent2.first_name}
            </div>
            <div class="block-title text-align-left info">
            Notes : ${swap.notes}
            </div>
          </div>
          ${
            swap.request_from != user.id
              ? `
          <div class="">
            <label class="checkbox">
            <input type="checkbox" onclick="swap_checked();" class="approveSwaps" data-swap="${swap.id}" name="switch" value="yes" />
            <i class="toggle-checkbox"></i>
            </label>
          </div>
          <div class="">
            <button class="button button-filled" onclick="removeSwap(${swap.id})">Decline</button>
          </div>
          
          `
              : ''
          }
        </div>
      </div>
    </div>`;
    } else if (type === 'balance') {
      html += `  <div class="card singleSwap">
      <div class="card-content card-content-padding swapInfoContainer">
        <div class="row no-margin">
          <div class="col-50 viewFiles-infoCol">
          <div class="block-title text-align-left info infoSwap">
          <b>Swap Type:</b>
        </div>
        <div class="block-title text-align-left info infoSwap infoSwapTimeType">
         ${time_types[swap.time_type]}
        </div>
           
          </div>
          <div class="col-50 viewFiles-infoCol">
          <div class="block-title text-align-left info infoSwap">
          <b>Quantity Banked:</b>
        </div>
        <div class="block-title text-align-left info infoSwap">
        ${swap.sum_quantity}
        </div>
          </div>
          
          </div>
          </div>
          <a href="/balance_details/?cohort_id=${cohort_id}&time_type=${swap.time_type}&parent=${swap.request_from}" class="button button-fill">Details</a>
    </div>`;
    } else if (type === 'upcoming') {
      html += `
      <div class="card singleSwap">
      <div class="card-header singleSwapCardHeader"><b>Co-Swap Date:</b> ${new Date(swap.date).toLocaleString('en-us', {
        timeZone: 'UTC',
        month: 'short',
        day: 'numeric',
        year: 'numeric',
      })}</div>
      <div class="card-content card-content-padding swapInfoContainer">
        <div class="row no-margin">
          <div class="col-60 viewFiles-infoCol">
            <div class="block-title text-align-left info infoSwap infoSwapTimeType">
            Time Type : ${time_types[swap.time_type]}
            </div>
            ${
              swap.time_type === 7
                ? ` <div class="block-title text-align-left info infoSwap">
             Quantity : ${swap.quantity} 
            </div>`
                : ''
            }
            <div class="block-title text-align-left info">
            Requested by : ${swap.request_from === user.id ? user.first_name : parent2.first_name}
            </div>
            <div class="block-title text-align-left info">
            Gaining Co-Parent : ${swap.request_from === user.id ? user.first_name : parent2.first_name}
            </div>
            <div class="block-title text-align-left info">
            Losing Co-Parent : ${swap.request_from !== user.id ? user.first_name : parent2.first_name}
            </div>
            <div class="block-title text-align-left info">
            Approved At : ${new Date(swap.updated_at).toLocaleString('en-us', {
              timeZone: 'UTC',
              month: 'short',
              day: 'numeric',
              year: 'numeric',
            })}
            </div>
            <div class="block-title text-align-left info">
            Requested At : ${new Date(swap.created_at).toLocaleString('en-us', {
              timeZone: 'UTC',
              month: 'short',
              day: 'numeric',
              year: 'numeric',
            })}
            </div>
            <div class="block-title text-align-left info">
            Notes : ${swap.notes}
            </div>
          </div>
          <div class="col-40 swapInfoCallToAction">

          </div>
        </div>
      </div>
    </div>
    `;
    } else {
      html += `
      <div class="card singleSwap">
      <div class="card-header singleSwapCardHeader"><b>Co-Swap Date:</b> ${new Date(swap.date).toLocaleString('en-us', {
        timeZone: 'UTC',
        month: 'short',
        day: 'numeric',
        year: 'numeric',
      })}</div>
      <div class="card-content card-content-padding swapInfoContainer">
        <div class="row no-margin">
          <div class="col-60 viewFiles-infoCol">
            <div class="block-title text-align-left info infoSwap infoSwapTimeType">
            Time Type : ${time_types[swap.time_type]}
            </div>
            ${
              swap.time_type === 7
                ? ` <div class="block-title text-align-left info infoSwap">
             Quantity : ${swap.quantity} 
            </div>`
                : ''
            }
            <div class="block-title text-align-left info">
            Requested by : ${swap.request_from === user.id ? user.first_name : parent2.first_name}
            </div>
            <div class="block-title text-align-left info">
            Requested At : ${new Date(swap.created_at).toLocaleString('en-us', {
              timeZone: 'UTC',
              month: 'short',
              day: 'numeric',
              year: 'numeric',
            })}
            </div>
            <div class="block-title text-align-left info">
            Gaining Co-Parent : ${swap.request_from === user.id ? user.first_name : parent2.first_name}
            </div>
            <div class="block-title text-align-left info">
            Losing Co-Parent : ${swap.request_from !== user.id ? user.first_name : parent2.first_name}
            </div>
            <div class="block-title text-align-left info">
            Notes : ${swap.notes}
            </div>
          </div>
          <div class="col-40 swapInfoCallToAction">

          </div>
        </div>
      </div>
    </div>
    `;
    }
  });
  $$(id).html(html);
};

const initTabs = () => {
  const user = localStorage.getItem('user') ? JSON.parse(localStorage.getItem('user')) : {};

  const p1 = $$(`a[href^='#parent1']`);
  for (let i = 0; i < p1.length; i++) {
    p1[i]?.classList.add('tab-link-active');
  }
};

const renameTabs = () => {
  const user = localStorage.getItem('user') ? JSON.parse(localStorage.getItem('user')) : {};

  if ($$('#parent-balance-tabs')) {
    const tabsHtml = `
    <a href="#parent1-balance" class="tab-link button-outline tab-link-active button">${user.first_name}</a>
    <a href="#parent2-balance" class="tab-link button-outline button">${parent2?.first_name ? parent2.first_name : 'Co-Parent'}</a>
    `;
    $$('#parent-balance-tabs').html(tabsHtml);
  }
  if ($$('#parent-pending-tabs')) {
    const tabsHtml = `
    <a href="#parent1-pending" class="tab-link button-outline tab-link-active button">${user.first_name}</a>
    <a href="#parent2-pending" class="tab-link button-outline button">${parent2?.first_name ? parent2.first_name : 'Co-Parent'}</a>
    `;
    $$('#parent-pending-tabs').html(tabsHtml);
  }
  if ($$('#parent-upcoming-tabs')) {
    const tabsHtml = `
    <a href="#parent1-upcoming" class="tab-link button-outline tab-link-active button">${user.first_name}</a>
    <a href="#parent2-upcoming" class="tab-link button-outline button">${parent2?.first_name ? parent2.first_name : 'Co-Parent'}</a>
    `;
    $$('#parent-upcoming-tabs').html(tabsHtml);
  }
  if ($$('#parent-past-tabs')) {
    const tabsHtml = `
    <a href="#parent1-past" class="tab-link button-outline tab-link-active button">${user.first_name}</a>
    <a href="#parent2-past" class="tab-link button-outline button">${parent2?.first_name ? parent2.first_name : 'Co-Parent'}</a>
    `;
    $$('#parent-past-tabs').html(tabsHtml);
  }
};

async function getSwaps() {
  const user = localStorage.getItem('user') ? JSON.parse(localStorage.getItem('user')) : {};

  try {
    app.preloader.show();

    const cohortId = $$('#swap-cohort-drop-filter').val();
    let response = await fetch(`/api/user/swaps/${cohortId}`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
    });
    let cohortRes = await fetch(`/api/cohort/${cohortId}`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
    });
    let resJSON = await response.json();
    let { cohort } = await cohortRes.json();
    if (cohort?.parent2) {
      parent2 = cohort.parent2.id === user.id ? cohort.parent1 : cohort.parent2;
    } else {
      parent2 = null;
    }
    renameTabs();

    app.preloader.hide();
    const parent_1_balance = resJSON.balanceSwaps.filter((swap) => swap.request_from == user.id);
    const parent_1_pending = resJSON.swaps.filter((swap) => swap.status == 1 && new Date(swap.date) >= Date.now());
    const parent_1_upcoming = resJSON.swaps.filter((swap) => swap.status == 0 && new Date(swap.date) > Date.now());
    const parent_1_past = resJSON.swaps.filter((swap) => swap.status == 0 && new Date(swap.date) <= Date.now() && swap.request_from == user.id);
    const parent_2_balance = resJSON.balanceSwaps.filter((swap) => swap.request_from != user.id);
    // const parent_2_pending = resJSON.swaps.filter((swap) => swap.status == 1 && swap.request_from != user.id);
    // const parent_2_upcoming = resJSON.swaps.filter((swap) => swap.status == 0 && new Date(swap.date) > Date.now() && swap.request_from != user.id);
    const parent_2_past = resJSON.swaps.filter((swap) => swap.status == 0 && new Date(swap.date) <= Date.now() && swap.request_from != user.id);
    renderSwap('#parent1-balance', parent_1_balance, 'balance');
    renderSwap('#parent1-pending', parent_1_pending, 'pending');
    renderSwap('#parent1-upcoming', parent_1_upcoming, 'upcoming');
    renderSwap('#parent2-balance', parent_2_balance, 'balance');
    // renderSwap('#parent2-pending-container', parent_2_pending, 'pending');
    // renderSwap('#parent2-upcoming', parent_2_upcoming);
    renderSwap('#parent1-past', parent_1_past);
    renderSwap('#parent2-past', parent_2_past);
  } catch (error) {
    app.preloader.hide();
    // app.dialog.alert(error);
    console.log(error);
  }
}

async function approveCheck() {
  const user = localStorage.getItem('user') ? JSON.parse(localStorage.getItem('user')) : {};

  try {
    var mainview = app.view.main;
    app.preloader.show();

    let expenses = [];
    const approveSwaps = $$('.approveSwaps');
    for (let i = 0; i < approveSwaps.length; i++) {
      if (approveSwaps[i].checked) {
        expenses.push(approveSwaps[i].dataset.swap);
      }
    }
    if (!expenses.length) return;
    const cohortId = $$('#swap-cohort-drop-filter').val();
    let res = await fetch(`/api/user/swaps/approve/${cohortId}`, {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ swapIds: expenses }),
    });
    await getSwaps();
    app.preloader.hide();
  } catch (error) {
    app.preloader.hide();
    // app.dialog.alert(error);
    console.log(error);
  }
}

function initializeDateCalender() {
  const user = localStorage.getItem('user') ? JSON.parse(localStorage.getItem('user')) : {};

  var today = new Date();
  var dd = today.getDate();
  var mm = today.getMonth() + 1;
  var yyyy = today.getFullYear();
  if (dd == 0) {
    const timeStamp = new Date().getTime();
    const yesterdayTimeStamp = timeStamp - 24 * 60 * 60 * 1000;
    const yesterdayDate = new Date(yesterdayTimeStamp);
    dd = yesterdayDate.getDate();
    mm = yesterdayDate.getMonth() + 1;
    yyyy = yesterdayDate.getFullYear();
  }
  if (dd < 10) {
    dd = '0' + dd;
  }
  if (mm < 10) {
    mm = '0' + mm;
  }
  today = yyyy + '-' + mm + '-' + dd;
  console.log(today);
  var calendar = app.calendar.create({
    inputEl: '#requestswap-date',
    disabled: {
      to: today,
    },
    dateFormat: {
      month: 'long',
      day: 'numeric',
      year: 'numeric',
    },
  });
}

function handleTypeChange(e) {
  let value = 0;
  if (e?.target?.value) value = e?.target?.value;
  document.querySelector('#swap-qty-dd input').value = 1;
  if (value == 7) {
    document.querySelector('#swap-qty-dd input').setAttribute('required', true);
    document.getElementById('swap-qty-dd').style.display = 'block';
  } else {
    console.log(document.querySelector('#swap-qty-dd input'));
    document.querySelector('#swap-qty-dd input').removeAttribute('required');
    console.log(document.querySelector('#swap-qty-dd input'));
    document.getElementById('swap-qty-dd').style.display = 'none';
  }
}

function swap_checked() {
  const chkboxs = document.querySelectorAll('.approveSwaps') ? document.querySelectorAll('.approveSwaps') : [];
  let checked = false;

  for (let i = 0; i < chkboxs.length; i++) {
    if (chkboxs[i].checked) {
      checked = true;
      break;
    }
  }
  if (checked) {
    document.getElementById('approve-btn').removeAttribute('disabled');
    document.getElementById('approve-btn').classList.remove('disabled');
  } else {
    document.getElementById('approve-btn').classList.add('disabled');
    document.getElementById('approve-btn').setAttribute('disabled', true);
  }
}

async function fetchSwaps(page) {
  try {
    const parent = page?.route?.query.parent;
    const time_type = page?.route?.query.time_type;
    const cohortId = page?.route?.query.cohort_id;

    app.preloader.show();

    let response = await fetch(`/api/user/swaps/${cohortId}`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
    });

    const { swaps } = await response.json();
    console.log(response);
    let html = '';
    swaps
      .filter((swap) => swap.status == 0 && swap.time_type == time_type && swap.request_from == parent)
      .forEach((swap) => {
        html += `<div class="card singleSwap">
      <div class="card-header singleSwapCardHeader"><b>Co-Swap Date: </b>${new Date(swap.date).toLocaleString('en-us', {
        timeZone: 'UTC',
        month: 'short',
        day: 'numeric',
        year: 'numeric',
      })}</div>
      <div class="card-content card-content-padding swapInfoContainer">
        <div class="row no-margin">
          <div class="col-60 viewFiles-infoCol">
            <div class="block-title text-align-left info infoSwap infoSwapTimeType">
            Swap Type : ${time_types[swap.time_type]}
            </div>
            ${
              swap.time_type === 7
                ? `  <div class="block-title text-align-left info infoSwap">
            Quantity : ${swap.quantity} 
            </div>`
                : ''
            }
          
            <div class="block-title text-align-left info">
            Notes : ${swap.notes}
            </div>
          </div>
        </div>
      </div>
    </div>`;
      });

    $$('#swaps-details-list').append(html);

    app.preloader.hide();
  } catch (error) {
    console.log(error);
    app.preloader.hide();
  }
}

async function removeSwap(id) {
  try {
    app.preloader.show();
    let res = await fetch(`/api/user/swap/${id}`, {
      method: 'DELETE',
    });
    console.log(res);

    if (res.ok) {
      await getSwaps();
    }
    app.preloader.hide();
  } catch (error) {
    app.preloader.hide();
    console.log(error);
  }
}
