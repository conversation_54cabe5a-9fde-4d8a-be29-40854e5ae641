const axios = require('axios');
require('dotenv').config();

let config = {
  auth: { username: 'cc98c9f2e37f12db1fd541ae' },
};

const api_url = 'https://api.forwardemail.net/v1';

exports.getAliases = async () => {
  const aliases = await axios.get(`${api_url}/domains/cphsend.com/aliases`, config);
  return aliases;
};

exports.createAlias = async ({ name, recipients }) => {
  await axios.post(`${api_url}/domains/cphsend.com/aliases`, { name, recipients, is_enabled: true }, config);
};

exports.updateAlias = async ({ name, recipients }) => {
  const aliase = await axios.get(`${api_url}/domains/cphsend.com/aliases?name=${name}`, config);
  await axios.put(`${api_url}/domains/cphsend.com/aliases/${aliase.data[0].id}`, { recipients, is_enabled: true }, config);
};

exports.deleteAlias = async (name) => {
  const aliase = await axios.get(`${api_url}/domains/cphsend.com/aliases?name=${name}`, config);
  await axios.delete(`${api_url}/domains/cphsend.com/aliases/${aliase.data[0].id}`, config);
};
