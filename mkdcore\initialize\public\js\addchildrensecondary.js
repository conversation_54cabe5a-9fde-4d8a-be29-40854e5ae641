var $$ = Dom7;

async function addchildrenViewActiveCohort() {
    try {
        app.preloader.show();
        let response = await fetch('/api/cohort/', {
            method: 'GET',
            headers: {
                Accept: 'application/json',
                'Content-Type': 'application/json',
            },
        });
        let resJSON = await response.json();
        console.log(resJSON);
        if (resJSON.data.length == 0) {
            app.preloader.hide();
            app.dialog.alert("You don't have any active Co-Hort");
            throw "You don't have any active Co-Hort";
        }
        let optionSeq = 0;
        resJSON.data.forEach((cohort) => {
            optionSeq += 1;
            if (optionSeq == 1) {
                html = `
                <option selected value="${cohort.name}" data-cohortid="${cohort.id}">${cohort.name}</option>
                `;
                $$('#addchildrenChoseCohortSelect .item-inner .item-after').html("");
                $$('#addchildrenChoseCohortSelect .item-inner .item-after').html(cohort.name);
            }
            else {
                html = `
                <option value="${cohort.name}" data-cohortid="${cohort.id}">${cohort.name}</option>
                `;
            }
            $$('#addchildren-cohortselector').append(html);
        });
        app.preloader.hide();
        // getChildOfCohort();
    } catch (error) {
        app.preloader.hide();
        // app.dialog.alert(error);
        console.log(error);
    }
}

async function addChildSecondary() {
    var mainview = app.view.main;
    var childName = document.getElementById("addchildsecondary-childname").value;
    // var childAge = document.getElementById("addchildsecondary-childage").value;
    var selectedOption = document.getElementById('addchildren-cohortselector');
    var cohortID = selectedOption.options[selectedOption.selectedIndex].getAttribute('data-cohortid');
    try {
        app.preloader.show();
        if (addChildValidateFields(childName) && document.querySelectorAll('.input-invalid').length == 0) {
            let body = {
                name: childName,
                age: 10,
            };
            let response = await fetch(`/api/user/children/cohort/${cohortID}`, {
                method: 'POST',
                headers: {
                    Accept: 'application/json',
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(body),
            });
            let resJSON = await response.json();
            if (response.status == 200) {
                app.preloader.hide();
                app.dialog.alert("Child added successfully");
                mainview.router.navigate({ name: 'viewcohort' });
            }
            if (response.status == 404) {
                app.preloader.hide();
                app.dialog.alert("Co-Hort not found");
            }
        }
        else {
            app.preloader.hide();
            throw "These field(s) are required: <br> Child Name <br>"
        }
    } catch (error) {
        app.preloader.hide();
        app.dialog.alert(error);
    }

}