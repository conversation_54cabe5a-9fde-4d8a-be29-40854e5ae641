{"name": "node_code_builder", "version": "1.0.0", "description": "", "main": "lambda.js", "scripts": {"deploy": "claudia create --region us-east-2 --handler lambda.handler", "update": "claudia update"}, "keywords": [], "author": "<PERSON>", "private": true, "license": "Private", "dependencies": {"apollo-server-express": "^2.11.0", "aws-serverless-express": "^3.3.7", "bcryptjs": "^2.4.3", "body-parser": "^1.19.0", "claudia": "^5.12.0", "cookie-parser": "~1.4.4", "dotenv": "^8.2.0", "express": "^4.17.1", "firebase-admin": "^11.11.1", "graphql-fields": "^2.0.3", "helmet": "^3.19.0", "jsonwebtoken": "^8.5.1", "moment": "^2.26.0", "morgan": "~1.9.1", "mysql2": "^2.1.0", "sequelize": "^6.29.0", "twilio": "^5.5.0"}}