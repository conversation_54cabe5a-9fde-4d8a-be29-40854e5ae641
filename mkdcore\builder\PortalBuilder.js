/*Powered By: Manaknightdigital Inc. https://manaknightdigital.com/ Year: 2020*/
/**
 * Portal builder
 * @copyright 2020 Manaknightdigital Inc.
 * @link https://manaknightdigital.com
 * @license Proprietary Software licensing
 * <AUTHOR>
 *
 */

const fs = require('fs');
const path = require('path');
const Builder = require('./Builder');
const browserify = require('browserify');

const validation_field_mapper = require('../source/utils/controllers/validations/field_mapper');
const inputFieldMapper = require('../source/utils/controllers/views/input_field_mapper');
const { SyncMapItemInstance } = require('twilio/lib/rest/preview/sync/service/syncMap/syncMapItem');

module.exports = function (config) {
  this._config = config;
  this._models = [];
  this._roles = [];
  this._portals = [];
  this._routes = [];
  this._render_list = [];
  this.finalTemplate = {
    login: '',
    register: '',
    forgot: '',
    google: '',
    facebook: '',
  };

  Builder.call(this);

  this.set_roles = function (roles) {
    this._roles = roles;
  };

  this.set_portals = function (portals) {
    this._portals = portals;
  };
  this.set_models = function (models) {
    this._models = models;
  };

  this.set_routes = function (routes) {
    this._routes = routes;
  };

  this.build = function () {
    for (let i = 0; i < this._portals.length; i++) {
      //Build Portal Controller
      //Build Profile Page
      //Build Profile View Model
      //If API, build Portal API
      //Add Token Service
      //If Login type set, add controller
      //If Register type set, add controller
      //If Forgot type set, add controller
      //If Reset type set, add controller
      //Build Layout
      //Build Dashboard
      //Build CSS/JS
      //Build Menu
    }

    for (let i = 0; i < this._roles.length; i++) {
      const role = this._roles[i];

      try {
        this._portals.forEach((portal) => {
          if (portal.role === role.name) {
            this.setup_portal(portal, role);
            this.setup_profile(portal, role);
            this.generate_menu(portal, role);
            this.generate_dashboard(portal, role);

            if (portal.contact) {
              this.generateContactForm(portal, role);
            }
          }
        });
      } catch (err) {
        console.error('Portal  Build Error', err);
      }
    }

    for (const key in this._render_list) {
      const page = this._render_list[key];
      try {
        this.writeFileSyncRecursive(key, page, {
          mode: 0775,
        });
      } catch (error) {
        console.log('Portal Build Error', error);
      }
    }
    this.generate_index_for_controllers_type();
    this.generate_index_file();
    console.log('Portal Build success');
  };

  this.generateContactForm = (portal, role) => {
    // Generate contact form for each portal

    // Copy controller template
    let contactController = fs.readFileSync('../mkdcore/source/portal/contact_controller.js', 'utf-8');
    contactController = this.inject_substitute(contactController, 'portal', portal.name);
    contactController = this.inject_substitute(contactController, 'role_id', role.id);

    this._render_list['../../release/controllers/' + portal.name + '/Contact_controller.js'] = contactController;

    // Copy Add
    let contactAddViewModel = fs.readFileSync('../mkdcore/source/portal/contact_add_view_model.js', 'utf-8');
    contactAddViewModel = this.inject_substitute(contactAddViewModel, 'portal', portal.name);

    this._render_list['../../release/view_models/contact_' + portal.name + '_add_view_model.js'] = contactAddViewModel;

    let contactAddView = fs.readFileSync('../mkdcore/source/views/custom/Add_Contact.eta', 'utf-8');
    contactAddView = this.inject_substitute(contactAddView, 'portal', portal.name);

    this._render_list['../../release/views/' + portal.name + '/Add_contact.eta'] = contactAddView;

    // Contact Paginate list
    let contactPaginateViewModel = fs.readFileSync('../mkdcore/source/portal/contact_paginate_list_view_model.js', 'utf-8');

    this._render_list['../../release/view_models/contact_' + portal.name + '/list_paginate_view_model.js'] = contactPaginateViewModel;

    let contactListView = fs.readFileSync('../mkdcore/source/views/custom/Contact.eta', 'utf-8');
    contactListView = this.inject_substitute(contactListView, 'portal', portal.name);

    this._render_list['../../release/views/' + portal.name + '/Contact.eta'] = contactListView;

    // Contact Detail

    let contactDetailViewModel = fs.readFileSync('../mkdcore/source/portal/contact_detail_view_model.js', 'utf-8');

    this._render_list['../../release/view_models/contact_' + portal.name + '_detail_view_model.js'] = contactDetailViewModel;

    let contactDetailView = fs.readFileSync('../mkdcore/source/views/custom/Contact.eta', 'utf-8');
    contactDetailView = this.inject_substitute(contactDetailView, 'portal', portal.name);
    this._render_list['../../release/views/' + portal.name + '/View_contact.eta'] = contactListView;

    // Delete view model
    let contactDeleteViewModel = fs.readFileSync('../mkdcore/source/portal/contact_delete_view_model.js', 'utf-8');

    this._render_list['../../release/view_models/contact_' + portal.name + '_delete_view_model.js'] = contactDeleteViewModel;
  };

  this.generate_index_file = function () {
    // Generate index file inside each controllers type
    const routes_path = path.join(__dirname, '../../release/routes');
    const directoryContents = this.directoryContents(routes_path);

    if (Array.isArray(directoryContents)) {
      const roles = new Set(this._roles.map((item) => item.name));
      directoryContents.forEach((item) => {
        if (!item.isFile && roles.has(item.name)) {
          const index_content = this.generateIndexFile(path.join(routes_path, item.name), true);

          const format_file_names = index_content.allFiles.map((item) => this.ucFirst(this.toCamelCaseString(item.replace(/.js$/g, ''))));

          const index_text =
            index_content.content +
            `\n\nmodule.exports = [${format_file_names.join()}]           
           `;
          this.writeFileSyncRecursive(path.join('../../release/routes', item.name, 'index.js'), index_text, {
            mode: 0775,
          });
        }
      });
    }
  };

  this.generate_index_for_controllers_type = function () {
    // Generate index file for each controllers type
    let main_index = `{{{controllers_imports}}}\n\n\nmodule.exports = [{{{controllers}}}];`;
    const directoryContents = this.directoryContents(path.join(__dirname, '../../release/routes'));

    const allImports = [];
    const allControllers = [];
    directoryContents.forEach((item) => {
      if (!item.isFile) {
        const ucName = this.ucFirst(item.name);
        allImports.push(`const ${ucName}Routes = require("./${item.name}/index");`);
        allControllers.push(`${ucName}Routes`);
      }
    });
    main_index = this.inject_substitute(main_index, 'controllers_imports', allImports.join('\n'));
    main_index = this.inject_substitute(main_index, 'controllers', allControllers.join());

    this.writeFileSyncRecursive('../../release/routes/index.js', main_index, {
      mode: 0775,
    });
  };

  this.generate_menu = function (portal, role) {
    const navItems = [];

    Object.entries(portal.menu).forEach(([key, value]) => {
      if (typeof value === 'object' && value === Object(value)) {
        const dropdownItems = Object.entries(value).map(
          ([_key, _value]) =>
            `<li><a href="/${role.name}${_value}" class="menu-item dropdownMenuItem <%= it._base_url === '/${role.name}${
              _value.split('/0')[0]
            }' ? 'menu-active':''%>">${_key}</a></li>`,
        );
        navItems.push(`<div class="card">
        <div class="card-header p-0" id="${key}">
            <button class="btn list-group-item-action custom-list-item <%= [${Object.entries(value).map(
              ([_key, _value]) => `'/${role.name}${_value.split('/0')[0]}'`,
            )}].includes(it._base_url) ? 'active' : '' %>"
              data-toggle="collapse" data-target="#collapse_${key.toLocaleLowerCase().replace(/ /g, '_')}" aria-expanded="false" aria-controls="collapseOne">
                ${key}
              <i class="fa fa-caret-down" aria-hidden="true"></i>
            </button>
        </div>
        <div id="collapse_${key.toLocaleLowerCase().replace(/ /g, '_')}" class="collapse <%= [${Object.entries(value).map(
          ([_key, _value]) => `'/${role.name}${_value.split('/0')[0]}'`,
        )}].includes(it._base_url) ? 'show' : ''%>"
          aria-labelledby="headingOne" data-parent="#accordion">
          <div class="card-body p-0">
           ${dropdownItems.join('\n')}
          </div>
        </div>
      </div>`);
      } else {
        navItems.push(`<li><a href="/${role.name}${value}" class="menu-item <%= it._base_url === '/${role.name}${value.split('/0')[0]}' ? 'menu-active':''%>">${key}</a></li>`);
      }
    });

    let navTemplate = fs.readFileSync('../mkdcore/source/views/partials/Nav.eta', 'utf-8');
    navTemplate = this.inject_substitute(navTemplate, 'menu', navItems.join('\n'));
    navTemplate = this.inject_substitute(navTemplate, 'copyright', this._config.config.copyright);
    navTemplate = this.inject_substitute(navTemplate, 'powered_by', this._config.config.powered_by);
    navTemplate = this.inject_substitute(navTemplate, 'company', this._config.config.company);

    this._render_list['../../release/views/partials/' + role.name + '/Nav.eta'] = this.inject_substitute(navTemplate, 'qwertyuiop', '');

    // Generate Global Response
    let responseTemplate = fs.readFileSync('../mkdcore/source/views/partials/GlobalResponse.eta', 'utf-8');

    this._render_list['../../release/views/partials/' + role.name + '/GlobalResponse.eta'] = this.inject_substitute(responseTemplate, 'qwertyuiop', '');

    // Generate Breadcrumb as well
    let breadcrumbsTemplate = fs.readFileSync('../mkdcore/source/views/partials/Breadcrumb.eta', 'utf-8');

    this._render_list['../../release/views/partials/' + role.name + '/Breadcrumb.eta'] = this.inject_substitute(breadcrumbsTemplate, 'qwertyuiop', '');

    // Generate Footer
    let footerTemplate = fs.readFileSync('../mkdcore/source/views/partials/Footer.eta', 'utf-8');

    this._render_list['../../release/views/partials/' + role.name + '/Footer.eta'] = this.inject_substitute(footerTemplate, 'qwertyuiop', '');
  };

  this.generate_dashboard = (portal, role) => {
    // By default every portal will have dashboard route

    let dashboardTemplate = fs.readFileSync('../mkdcore/source/views/custom/Dashboard.eta', 'utf-8');

    let dashboardController = fs.readFileSync('../mkdcore/source/portal/dashboard.js', 'utf-8');

    dashboardController = this.inject_substitute(dashboardController, 'portal', role.name);
    dashboardController = this.inject_substitute(dashboardController, 'role_id', role.id);

    this._render_list['../../release/controllers/' + role.name + '/' + 'Dashboard.js'] = dashboardController;

    dashboardTemplate = this.inject_substitute(dashboardTemplate, 'role', role.name);

    this._render_list['../../release/views/' + portal.name + '/Dashboard.eta'] = dashboardTemplate;
  };

  this.inject_copyright_and_powered_by = function (template = '') {
    const copyright = this._config.config.copyright;
    const powered_by = this._config.config.powered_by;

    template = this.inject_substitute(template, 'copyright', copyright);
    template = this.inject_substitute(template, 'powered_by', powered_by);

    return template;
  };

  this.setup_portal = function (portal, role) {
    // Make layouts for each portal so that each portal can inject their own defined css and js

    let mainLayout = fs.readFileSync('../mkdcore/source/views/layouts/Main.eta', 'utf-8');

    let clanLayout = fs.readFileSync('../mkdcore/source/views/layouts/Clean.eta', 'utf-8');

    const portalJsFiles = [...portal.js];
    const portalCssFiles = [...portal.css];
    const portalScripts = [...portal.script];

    // build module
    if (portal.module.length) {
      const modules_path = '../mkdcore/initialize/public/modules';
      const output_path = '../../release/public/';

      try {
        portalJsFiles.push('/js/module.merge.js|defer');
        portalCssFiles.push('/css/module.merge.css');

        let moduleMergedJs = '';
        let moduleMergedCss = '';

        portal.module.forEach(async (module) => {
          const jsFilePathIn = path.join(modules_path, 'js', module) + '.js';
          const cssFilePathIn = path.join(modules_path, 'css', module) + '.css';
          const scriptFilePathIn = path.join(modules_path, 'scripts', module) + '.script';

          if (fs.existsSync(jsFilePathIn)) {
            const b = browserify();
            b.add(jsFilePathIn);
            b.bundle((err, src) => {
              if (err) throw err;
              else {
                moduleMergedJs += `\n ${src} \n`;

                this.createDirectoriesRecursive(output_path);
                fs.writeFileSync(path.join(__dirname, output_path, 'js', 'module.merge.js'), moduleMergedJs, {
                  mode: 0775,
                });
              }
            });
          }

          if (fs.existsSync(cssFilePathIn)) {
            const cssFile = fs.readFileSync(cssFilePathIn, 'utf-8');
            moduleMergedCss += `\n ${cssFile} \n`;

            this.createDirectoriesRecursive(output_path);
            fs.writeFileSync(path.join(__dirname, output_path, 'css', 'module.merge.css'), moduleMergedCss, {
              mode: 0775,
            });
          }

          if (fs.existsSync(scriptFilePathIn)) {
            const script = fs.readFileSync(scriptFilePathIn, 'utf-8');
            portalScripts.push(script);
          }
        });
      } catch (error) {
        console.error('Portal Builder Build Error: While generating modules', error);
      }
    }

    const allJS = portalJsFiles.map((item) => {
      if (item.includes('|')) {
        let scriptName = item.split('|')[0];
        let scriptLoadTime = item.split('|')[1];
        return `<script src="${scriptName}" ${scriptLoadTime}></script>`;
      } else {
        return `<script src="${item}"></script>`;
      }
    });

    const allScripts = portalScripts;

    const allCSS = portalCssFiles.map((item) => `<link rel="stylesheet" href="${item}"></link>`);

    let headerLayout = fs.readFileSync('../mkdcore/source/views/partials/Header.eta', 'utf-8');

    headerLayout = this.inject_substitute(headerLayout, 'js', allJS.join('\n\t'));
    headerLayout = this.inject_substitute(headerLayout, 'css', allCSS.join('\n\t'));
    headerLayout = this.inject_substitute(headerLayout, 'script', allScripts.join('\n\t'));

    mainLayout = this.inject_substitute(mainLayout, 'role', role.name);
    clanLayout = this.inject_substitute(clanLayout, 'role', role.name);

    this._render_list['../../release/views/partials/' + portal.role + '/Header.eta'] = headerLayout;
    this._render_list['../../release/views/layouts/' + portal.role + '/Main.eta'] = mainLayout;
    this._render_list['../../release/views/layouts/' + portal.role + '/Clean.eta'] = clanLayout;

    // Initialize viewModel
    let viewModel = fs.readFileSync('../mkdcore/source/portal/Auth_view_model.js', 'utf-8');

    viewModel = this.inject_substitute(viewModel, 'uc_role', this.ucFirst(role.name));

    viewModel = this.inject_substitute(viewModel, 'role', portal.role);

    viewModel = this.inject_substitute(viewModel, 'year', new Date().getFullYear());

    // Profile fields
    let viewModelProfileFields = portal.profile_page_fields.map((field) => {
      if (field.split('~')[1] && field.split('~')[1].includes('.')) {
        return field.split('~')[1];
      } else {
        return field.split('~')[0];
      }
    });

    viewModelProfileFields = viewModelProfileFields
      .map((item) => {
        return `'${item}': ''`;
      })
      .join(',');

    viewModel = this.inject_substitute(viewModel, 'profile_fields', viewModelProfileFields);

    this._render_list['../../release/view_models/' + role.name + '_auth_view_model.js'] = viewModel;

    // If Login is enabled
    if (portal.login) {
      let finalLogin = '';

      const [{ login, logout }, { apiLogin, apiLogout }] = this.setup_login(portal, role);

      let loginTemplate = fs.readFileSync('../mkdcore/source/portal/portal_login_template.js', 'utf-8');

      finalLogin += login;
      if (portal.api) {
        finalLogin += '\n\n\n' + apiLogin;
      }

      loginTemplate = this.inject_substitute(loginTemplate, 'role_id', role.id);
      loginTemplate = this.inject_substitute(loginTemplate, 'login', finalLogin);

      this._render_list['../../release/routes/' + role.name + '/login.js'] = loginTemplate;

      let finalLogout = '';

      let logoutTemplate = fs.readFileSync('../mkdcore/source/portal/portal_logout_template.js', 'utf-8');

      finalLogout += logout;
      if (portal.api) {
        finalLogout += '\n\n\n' + apiLogout;
      }

      logoutTemplate = this.inject_substitute(logoutTemplate, 'logout', finalLogout);

      this._render_list['../../release/routes/' + role.name + '/logout.js'] = logoutTemplate;
    }

    // If register is enabled
    if (portal.register) {
      let final = '';
      const [register, apiRegister] = this.setup_register(portal, role);

      let registerTemplate = fs.readFileSync('../mkdcore/source/portal/portal_register_template.js', 'utf-8');
      final += register;
      if (portal.api) {
        final += '\n\n\n' + apiRegister;
      }

      registerTemplate = this.inject_substitute(registerTemplate, 'register', final);
      registerTemplate = this.inject_substitute(registerTemplate, 'role_id', role.id);

      this._render_list['../../release/routes/' + role.name + '/register.js'] = registerTemplate;
    }

    // If forgot password is enabled
    if (portal.forgot) {
      let final = '';
      const [forgot, apiForgot] = this.setup_forgot(portal, role);

      let forgotTemplate = fs.readFileSync('../mkdcore/source/portal/portal_forgot_template.js', 'utf-8');
      final += forgot;

      if (portal.api) {
        final += '\n\n\n' + apiForgot;
      }

      forgotTemplate = this.inject_substitute(forgotTemplate, 'forgot', final);
      forgotTemplate = this.inject_substitute(forgotTemplate, 'role_id', role.id);

      this._render_list['../../release/routes/' + role.name + '/forgot.js'] = forgotTemplate;
    }

    // If google  is enabled
    if (portal.google) {
      let google_template = fs.readFileSync('../mkdcore/source/portal/portal_google_template.js', 'utf-8');
      const [googleControllers, googleAPIControllers] = this.setup_google(portal, role);

      google_template = this.inject_substitute(google_template, 'google', googleControllers);
      google_template = this.inject_substitute(google_template, 'google_api', googleAPIControllers);

      this._render_list['../../release/routes/' + role.name + '/google.js'] = google_template;
    }

    // If facebook  is enabled
    if (portal.facebook) {
      let facebook_template = fs.readFileSync('../mkdcore/source/portal/portal_facebook_template.js', 'utf-8');
      const [facebookControllers, facebookAPIControllers] = this.setup_facebook(portal, role);

      facebook_template = this.inject_substitute(facebook_template, 'facebook', facebookControllers);
      facebook_template = this.inject_substitute(facebook_template, 'facebook_api', facebookAPIControllers);

      this._render_list['../../release/routes/' + role.name + '/facebook.js'] = facebook_template;
    }

    // Two Factor Authentication
    if (portal.two_factor_authentication) {
      const [twoFA, API_twoFA] = this.setup_two_factor_authentication(portal, role);

      let twoFATemplate = fs.readFileSync('../mkdcore/source/portal/controller_2FA_template.js', 'utf-8');

      twoFATemplate = this.inject_substitute(twoFATemplate, 'role_id', role.id);
      twoFATemplate = this.inject_substitute(twoFATemplate, 'twoFA', twoFA);
      twoFATemplate = this.inject_substitute(twoFATemplate, 'API_TwoFA', API_twoFA);

      this._render_list['../../release/routes/' + portal.name + '/two_factor_authentication.js'] = twoFATemplate;
    }
  };

  // Login
  this.setup_login = function (portal, role) {
    // Check if model exists or not

    if (!this._config.models.find((model) => model.name === portal.model)) {
      throw new Error(portal.model + " doesn't exist in models");
    }

    let loginControllerTemplate = fs.readFileSync('../mkdcore/source/portal/portal_login.js', 'utf-8');

    let logoutControllerTemplate = fs.readFileSync('../mkdcore/source/portal/portal_logout.js', 'utf-8');

    loginControllerTemplate = this.inject_substitute(loginControllerTemplate, 'model', portal.model);

    loginControllerTemplate = this.inject_substitute(loginControllerTemplate, 'role', role.name);

    logoutControllerTemplate = this.inject_substitute(logoutControllerTemplate, 'role', role.name);

    loginControllerTemplate = this.inject_substitute(loginControllerTemplate, 'role_id', role.id);

    if (portal.remember_me) {
      loginControllerTemplate = this.inject_substitute(
        loginControllerTemplate,
        'remember_me',
        `
        if (req.body.remember_me === 'on') {
          const day = 60 * 60 * 1000 * 24;
          req.session.cookie.expires = new Date(Date.now() + day * 31);
          req.session.cookie.maxAge = day * 31;
        }`,
      );
    } else {
      loginControllerTemplate = this.inject_substitute(loginControllerTemplate, 'remember_me', '');
    }

    if (this._config.packages.multitenant.active) {
      loginControllerTemplate = this.inject_substitute(
        loginControllerTemplate,
        'multitenant',
        `
        const isOrganizationUser = await db.organization.getByPK(
          user.organization_id,
        );

        if (isOrganizationUser) {
          const { status } = isOrganizationUser;

          if (status !== 1) {
            const status_mapping = db.organization.status_mapping();

            viewModel.error = 'Your organization ' + status_mapping[status];
            return res.render('admin/Login', viewModel);
          }
        }
        `,
      );
    } else {
      loginControllerTemplate = this.inject_substitute(loginControllerTemplate, 'multitenant', '');
    }

    if (this._config.packages.permission.active) {
      loginControllerTemplate = this.inject_substitute(
        loginControllerTemplate,
        'permission',
        `
        const user_permissions = await db.user_permission.getByField(
          'user_id',
          user.id,
        );

        const permissions = [];

        if (user_permissions) {
            const async_permissions = user_permissions.permissions
              .split(',')
              .map((permission) => {
                return db.permission.getByPK(permission);
              });

            const resolved_permission = await Promise.all(async_permissions);

            const filterNulls = resolved_permission.filter(Boolean);
            // .filter(Boolean) to remove null values, null values form when permission is deleted

            filterNulls.forEach((permission) => {
              permissions.push(permission.route);
            });
          }


        session.permissions = permissions;
        `,
      );
    } else {
      loginControllerTemplate = this.inject_substitute(loginControllerTemplate, 'permission', '');
    }

    loginControllerTemplate = this.inject_substitute(
      loginControllerTemplate,
      'two_factor_authentication',
      portal.two_factor_authentication
        ? ` if (accountExists.two_factor_authentication === 1) {
            req.session.two_factor_authentication = true;
            const phoneNumber = user.phone;
            await viewModel.sendSMS(phoneNumber, user.id);
          }`
        : '',
    );

    let loginView = fs.readFileSync('../mkdcore/source/views/custom/Login.eta', 'utf-8');

    loginView = this.inject_copyright_and_powered_by(loginView);

    loginView = this.inject_substitute(loginView, 'title', this._config.project_name);
    loginView = this.inject_substitute(loginView, 'role', role.name);

    // If Forgot Password link should be included or not
    let forgotTemplate = '';
    if (portal.forgot) {
      forgotTemplate = `<div class="form-group mt-3 d-flex justify-content-start">\n<a href="/${role.name}/forgot" id="mkd-forgot-password-link">xyzForgot_password ?</a>\n</div>`;
    }
    loginView = this.inject_substitute(loginView, 'forgotPassword', forgotTemplate);

    let socialLoginTemplate = '';
    if (portal.google) {
      // TODO: Social Login controller

      if (portal.google) {
        socialLoginTemplate += `\n<div class="text-center mt-3">\n<span> OR </span>\n</div>\n`;

        const googleLogin = ` <div class="form-group mt-3 ">
        <a href="<%= it.google_auth_url%>" class="btn btn-accent-light-outline btn-sign-up google-button" >\n<i class="fab fa-google" aria-hidden="true"></i>&nbsp;\n xyzLogin_with_Google</a>\n </div>`;

        socialLoginTemplate = socialLoginTemplate + '\n' + googleLogin;
      }
      if (portal.facebook) {
        const facebookLogin = `<div class="form-group mt-3">
        <a href="<%= it.facebook_auth_url%>" class="btn btn-accent-light-outline btn-sign-up facebook-button" > \n<i class="fab fa-facebook" aria-hidden="true"></i>&nbsp;\n xyzLogin_with_Facebook</a>\n </div>`;
        socialLoginTemplate = socialLoginTemplate + '\n' + facebookLogin;
      }
    }
    loginView = this.inject_substitute(loginView, 'socialLogins', socialLoginTemplate);

    loginView = this.inject_substitute(
      loginView,
      'dontHaveAnAccountYet',
      portal.register
        ? `<div class="text-center ">\n<span>xyzDontHaveAnAccount
            <a href="/${role.name}/register">xyzRegister</a>\n</span>\n</div>`
        : '',
    );

    if (portal.remember_me) {
      loginView = this.inject_substitute(
        loginView,
        'remember_me',
        `
        <div class="form-check">
          <input class="form-check-input" type="checkbox"  id="remember_me" name="remember_me" >
          <label class="form-check-label" for="remember_me">
          Remember me
          </label>
       </div>`,
      );
    } else {
      loginView = this.inject_substitute(loginView, 'remember_me', '');
    }

    // Final Login View
    this._render_list['../../release/views/' + role.name + '/Login.eta'] = loginView;

    let loginAPIControllerTemplate = '';
    let logoutAPIControllerTemplate = '';
    if (portal.api) {
      loginAPIControllerTemplate = fs.readFileSync('../mkdcore/source/portal/api_portal_login.js', 'utf-8');

      logoutAPIControllerTemplate = fs.readFileSync('../mkdcore/source/portal/api_portal_logout.js', 'utf-8');

      loginAPIControllerTemplate = this.inject_substitute(loginAPIControllerTemplate, 'role', role.name);

      logoutAPIControllerTemplate = this.inject_substitute(logoutAPIControllerTemplate, 'role', role.name);

      loginAPIControllerTemplate = this.inject_substitute(loginAPIControllerTemplate, 'role_id', role.id);
    }

    return [
      { login: loginControllerTemplate, logout: logoutControllerTemplate },
      {
        apiLogin: loginAPIControllerTemplate,
        apiLogout: logoutAPIControllerTemplate,
      },
    ];
  };

  // Register
  this.setup_register = function (portal, role) {
    // Check if model exists or not

    if (!this._config.models.find((model) => model.name === portal.model)) {
      throw new Error(portal.model + " doesn't exist in models");
    }

    let registerControllerTemplate = fs.readFileSync('../mkdcore/source/portal/portal_register.js', 'utf-8');

    registerControllerTemplate = this.inject_substitute(registerControllerTemplate, 'model', portal.model);

    registerControllerTemplate = this.inject_substitute(registerControllerTemplate, 'role', role.name);

    registerControllerTemplate = this.inject_substitute(registerControllerTemplate, 'role_id', role.id);

    let registerView = fs.readFileSync('../mkdcore/source/views/custom/Register.eta', 'utf-8');

    registerView = this.inject_copyright_and_powered_by(registerView);

    registerView = this.inject_substitute(registerView, 'title', this._config.project_name);
    registerView = this.inject_substitute(registerView, 'role', role.name);

    let socialLoginTemplate = '';
    if (portal.google) {
      // TODO: Social Register controllers
      if (portal.google) {
        socialLoginTemplate += `\n<div class="text-center mt-3">\n<span> OR </span>\n</div>\n`;
        const googleLogin = ` <div class="form-group mt-3">
        <a href="<%= it.google_auth_url%>" class="btn btn-accent-light-outline btn-sign-up google-button" >\n<i class="fab fa-google" aria-hidden="true"></i>&nbsp;\n xyzSignup_with_Google</a>\n </div>`;

        socialLoginTemplate = socialLoginTemplate + '\n' + googleLogin;
      }
      if (portal.facebook) {
        const facebookLogin = ` <div class="form-group mt-3">
        <a href="<%= it.facebook_auth_url%>" class="btn btn-accent-light-outline btn-sign-up facebook-button" > \n<i class="fab fa-facebook" aria-hidden="true"></i>&nbsp;\n xyzSignup_with_Facebook</a>\n </div>`;
        socialLoginTemplate = socialLoginTemplate + '\n' + facebookLogin;
      }
    }
    registerView = this.inject_substitute(registerView, 'socialLogins', socialLoginTemplate);

    registerView = this.inject_substitute(
      registerView,
      'alreadyHaveAnAccount',
      portal.login
        ? ` <div class="text-center mt-4">\n<span>xyzAlreadyHaveAnAccount
          <a href="/${role.name}/login">xyzLogin</a>\n </span>\n</div>`
        : '',
    );

    // Final Register View
    this._render_list['../../release/views/' + role.name + '/Register.eta'] = registerView;

    let registerAPIControllerTemplate = fs.readFileSync('../mkdcore/source/portal/api_portal_register.js', 'utf-8');
    if (portal.api) {
      registerAPIControllerTemplate = this.inject_substitute(registerAPIControllerTemplate, 'role_id', role.id);
      registerAPIControllerTemplate = this.inject_substitute(registerAPIControllerTemplate, 'role', role.name);
    }

    return [registerControllerTemplate, registerAPIControllerTemplate];
  };

  // Setup forgot password and all its related stuffs
  this.setup_forgot = function (portal, role) {
    // Check if model exists or not

    if (!this._config.models.find((model) => model.name === portal.model)) {
      throw new Error(portal.model + " doesn't exist in models");
    }

    let forgotControllerTemplate = fs.readFileSync('../mkdcore/source/portal/portal_forgot.js', 'utf-8');

    forgotControllerTemplate = this.inject_substitute(forgotControllerTemplate, 'model', portal.model);

    forgotControllerTemplate = this.inject_substitute(forgotControllerTemplate, 'role', role.name);

    forgotControllerTemplate = this.inject_substitute(forgotControllerTemplate, 'role_id', role.id);

    // Forgot View
    let forgotView = fs.readFileSync('../mkdcore/source/views/custom/Forgot.eta', 'utf-8');

    forgotView = this.inject_substitute(forgotView, 'role', role.name);

    // Reset View
    let resetView = fs.readFileSync('../mkdcore/source/views/custom/Reset.eta', 'utf-8');

    resetView = this.inject_substitute(resetView, 'role', role.name);

    this._render_list['../../release/views/' + role.name + '/Forgot.eta'] = forgotView;
    this._render_list['../../release/views/' + role.name + '/Reset.eta'] = resetView;

    let forgotAPIControllerTemplate = '';
    if (portal.api) {
      //
    }

    return [forgotControllerTemplate, forgotAPIControllerTemplate];
  };

  this.setup_google = function (portal, role) {
    if (!this._config.models.find((model) => model.name === portal.model)) {
      throw new Error(portal.model + " doesn't exist in models");
    }

    let googleControllerTemplate = fs.readFileSync('../mkdcore/source/portal/portal_google.js', 'utf-8');

    googleControllerTemplate = this.inject_substitute(googleControllerTemplate, 'role', role.name);

    googleControllerTemplate = this.inject_substitute(googleControllerTemplate, 'role_id', role.id);
    let googleControllerAPITemplate = '';
    if (portal.api) {
      googleControllerAPITemplate = fs.readFileSync('../mkdcore/source/portal/api_portal_google.js', 'utf-8');

      googleControllerAPITemplate = this.inject_substitute(googleControllerAPITemplate, 'portal', portal.name);
      googleControllerAPITemplate = this.inject_substitute(googleControllerAPITemplate, 'role_id', role.id);
    }
    return [googleControllerTemplate, googleControllerAPITemplate];
  };

  this.setup_facebook = function (portal, role) {
    if (!this._config.models.find((model) => model.name === portal.model)) {
      throw new Error(portal.model + " doesn't exist in models");
    }

    let facebookControllerTemplate = fs.readFileSync('../mkdcore/source/portal/portal_facebook.js', 'utf-8');

    facebookControllerTemplate = this.inject_substitute(facebookControllerTemplate, 'role', role.name);

    facebookControllerTemplate = this.inject_substitute(facebookControllerTemplate, 'role_id', role.id);
    let facebookControllerAPITemplate = '';
    if (portal.api) {
      facebookControllerAPITemplate = fs.readFileSync('../mkdcore/source/portal/api_portal_facebook.js', 'utf-8');

      facebookControllerAPITemplate = this.inject_substitute(facebookControllerAPITemplate, 'portal', portal.name);
      facebookControllerAPITemplate = this.inject_substitute(facebookControllerAPITemplate, 'role_id', role.id);
    }
    return [facebookControllerTemplate, facebookControllerAPITemplate];
  };

  this.setup_two_factor_authentication = function (portal, role) {
    if (!this._config.models.find((model) => model.name === portal.model)) {
      throw new Error(portal.model + " doesn't exist in models");
    }

    // Controllers
    let twoFAControllerTemplate = fs.readFileSync('../mkdcore/source/portal/controller_2FA.js', 'utf-8');

    twoFAControllerTemplate = this.inject_substitute(twoFAControllerTemplate, 'portal', role.name);

    twoFAControllerTemplate = this.inject_substitute(twoFAControllerTemplate, 'role_id', role.id);

    // View
    let twoFAView = fs.readFileSync('../mkdcore/source/views/custom/Account_verification.eta', 'utf-8');

    twoFAView = this.inject_substitute(twoFAView, 'portal', role.name);
    twoFAView = this.inject_copyright_and_powered_by(twoFAView);

    this._render_list['../../release/views/' + portal.name + '/Account_verification.eta'] = twoFAView;

    return [twoFAControllerTemplate, ''];
  };

  this.setup_profile = function (portal, role) {
    const model = this._models.find((model) => {
      return model.name === portal.model;
    });

    const profileFields = portal.profile_page_fields || [];

    if (!profileFields.length) {
      return ['', ''];
    }

    let joinModel = this._models.find((model) => {
      return model.name === portal.join;
    });
    let joinStmnt = joinModel
      ? model.join.find((stmnt) => {
          return stmnt.name == portal.join;
        })
      : null;
    let joinType = joinStmnt ? joinStmnt.type : null;

    const { getFieldType, getInputValidations } = validation_field_mapper();

    const validation_fields = {};
    const validation_extend_text = {};
    const profile_fields = [];

    const getProfileFields = profileFields;
    const fields = model.field;

    let drop_down_generators = '';
    let drop_down_generators_holders = '';
    let drop_down_generators_initialize = '';

    const join_table_fields_reference = [];

    let foreignTable;

    getProfileFields.forEach((profileField) => {
      const splitted = profileField.split('~');
      const profile_field = splitted[0];

      let field = fields.find((item) => item[0] === profile_field.split('|')[0]); // This split is required to be compatible with autocomplete as well
      if (splitted.length > 1) {
        const _foreignTable = splitted[1].split('.');
        const foreignTableName = _foreignTable[0];
        const foreignTableFiled = _foreignTable[1];
        // join_table_fields_reference.push(foreignTableFiled);
        join_table_fields_reference.push({ field: splitted[1] });
        foreignTable = this._config.models.find((_model) => _model.name === foreignTableName);
        field = foreignTable.field.find((item) => item[0] === foreignTableFiled);
      }
      if (field.length) {
        const fieldName = field[0];
        const readableFieldName = this.ucFirst(this.toCamelCaseString(fieldName));

        const profile_validation = field[5];
        const strip_validation = this.stripPipe(profile_validation);

        const findProfileField = getProfileFields.find((item) => {
          let actualField = this.stripPipe(item)[0];
          const splitted = actualField.split('~');
          if (splitted.length > 1) {
            actualField = splitted[0];
          }
          return actualField === field[0];
        });

        if (getFieldType(field[1], findProfileField).includes('drop_down')) {
          const actualType = getFieldType(field[1], findProfileField);
          const name = field[0];

          const dropdown_model_name = actualType.substring(actualType.indexOf(':') + 1, actualType.indexOf(':('));

          const code = `
              this.set_dropdown_${name} = async () => {
                this.dropdown_${name} = await db.${dropdown_model_name}.findAll({raw: true})
              }
            `;

          drop_down_generators_holders += `\n this.dropdown_${name} = []; \n`;
          drop_down_generators += `\n ${code} \n`;
          drop_down_generators_initialize += `\n await viewModel.set_dropdown_${name}() \n`;
        }

        profile_fields.push({
          foreignField: splitted.length > 1 ? splitted[1] : false,
          joinType: joinType,
          name: field[0],
          type: this.stripPipe(getFieldType(field[1], findProfileField))[0],
          actualType: getFieldType(field[1], findProfileField), // FE form validation from edit fields
          label: field[3],
          value: '',
          validation: field[5],
        });

        // Prepares an array of name, type, actualType and label for fields
        if (Array.isArray(getProfileFields) && getProfileFields.includes(fieldName)) {
          strip_validation.forEach((item) => {
            getInputValidations(item, fieldName, readableFieldName, validation_fields, validation_extend_text);
          });
          // }
        }
      }
    });

    let profileControllerAPITemplate = '';

    profileControllerTemplate = fs.readFileSync('../mkdcore/source/portal/controller_profile.js', {
      encoding: 'utf-8',
    });

    profileControllerTemplate = this.inject_substitute(profileControllerTemplate, 'portal', portal.name);
    profileControllerTemplate = this.inject_substitute(
      profileControllerTemplate,
      'get_resource_method',
      !foreignTable ? `db.${portal.model}.getByPK(id);` : `db.${model.name}.get_${model.name}_${foreignTable.name}(id, db);`,
    );

    if (foreignTable) {
      profileControllerTemplate = this.inject_substitute(
        profileControllerTemplate,
        'join_table_fields',
        join_table_fields_reference
          .map((item) => {
            // if (item.field.split('.')[1] !== 'password') {
            if (joinType == 'N:1') {
              return `if (field === '${item.field}') {                      
                      viewModel.form_fields[field] = values['${joinStmnt.as}'].map((item) => item.${item.field.split('.')[1]});
                      return;
                    }`;
            } else {
              return `if (field === '${item.field}') {
                      viewModel.form_fields[field] = values["${joinStmnt.as}"]["${item.field.split('.')[1]}"];
                      return;
                    }`;
            }
            // }
          })
          .join('\t\n\t'),
      );
    } else {
      profileControllerTemplate = this.inject_substitute(profileControllerTemplate, 'join_table_fields', '');
    }

    profileControllerTemplate = this.inject_substitute(profileControllerTemplate, 'joined_table', joinModel ? `viewModel.${joinModel.name} = db.${joinModel.name}` : '');

    if (this._config.packages.permission.active) {
      profileControllerTemplate = this.inject_substitute(profileControllerTemplate, 'permission', `PermissionService.verifyPermission(role, "${portal.name}"),`);
    } else {
      profileControllerTemplate = this.inject_substitute(profileControllerTemplate, 'permission', '');
    }

    // Generate the form fields
    let mappings = model.mapping;
    let foreignTableMappings = {};
    if (joinModel) {
      var key,
        keys = Object.keys(joinModel.mapping);
      var n = keys.length;
      while (n--) {
        key = keys[n];
        foreignTableMappings[`${joinModel.name}.${key}`] = joinModel.mapping[key];
      }
    }
    mappings = { ...mappings, ...foreignTableMappings };

    const form = inputFieldMapper(profile_fields, mappings, {
      tableName: portal.model,
    });

    let profile_view = fs.readFileSync('../mkdcore/source/views/custom/Profile.eta', 'utf-8');

    profile_view = this.inject_substitute(
      profile_view,
      'formContent',
      `<form action="/${portal.name}/profile" method="POST">
        ${form}
         <div class="form-group pl-3">
            <button type="submit" class="btn btn-primary">xyzSubmit</button>
           </div>
         </form>
        `,
    );

    const credentialFields = [];
    const userFields = [];

    portal.profile_page_fields.forEach((field) => {
      const splitted = field.split('~');
      if (splitted.length > 1) {
        credentialFields.push(`'${splitted[0]}': ${splitted[1].replace(/\./g, '_')}`);
      } else {
        userFields.push(splitted[0]);
      }
    });

    const controllerProfileFields = portal.profile_page_fields.map((field) => {
      const splitted = field.split('~');
      if (splitted.length > 1) {
        return splitted[1];
      } else {
        return splitted[0];
      }
    });

    const acceptedFields = controllerProfileFields
      .map((item) => {
        if (item.includes('.')) {
          return item.replace(/\./g, '_');
        } else {
          return item;
        }
      })
      .join(',');
    const vmFormFields = controllerProfileFields
      .map((item) => {
        if (item.includes('.')) {
          return `'${item}': ${item.replace(/\./g, '_')}`;
        } else {
          return item;
        }
      })
      .join(',');
    profileControllerTemplate = this.inject_substitute(profileControllerTemplate, 'acceptedFields', acceptedFields);
    profileControllerTemplate = this.inject_substitute(profileControllerTemplate, 'vm_form_fields', vmFormFields);

    profileControllerTemplate = this.inject_substitute(profileControllerTemplate, 'credentialFields', credentialFields.join(','));

    profileControllerTemplate = this.inject_substitute(profileControllerTemplate, 'userFields', userFields.join(','));
    profileControllerTemplate = this.inject_substitute(profileControllerTemplate, 'credentialRef', credentialFields.map((item) => `${item}:CredentialRef.${item}`).join(',\t\n'));

    profile_view = this.inject_substitute(profile_view, 'role', portal.role);

    this._render_list['../../release/views/' + portal.name + '/Profile.eta'] = profile_view;

    // if (portal.api) {
    //   // Load controller template
    //   api_edit_template = fs.readFileSync('../mkdcore/source/portal/api_controller_edit.js', {
    //     encoding: 'utf8',
    //   });
    //   // Add autocomplete
    //   api_edit_template = this.inject_substitute(api_edit_template, 'autocomplete', !controller.view ? autocompleteAPIController : '');

    //   api_edit_template = this.inject_substitute(api_edit_template, 'portal', controller.portal);

    //   api_edit_template = this.inject_substitute(api_edit_template, 'route', controllerRoute + '-edit');

    //   // If Validation fields are empty do not include the validation middleware
    //   api_edit_template = this.inject_substitute(
    //     api_edit_template,
    //     'additional_put_middlewares',
    //     Object.keys(validation_fields).length > 0
    //       ? `ValidationService.validateInput(
    //         ${JSON.stringify(validation_fields)},
    //         ${JSON.stringify(validation_extend_text)}
    //       ),`
    //       : '',
    //   );

    //   api_edit_template = this.inject_substitute(api_edit_template, 'view_model_name', viewModelName);
    //   api_edit_template = this.inject_substitute(api_edit_template, 'cc_view_model_name', this.toCamelCaseString(viewModelName));
    //   api_edit_template = this.inject_substitute(api_edit_template, 'view_model_arguments', `db.${controller.model}`);

    //   api_edit_template = this.inject_substitute(api_edit_template, 'acceptedFields', acceptedFields);
    //   api_edit_template = this.inject_substitute(api_edit_template, 'ucModelName', ucModelName);
    //   api_edit_template = this.inject_substitute(api_edit_template, 'method_edit_pre', controller.method_edit_pre.join('\n\n'));
    //   api_edit_template = this.inject_substitute(api_edit_template, 'method_edit', controller.method_edit.join('\n\n'));
    //   api_edit_template = this.inject_substitute(api_edit_template, 'method_edit_success', controller.method_edit_success.join('\n\n'));
    // }

    let profile_template = fs.readFileSync('../mkdcore/source/portal/controller_profile_template.js', 'utf-8');

    profile_template = this.inject_substitute(profile_template, 'profile', profileControllerTemplate);

    profile_template = this.inject_substitute(profile_template, 'role_id', role.id);

    profile_template = this.inject_substitute(profile_template, 'api_profile', portal.api ? profileControllerAPITemplate : '');

    this._render_list['../../release/routes/' + portal.name + '/profile.js'] = profile_template;
  };
};
