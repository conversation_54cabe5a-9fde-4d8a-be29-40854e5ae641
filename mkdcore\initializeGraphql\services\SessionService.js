/*Powered By: Manaknightdigital Inc. https://manaknightdigital.com/ Year: 2020*/
/**
 * Session Service
 * @copyright 2020 Manaknightdigital Inc.
 * @link https://manaknightdigital.com
 * @license Proprietary Software licensing
 * <AUTHOR>
 *
 */
module.exports = {
  verifySessionMiddleware: function (role) {
    return function (req, res, next) {
      const currentRole = req.session.role ? req.session.role : 0;
      if (currentRole === 0) {
        res.redirect("/");
      } else if (currentRole != role) {
        // console.log('not correct role');
        res.redirect("/");
      } else {
        next();
      }
    };
  },
  randomString: function (len) {
    const charSet =
      'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789~!@#$%^&*()_+{}|":?><,./;[]';
    let randomString = "";
    for (let i = 0; i < len; i++) {
      let randomPoz = Math.floor(Math.random() * charSet.length);
      randomString += charSet.substring(randomPoz, randomPoz + 1);
    }
    return randomString;
  },
};
