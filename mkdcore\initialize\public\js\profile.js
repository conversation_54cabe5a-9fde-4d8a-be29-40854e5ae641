var $$ = Dom7;

async function getProfile() {
  try {
    app.preloader.show();
    let response = await fetch('/api/user/profile/', {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
    });
    console.log(response.status);
    let resJSON = await response.json();
    
    if (response.status == 200) {
      

      $$('#profile-namecontainer-text').text(resJSON.user.first_name + ' ' + resJSON.user.last_name);
      document.getElementById('profile-email').value = resJSON.user.credential.email;
      document.getElementById('profile-fname').value = resJSON.user.first_name;
      document.getElementById('profile-lname').value = resJSON.user.last_name;
      document.getElementById('profile-phone').value = resJSON.user.phone.substring(2);
      
      app.preloader.hide();
    } else {
      app.preloader.hide();
      throw "Couldn't fetch Co-Profile";
    }
  } catch (error) {
    app.preloader.hide();
    app.dialog.alert.res.messagealert(error);
  }
}

async function updateProfile() {
  try {
    app.preloader.show();
    let email = document.getElementById('profile-email').value;
    let firstName = document.getElementById('profile-fname').value;
    let lastName = document.getElementById('profile-lname').value;
    let password = document.getElementById('profile-password').value;
    let phoneIn = '+1' + document.getElementById('profile-phone').value;
    let phone = phoneIn.replace(/\s+/g, '');

    if ($$('#profile-phone').hasClass('input-invalid') || $$('#profile-email').hasClass('input-invalid')) {
      app.preloader.hide();
      throw 'Please enter valid Co-Profile information';
    }

    if (validateProfileFields(email, firstName, lastName, phone)) {
      let body = {
        email: email,
        first_name: firstName,
        last_name: lastName,
        password: password,
        phone: phone,
      };

      let response = await fetch('/api/user/profile/', {
        method: 'POST',
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
      });

      if (response.status == 200) {
        app.preloader.hide();
        app.dialog.alert('Profile updated');
        // console.log("**");
        getProfile();
      } else {
        app.preloader.hide();
        throw "Couldn't update Profile";
      }
    }
  } catch (error) {
    app.preloader.hide();
    app.dialog.alert(error);
  }
}

function validateProfileFields(email, firstName, lastName, phone) {
  var AllFieldsValidated = true;
  var errortoThrow = 'These field(s) are required: <br>';
  if (email == '') {
    AllFieldsValidated = false;
    errortoThrow += 'Email <br>';
  }
  if (firstName == '') {
    AllFieldsValidated = false;
    errortoThrow += 'First Name <br>';
  }
  if (lastName == '') {
    AllFieldsValidated = false;
    errortoThrow += 'Last Name <br>';
  }
  if (phone == '') {
    AllFieldsValidated = false;
    errortoThrow += 'Phone <br>';
  }
  if (AllFieldsValidated == false) {
    throw errortoThrow;
  }
  return AllFieldsValidated;
}

async function handleForgot(e) {
  e.preventDefault();
  try {
    app.preloader.show();
    const email = document.getElementById('forgot-email').value;
    if (!email) return;
    console.log(email);
    let res = await fetch('/api/user/forgot', {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email }),
    });
    res = await res.json();
    app.preloader.hide();
    console.log(res);
    if (res.status === 'success') app.view.main.router.navigate({ name: 'verify' });
    if (res.error) app.dialog.alert(res.message);
  } catch (error) {
    app.preloader.hide();
    console.log(error);
  }
}
async function handleVerification(e) {
  e.preventDefault();
  try {
    app.preloader.show();
    const code = document.getElementById('forgot-code').value;
    if (!code) return;
    let res = await fetch('/api/user/verify', {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ code }),
    });
    localStorage.setItem('verification_code', code);
    res = await res.json();
    app.preloader.hide();
    if (res.status === 'success') app.view.main.router.navigate({ name: 'reset' });

    if (res.error) app.dialog.alert('Invalid verification code');
  } catch (error) {
    app.preloader.hide();
    console.log(error);
  }
}
async function handleReset(e) {
  e.preventDefault();
  try {
    const code = localStorage.getItem('verification_code');
    const password = document.getElementById('forgot-password').value;
    const password_confirm = document.getElementById('forgot-confirm-password').value;
    if (password !== password_confirm) return app.dialog.alert("Passwords don't match");
    app.preloader.show();
    let res = await fetch('/api/user/reset', {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ password, code }),
    });
    if (res.ok) {
      app.preloader.hide();
      app.dialog.alert('Password updated');
      return app.view.main.router.navigate('/');
    }
    res = await res.json();
    app.preloader.hide();
    console.log(res);
  } catch (error) {
    app.preloader.hide();
    console.log(error);
  }
}
