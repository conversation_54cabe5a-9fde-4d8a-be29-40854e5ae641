<!DOCTYPE html>
<html>

<head>
    <!-- Required meta tags-->
    <meta charset="utf-8">
    <meta name="viewport"
        content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no, viewport-fit=cover">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <!-- Color theme for statusbar (Android only) -->
    <meta name="theme-color" content="#2196f3">
    <!-- Your app title -->
    <title>Co-Parent</title>
</head>

<body>
    <!-- App root element -->
    <div id="app">
        <div class="page" data-name="add-files">

            <!-- Top Navbar -->
            <div class="navbar">
                <div class="navbar-bg"></div>
                <div class="navbar-inner">
                    <div class="left">
                        <a class="link back">
                            <i class="icon icon-back color-black"></i>
                            <span class="if-not-md">Back</span>
                        </a>
                    </div>
                    <div class="title text-align-center">
                        Add Co-Files
                    </div>
                    <div class="right">
                        <button onclick="uploadFile()" class="form-btn"><i
                                class="f7-icons color-black">checkmark_alt</i></button>
                    </div>
                </div>
            </div>

            <!-- Scrollable page content -->
            <div class="page-content no-swipe-panel">
                <div class="card">
                    <div class="card-content padding-vertical">
                        <div class="list no-hairlines">
                            <form id="addfiles-forms">
                                <ul>
                                    <li class="smart-select-list">
                                        <div class="item-title item-label smart-select-label">Choose Co-Hort </div>
                                        <a class="item-link smart-select smart-select-init" data-open-in="sheet"
                                            data-close-on-select="true">
                                            <select name="selected-cohort" onchange="getCohortFiles()"
                                                id="files-cohort-drop-add" required validate
                                                data-validate-on-blur="true">
                                                <!-- This is Dummy Content, Data is populaed through Javascript-->

                                            </select>
                                            <div
                                                class="item-content expensesChoseCohortSelect addFilesChoseCohortSelect">
                                                <div class="item-inner">
                                                    <div class="item-title">Choose Co-Hort</div>
                                                </div>
                                            </div>
                                        </a>
                                    </li>

                                    <li class="item-content item-input item-input-outline">
                                        <div class="item-inner">
                                            <div class="item-title item-label">Label</div>
                                            <div class="item-input-wrap">
                                              <input id="file-label" type="text" />
                                              <span class="input-clear-button"></span>
                                            </div>
                                          </div>
                                    </li>
                                    <li class="item-content item-input item-input-outline">
                                        <div class="item-inner">
                                            <div class="item-title item-label">Notes</div>
                                            <div class="item-input-wrap">
                                                <textarea id="file-notes"></textarea>
                                                <span class="input-clear-button"></span>
                                            </div>
                                        </div>
                                    </li>

                                    <li class="item-content item-input item-input-outline">
                                        <div class="item-inner">
                                            <div class="item-title item-label">Upload File</div>
                                            <div class="item-input-wrap addExpensesUpload">
                                                <input id="addfiles-upload-files-button" type="file" name="reciepts" />
                                            </div>
                                        </div>
                                    </li>

                                </ul>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

        </div>
</body>

</html>