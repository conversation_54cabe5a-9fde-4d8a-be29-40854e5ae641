<!DOCTYPE html>
<html>
  <head>
    <!-- Required meta tags-->
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no, viewport-fit=cover" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <!-- Color theme for statusbar (Android only) -->
    <meta name="theme-color" content="#2196f4" />
    <!-- Your app title -->
    <title>Co-Parent</title>
    <!-- Path to your custom app styles-->
    <!-- <link rel="stylesheet" href="path/to/my-app.css"> -->
  </head>

  <body>
    <!-- App root element -->
    <div id="app">
      <div class="page" data-name="viewcohort">
        <!-- Top Navbar -->
        <div class="navbar">
          <div class="navbar-bg"></div>
          <div class="navbar-inner">
            <div class="left">
              <a href="#" class="panel-toggle color-black">&#9776;</a>
            </div>
            <div class="title text-align-center">View Co-Hort</div>
            <div class="right">
              <a href="/newOrJoin/"><i class="f7-icons color-black">plus_circle</i></a>
            </div>
          </div>
        </div>

        <!-- Scrollable page content -->
        <div class="page-content">
          <div class="view-cohart-container" id="view-cohart-container">
            <!--This is a skeleton, It is not being rendered. Rendering is done through Javascript!-->
          </div>
        </div>
      </div>
    </div>
  </body>
</html>
