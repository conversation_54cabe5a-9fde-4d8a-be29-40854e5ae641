'use strict';

const app = require('express').Router();
const helpers = require('../../core/helpers');
const { protect } = require('../../middlewares/auth_middleware');
const db = require('../../models');
const { Op } = require('sequelize');

app.post('/api/settlement/add', protect, async (req, res, next) => {
  try {
    const { user } = req;
    const { cohort_id, amount, payment_method, date } = req.body;
    const belongToCohort = await db.cohort.findOne({
      where: {
        [Op.or]: {
          parent_1: user.id,
          parent_2: user.id,
        },
        id: cohort_id,
      },
    });
    if (!belongToCohort) {
      return res.status(404).json({
        status: 'fail',
        message: 'Cohort not found',
      });
    }
    const parent_receive = belongToCohort.parent_1 === user.id ? belongToCohort.parent_2 : belongToCohort.parent_1;
    const settle = await db.settle_amount.insert({ parent_send: req.user.id, parent_receive, amount, payment_method, cohort_id, date });
    // expenses.forEach(async (exp) => {
    //   await db.expense.edit({ settlement: settle, status: 0 }, exp);
    // });
    return res.status(200).json({
      status: 'success',
      message: 'settlement created',
      settle,
    });
  } catch (error) {
    console.log(error);
    return res.status(400).json({
      error: true,
      message: error.message,
    });
  }
});

app.get('/api/settlement/:cohort_id', protect, async function (req, res, next) {
  try {
    const { user } = req;
    const { cohort_id } = req.params;
    const belongToCohort = await db.cohort.findOne({
      where: {
        [Op.or]: {
          parent_1: user.id,
          parent_2: user.id,
        },
        id: cohort_id,
      },
    });
    if (!belongToCohort) {
      return res.status(404).json({
        status: 'fail',
        message: 'Cohort not found',
      });
    }

    const settlements = await db.settle_amount.findAll({
      where: { cohort_id },
      include: [
        {
          model: db.user,
          as: 'parentSend',
        },
        {
          model: db.user,
          as: 'parentReceive',
        },
      ],
    });
    res.status(200).json({
      status: 'status',
      settlements,
    });
  } catch (error) {
    console.log(error);
    return res.status(400).json({
      error: true,
      message: error.message,
    });
  }
});

module.exports = app;
