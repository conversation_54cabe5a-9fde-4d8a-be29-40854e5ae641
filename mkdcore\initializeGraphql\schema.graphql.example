type Task {
  id: Int!
  title: String
  userTestId: Int
  userTest: UserTest
}

type UserTest {
  id: Int!
  username: String
}

type Book {
  title: String
  author: String
}

type Mission {
  name: String
  missionPatch(size: PatchSize): String
}

type TaskUpdateResponse {
  success: Boolean!
  message: String
  task: [Task]
}
type Query {
  "A simple hello to start it"
  hello: String
  bad: Int
  tasks: [Task]
  task(id: ID!): Task
  me: UserTest
}

type Mutation {
  bookTrips(launchIds: [ID]!): TaskUpdateResponse!
  cancelTrip(launchId: ID!): TaskUpdateResponse!
  login(email: String): String # login token
}

enum PatchSize {
  SMALL
  LARGE
}