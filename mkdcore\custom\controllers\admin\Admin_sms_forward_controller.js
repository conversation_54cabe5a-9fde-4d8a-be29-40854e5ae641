'use strict';

const app = require('express').Router();
const Sequelize = require('sequelize');
const logger = require('../../services/LoggingService');
let pagination = require('../../services/PaginationService');
let SessionService = require('../../services/SessionService');
let JwtService = require('../../services/JwtService');
const ValidationService = require('../../services/ValidationService');
const PermissionService = require('../../services/PermissionService');
const UploadService = require('../../services/UploadService');
const AuthService = require('../../services/AuthService');
const db = require('../../models');
const helpers = require('../../core/helpers');

const role = 1;

app.get('/admin/sms_forwards/:num', SessionService.verifySessionMiddleware(role, 'admin'), async function (req, res, next) {
  try {
    let session = req.session;
    let paginateListViewModel = require('../../view_models/sms_forwards_admin_list_paginate_view_model');

    var viewModel = new paginateListViewModel(db.sms_forward, 'SMS Forwards', session.success, session.error, '/admin/sms_forwards');

    const format = req.query.format ? req.query.format : 'view';
    const direction = req.query.direction ? req.query.direction : 'ASC';
    const per_page = req.query.per_page ? req.query.per_page : 10;
    let order_by = req.query.order_by ? req.query.order_by : viewModel.get_field_column()[0];
    let orderAssociations = [];
    viewModel.set_order_by(order_by);
    let joins = order_by.includes('.') ? order_by.split('.') : [];
    order_by = order_by.includes('.') ? joins[joins.length - 1] : order_by;
    if (joins.length > 0) {
      for (let i = joins.length - 1; i > 0; i--) {
        orderAssociations.push(`${joins[i - 1]}`);
      }
    }
    // Check for flash messages
    const flashMessageSuccess = req.flash('success');
    if (flashMessageSuccess && flashMessageSuccess.length > 0) {
      viewModel.success = flashMessageSuccess[0];
    }
    const flashMessageError = req.flash('error');
    if (flashMessageError && flashMessageError.length > 0) {
      viewModel.error = flashMessageError[0];
    }

    viewModel.set_id(req.query.id ? req.query.id : '');
    viewModel.set_cohort_id(req.query.cohort_id ? req.query.cohort_id : '');
    viewModel.set_from(req.query.from ? req.query.from : '');
    viewModel.set_to(req.query.to ? req.query.to : '');
    viewModel.set_date(req.query.date ? req.query.date : '');
    viewModel.set_status(req.query.status ? req.query.status : '');

    let where = helpers.filterEmptyFields({
      id: viewModel.get_id(),
      cohort_id: viewModel.get_cohort_id(),
      from: viewModel.get_from(),
      to: viewModel.get_to(),
      date: viewModel.get_date(),
      status: viewModel.get_status(),
    });

    const count = await db.sms_forward._count(where, []);

    let sort_url = '';

    if (req.originalUrl.includes('?')) {
      if (req.originalUrl.includes('order_by')) {
        let url_query = req.originalUrl.split('?')[1];
        sort_url = `${url_query.split('order_by')[0]}`;
      } else {
        sort_url = `${req.originalUrl.split('?')[1]}`;
      }
    }
    viewModel.set_total_rows(count);
    viewModel.set_per_page(+per_page);
    viewModel.set_page(+req.params.num);
    viewModel.set_query(req.query);
    viewModel.set_sort_base_url(`/admin/sms_forwards/${+req.params.num}?${sort_url}`);
    viewModel.set_sort(direction);

    const list = await db.sms_forward.getPaginated(
      viewModel.get_page() - 1 < 0 ? 0 : viewModel.get_page(),
      viewModel.get_per_page(),
      where,
      order_by,
      direction,
      orderAssociations,
    );

    viewModel.set_list(list);

    if (format == 'csv') {
      const csv = viewModel.to_csv();
      return res
        .set({
          'Content-Type': 'text/csv',
          'Content-Disposition': 'attachment; filename="export.csv"',
        })
        .send(csv);
    }

    // if (format != 'view') {
    //   res.json(viewModel.to_json());
    // } else {
    // }

    return res.render('admin/Sms_forwards', viewModel);
  } catch (error) {
    console.error(error);
    viewModel.error = error.message || 'Something went wrong';
    return res.render('admin/Sms_forwards', viewModel);
  }
});

app.get('/admin/sms_forwards-add', SessionService.verifySessionMiddleware(role, 'admin'), async function (req, res, next) {
  if (req.session.csrf === undefined) {
    req.session.csrf = SessionService.randomString(100);
  }

  const smsForwardsAdminAddViewModel = require('../../view_models/sms_forwards_admin_add_view_model');

  const viewModel = new smsForwardsAdminAddViewModel(db.sms_forward, 'Add sms forward', '', '', '/admin/sms_forwards');

  res.render('admin/Add_Sms_forwards', viewModel);
});

app.post(
  '/admin/sms_forwards-add',
  SessionService.verifySessionMiddleware(role, 'admin'),
  ValidationService.validateInput(
    { cohort_id: 'required', date: 'required', from: 'required', to: 'required', status: 'required' },
    {
      'cohort_id.required': 'CohortId is required',
      'date.required': 'Date is required',
      'from.required': 'From is required',
      'to.required': 'To is required',
      'status.required': 'Status is required',
    },
  ),
  async function (req, res, next) {
    if (req.session.csrf === undefined) {
      req.session.csrf = SessionService.randomString(100);
    }
    const smsForwardsAdminAddViewModel = require('../../view_models/sms_forwards_admin_add_view_model');

    const viewModel = new smsForwardsAdminAddViewModel(db.sms_forward, 'Add sms forward', '', '', '/admin/sms_forwards');

    // TODO use separate controller for image upload
    //  {{{upload_field_setter}}}

    const { cohort_id, date, from, to, status } = req.body;

    viewModel.form_fields = {
      ...viewModel.form_fields,
      cohort_id,
      date,
      from,
      to,
      status,
    };

    try {
      if (req.validationError) {
        viewModel.error = req.validationError;
        return res.render('admin/Add_Sms_forwards', viewModel);
      }

      viewModel.session = req.session;

      const data = await db.sms_forward.insert({ cohort_id, date, from, to, status });
      const notification = await db.notification_log.insert({ to, status: 0, name: `SMS Forward`, description: `SMS Forward in Cohort ${cohort_id}` });

      if (!data || !notification) {
        viewModel.error = 'Something went wrong';
        return res.render('admin/Add_Sms_forwards', viewModel);
      }

      await db.activity_log.insert({
        action: 'ADD',
        name: 'Admin_sms_forward_controller.js',
        portal: 'admin',
        data: JSON.stringify({ cohort_id, date, from, to, status }),
      });

      req.flash('success', 'Sms forward created successfully');
      return res.redirect('/admin/sms_forwards/0');
    } catch (error) {
      console.error(error);
      viewModel.error = error.message || 'Something went wrong';
      return res.render('admin/Add_Sms_forwards', viewModel);
    }
  },
);

app.get('/admin/sms_forwards-edit/:id', SessionService.verifySessionMiddleware(role, 'admin'), async function (req, res, next) {
  let id = req.params.id;
  if (req.session.csrf === undefined) {
    req.session.csrf = SessionService.randomString(100);
  }
  const smsForwardsAdminEditViewModel = require('../../view_models/sms_forwards_admin_edit_view_model');

  const viewModel = new smsForwardsAdminEditViewModel(db.sms_forward, 'Edit sms forward', '', '', '/admin/sms_forwards');

  try {
    const exists = await db.sms_forward.getByPK(id);

    if (!exists) {
      req.flash('error', 'Sms forward not found');
      return res.redirect('/admin/sms_forwards/0');
    }
    const values = exists;
    Object.keys(viewModel.form_fields).forEach((field) => {
      viewModel.form_fields[field] = values[field] || '';
    });

    return res.render('admin/Edit_Sms_forwards', viewModel);
  } catch (error) {
    console.error(error);
    viewModel.error = error.message || 'Something went wrong';
    return res.render('admin/Edit_Sms_forwards', viewModel);
  }
});

app.post(
  '/admin/sms_forwards-edit/:id',
  SessionService.verifySessionMiddleware(role, 'admin'),
  ValidationService.validateInput(
    { cohort_id: 'required', date: 'required', from: 'required', to: 'required', status: 'required' },
    {
      'cohort_id.required': 'CohortId is required',
      'date.required': 'Date is required',
      'from.required': 'From is required',
      'to.required': 'To is required',
      'status.required': 'Status is required',
    },
  ),
  async function (req, res, next) {
    let id = req.params.id;
    if (req.session.csrf === undefined) {
      req.session.csrf = SessionService.randomString(100);
    }

    const smsForwardsAdminEditViewModel = require('../../view_models/sms_forwards_admin_edit_view_model');

    const viewModel = new smsForwardsAdminEditViewModel(db.sms_forward, 'Edit sms forward', '', '', '/admin/sms_forwards');

    const { cohort_id, date, from, to, status } = req.body;

    viewModel.form_fields = {
      ...viewModel.form_fields,
      cohort_id,
      date,
      from,
      to,
      status,
    };

    delete viewModel.form_fields.id;

    try {
      if (req.validationError) {
        viewModel.error = req.validationError;
        return res.render('admin/Edit_Sms_forwards', viewModel);
      }

      const resourceExists = await db.sms_forward.getByPK(id);
      if (!resourceExists) {
        req.flash('error', 'Sms forward not found');
        return res.redirect('/admin/sms_forwards/0');
      }

      viewModel.session = req.session;

      let data = await db.sms_forward.edit({ cohort_id, date, from, to, status }, id);
      if (!data) {
        viewModel.error = 'Something went wrong';
        return res.render('admin/Edit_Sms_forwards', viewModel);
      }

      await db.activity_log.insert({
        action: 'EDIT',
        name: 'Admin_sms_forward_controller.js',
        portal: 'admin',
        data: JSON.stringify({ cohort_id, date, from, to, status }),
      });

      req.flash('success', 'Sms forward edited successfully');

      return res.redirect('/admin/sms_forwards/0');
    } catch (error) {
      console.error(error);
      viewModel.error = error.message || 'Something went wrong';
      return res.render('admin/Edit_Sms_forwards', viewModel);
    }
  },
);

app.get(
  '/admin/sms_forwards-view/:id',
  SessionService.verifySessionMiddleware(role, 'admin'),

  async function (req, res, next) {
    try {
      let id = req.params.id;

      const smsForwardsAdminDetailViewModel = require('../../view_models/sms_forwards_admin_detail_view_model');

      var viewModel = new smsForwardsAdminDetailViewModel(db.sms_forward, 'Sms forward details', '', '', '/admin/sms_forwards');

      const data = await db.sms_forward.getByPK(id);
      data.status = db.sms_forward.status_mapping()[data.status];

      if (!data) {
        viewModel.error = 'Sms forward not found';
        viewModel.detail_fields = { ...viewModel.detail_fields, id: 'N/A', cohort_id: 'N/A', date: 'N/A', from: 'N/A', to: 'N/A', status: 'N/A' };
      } else {
        viewModel.detail_fields = {
          ...viewModel.detail_fields,
          id: data['id'] || 'N/A',
          cohort_id: data['cohort_id'] || 'N/A',
          date: data['date'] || 'N/A',
          from: data['from'] || 'N/A',
          to: data['to'] || 'N/A',
          status: data['status'] || 'N/A',
        };
      }

      res.render('admin/View_Sms_forwards', viewModel);
    } catch (error) {
      console.error(error);
      viewModel.error = error.message || 'Something went wrong';
      viewModel.detail_fields = { ...viewModel.detail_fields, id: 'N/A', cohort_id: 'N/A', date: 'N/A', from: 'N/A', to: 'N/A', status: 'N/A' };
      res.render('admin/View_Sms_forwards', viewModel);
    }
  },
);

app.get('/admin/sms_forwards-delete/:id', SessionService.verifySessionMiddleware(role, 'admin'), async function (req, res, next) {
  let id = req.params.id;

  const smsForwardsAdminDeleteViewModel = require('../../view_models/sms_forwards_admin_delete_view_model');

  const viewModel = new smsForwardsAdminDeleteViewModel(db.sms_forward);

  try {
    const exists = await db.sms_forward.getByPK(id);

    if (!exists) {
      req.flash('error', 'Sms forward not found');
      return res.redirect('/admin/sms_forwards/0');
    }

    viewModel.session = req.session;

    await db.sms_forward.realDelete(id);

    await db.activity_log.insert({
      action: 'DELETE',
      name: 'Admin_sms_forward_controller.js',
      portal: 'admin',
      data: JSON.stringify(exists),
    });

    req.flash('success', 'Sms forward deleted successfully');

    return res.redirect('/admin/sms_forwards/0');
  } catch (error) {
    console.error(error);
    req.flash('error', error.message || 'Something went wrong');
    return res.redirect('/admin/sms_forwards/0');
  }
});

// APIS

app.get('/admin/api/sms_forwards', JwtService.verifyTokenMiddleware(role), async function (req, res, next) {
  try {
    const user_id = req.user_id;
    const session = req.session;
    let listViewModel = require('../../view_models/sms_forwards_admin_list_paginate_view_model');
    let viewModel = new listViewModel(db.sms_forward, 'SMS Forwards', session.success, session.error, '/admin/sms_forwards');
    const direction = req.query.direction ? req.query.direction : 'ASC';
    const page = req.query.page ? parseInt(req.query.page) : 1;
    const limit = req.query.limit ? parseInt(req.query.limit) : 10;
    const offset = (page - 1) * limit;
    let order_by = req.query.order_by ? req.query.order_by : viewModel.get_field_column()[0];
    let orderAssociations = [];
    viewModel.set_order_by(order_by);
    let joins = order_by.includes('.') ? order_by.split('.') : [];
    order_by = order_by.includes('.') ? joins[joins.length - 1] : order_by;
    if (joins.length > 0) {
      for (let i = joins.length - 1; i > 0; i--) {
        orderAssociations.push(`${joins[i - 1]}`);
      }
    }

    viewModel.set_id(req.query.id ? req.query.id : '');
    viewModel.set_cohort_id(req.query.cohort_id ? req.query.cohort_id : '');
    viewModel.set_from(req.query.from ? req.query.from : '');
    viewModel.set_to(req.query.to ? req.query.to : '');
    viewModel.set_date(req.query.date ? req.query.date : '');
    viewModel.set_status(req.query.status ? req.query.status : '');

    let where = helpers.filterEmptyFields({
      id: viewModel.get_id(),
      cohort_id: viewModel.get_cohort_id(),
      from: viewModel.get_from(),
      to: viewModel.get_to(),
      date: viewModel.get_date(),
      status: viewModel.get_status(),
    });

    let include = [];

    const { rows: allItems, count } = await db.sms_forward.findAndCountAll({
      where: where,
      limit: limit == 0 ? null : limit,
      offset: offset,
      include: include,
      distinct: true,
    });

    const response = {
      items: allItems,
      page,
      nextPage: count > offset + limit ? page + 1 : false,
      retrievedCount: allItems.length,
      fullCount: count,
    };

    return res.status(201).json({ success: true, data: response });
  } catch (error) {
    console.error(error);
    return res.status(500).json({ success: false, message: error.message || 'Something went wrong' });
  }
});

app.post(
  '/admin/api/sms_forwards-add',
  JwtService.verifyTokenMiddleware(role),
  ValidationService.validateInput(
    { cohort_id: 'required', date: 'required', from: 'required', to: 'required', status: 'required' },
    {
      'cohort_id.required': 'CohortId is required',
      'date.required': 'Date is required',
      'from.required': 'From is required',
      'to.required': 'To is required',
      'status.required': 'Status is required',
    },
  ),
  async function (req, res, next) {
    const smsForwardsAdminAddViewModel = require('../../view_models/sms_forwards_admin_add_view_model');

    const viewModel = new smsForwardsAdminAddViewModel(db.sms_forward);

    const { cohort_id, date, from, to, status } = req.body;
    try {
      if (req.validationError) {
        return res.status(500).json({ success: false, message: req.validationError });
      }

      const data = await db.sms_forward.insert({ cohort_id, date, from, to, status });

      if (!data) {
        return res.status(500).json({ success: false, message: 'Something went wrong' });
      }

      await db.activity_log.insert({
        action: 'ADD',
        name: 'Admin_sms_forward_controller.js',
        portal: 'admin',
        data: JSON.stringify({ cohort_id, date, from, to, status }),
      });

      return res.status(201).json({ success: true, message: 'Sms forward created successfully' });
    } catch (error) {
      return res.status(500).json({ success: false, message: 'Something went wrong' });
    }
  },
);

app.put(
  '/admin/api/sms_forwards-edit/:id',
  JwtService.verifyTokenMiddleware(role),
  ValidationService.validateInput(
    { cohort_id: 'required', date: 'required', from: 'required', to: 'required', status: 'required' },
    {
      'cohort_id.required': 'CohortId is required',
      'date.required': 'Date is required',
      'from.required': 'From is required',
      'to.required': 'To is required',
      'status.required': 'Status is required',
    },
  ),
  async function (req, res, next) {
    let id = req.params.id;

    const smsForwardsAdminEditViewModel = require('../../view_models/sms_forwards_admin_edit_view_model');

    const viewModel = new smsForwardsAdminEditViewModel(db.sms_forward);

    const { cohort_id, date, from, to, status } = req.body;

    try {
      if (req.validationError) {
        return res.status(500).json({ success: false, message: req.validationError });
      }

      const resourceExists = await db.sms_forward.getByPK(id);
      if (!resourceExists) {
        return res.status(404).json({ success: false, message: 'Sms forward not found' });
      }

      const data = await db.sms_forward.edit({ cohort_id, date, from, to, status }, id);

      if (!data) {
        return res.status(500).json({ success: false, message: 'Something went wrong' });
      }

      await db.activity_log.insert({
        action: 'EDIT',
        name: 'Admin_sms_forward_controller.js',
        portal: 'admin',
        data: JSON.stringify({ cohort_id, date, from, to, status }),
      });

      return res.json({ success: true, message: 'Sms forward edited successfully' });
    } catch (error) {
      return res.status(500).json({ success: false, message: 'Something went wrong' });
    }
  },
);

app.get('/admin/api/sms_forwards-view/:id', JwtService.verifyTokenMiddleware(role), async function (req, res, next) {
  let id = req.params.id;

  const smsForwardsAdminDetailViewModel = require('../../view_models/sms_forwards_admin_detail_view_model');

  const viewModel = new smsForwardsAdminDetailViewModel(db.sms_forward);

  try {
    const data = await db.sms_forward.getByPK(id);

    if (!data) {
      return res.status(404).json({ message: 'Sms forward not found', data: null });
    } else {
      const fields = {
        ...viewModel.detail_fields,
        id: data['id'] || '',
        cohort_id: data['cohort_id'] || '',
        date: data['date'] || '',
        from: data['from'] || '',
        to: data['to'] || '',
        status: data['status'] || '',
      };
      return res.status(200).json({ data: fields });
    }
  } catch (error) {
    return res.status(404).json({ message: 'Something went wrong', data: null });
  }
});

app.delete('/admin/api/sms_forwards-delete/:id', JwtService.verifyTokenMiddleware(role), async function (req, res, next) {
  let id = req.params.id;

  const smsForwardsAdminDeleteViewModel = require('../../view_models/sms_forwards_admin_delete_view_model');

  const viewModel = new smsForwardsAdminDeleteViewModel(db.sms_forward);

  try {
    const exists = await db.sms_forward.getByPK(id);

    if (!exists) {
      return res.status(404).json({ success: false, message: 'Sms forward not found' });
    }

    await db.sms_forward.realDelete(id);

    await db.activity_log.insert({
      action: 'DELETE',
      name: 'Admin_sms_forward_controller.js',
      portal: 'admin',
      data: JSON.stringify(exists),
    });

    return res.status(200).json({ success: true, message: 'Sms forward deleted successfully' });
  } catch (error) {
    return res.status(500).json({ success: false, message: 'Something went wrong' });
  }
});

module.exports = app;
