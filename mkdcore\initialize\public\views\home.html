<!DOCTYPE html>
<html>

<head>
    <!-- Required meta tags-->
    <meta charset="utf-8">
    <meta name="viewport"
        content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no, viewport-fit=cover">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <!-- Color theme for statusbar (Android only) -->
    <meta name="theme-color" content="#2196f3">
    <!-- Your app title -->
    <title>Coparent</title>
    <!-- Path to your custom app styles-->
    <!-- <link rel="stylesheet" href="path/to/my-app.css"> -->
</head>

<body>
    <!-- App root element -->
    <div id="app">
        <div class="page">

            <!-- Top Navbar -->
            <div class="navbar">
                <div class="navbar-bg"></div>
                <div class="navbar-inner">
                    <div class="left">
                        <a href="#" class="panel-toggle"><i class="f7-icons color-black">menu</i></a>
                    </div>
                    <div class="title">
                        Dashboard
                    </div>
                    <div class="right">

                    </div>
                </div>
            </div>

            <!-- Scrollable page content -->
            <div class="page-content">

                <div class="list no-hairlines">

                    <ul>
                        <li>
                            <a class="item-link smart-select smart-select-init" data-open-in="sheet">
                                <select name="mac-windows" id="testing">
                                    <option value="mac">Mac</option>
                                    <option value="windows" selected>Windows</option>
                                </select>
                                <div class="item-content">
                                    <div class="item-inner">
                                        <div class="item-title">Chose Value</div>
                                    </div>
                                </div>
                            </a>
                        </li>

                    </ul>

                </div>


                <div class="list no-hairlines">
                    <ul>
                        <li>
                            <!-- <div class="block-title"> <i
                                    class="f7-icons color-black viewcohort-icon">person_crop_circle_fill</i>
                                Parent One
                            </div> -->
                            <div class="item-content">
                                <div class="item-media">
                                    <i class="f7-icons color-black viewcohort-icon">
                                        person_crop_circle_fill
                                    </i>
                                </div>
                                <div class="item-inner">
                                    <div class="item-title">Parent One</div>
                                </div>
                            </div>
                            <div class="item-content">
                                <div class="item-media">
                                    <i class="f7-icons color-black viewcohort-icon">
                                        device_phone_portrait
                                    </i>
                                </div>
                                <div class="item-inner">
                                    <div class="item-title">+18792314589</div>
                                </div>
                            </div>

                            <div class="item-content">
                                <div class="item-media">
                                    <i class="f7-icons color-black viewcohort-icon">
                                        envelope
                                    </i>
                                </div>
                                <div class="item-inner">
                                    <div class="item-title"><EMAIL></div>
                                </div>
                            </div>
                        </li>
                    </ul>
                </div>

            </div>

        </div>
    </div>
    <!-- Path to Framework7 Library Bundle JS-->
    <script type="text/javascript" src="../js/framework7-bundle.min.js"></script>
    <!-- Path to your app js-->
    <script type="text/javascript" src="../main_app.js"></script>

</body>

</html>