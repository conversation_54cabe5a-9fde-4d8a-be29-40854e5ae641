const jwt = require('../../services/jwt_service');

module.exports = {
  get: async function (req) {
    let response = {};

    try {
      const verify = jwt.verifyRefreshToken(req.body.token);

      if (!verify) {
        response = { status_code: 403, msg: 'Refresh Token not verified' };
      } else {
        delete verify.iat;
        delete verify.exp;
        delete verify.nbf;
        delete verify.jti; //We are generating a new token, if you are using jwtid during signing, pass it in refreshOptions
        response = {
          status_code: 200,
          access_token: jwt.createAccessToken(verify),
          refresh_token: jwt.createRefreshToken(verify),
        };
      }
    } catch (err) {
      console.log();
      response = { status_code: 403, msg: 'Failed  to create access token' };
    }

    return response;
  },
};
