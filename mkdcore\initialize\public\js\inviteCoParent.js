async function inviteCopatent() {
  var mainview = app.view.main;
  let inviteName = document.getElementById('inviteCoparent-name').value;
  let inviteEmail = document.getElementById('inviteCoparent-email').value;
  let invitePhoneIn = document.getElementById('inviteCoparent-phone').value;
  let invitePhone = invitePhoneIn.replace(/\s+/g, '');
  let cohortID = this.store.getters.cohortID.value;
  console.log('teteataetaetas');
  try {
    app.preloader.show();

    if (inviteCoParentValidateField(inviteEmail, invitePhone, inviteName)) {
      let body = {
        name: inviteName,
        email: inviteEmail,
        phone: '+1' + invitePhone,
        cohort_id: cohortID,
      };
      let response = await fetch('/api/user/invite', {
        method: 'POST',
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
      });
      let resJSON = await response.json();
      if (response.status == 200) {
        app.preloader.hide();

        app.dialog.alert('Invitation has been sent', 'Success');
        mainview.router.navigate({ name: 'sharedPhone' });
      }
      if (response.status == 400) {
        app.preloader.hide();

        throw 'Invalid number, please enter a valid number';
      }
    } else {
      app.preloader.hide();
      console.log(response);
      throw 'Something went wrong, please try again';
    }
  } catch (error) {
    app.preloader.hide();
    app.dialog.alert(error);
  }
}

function inviteCoParentValidateField(email, phone, name) {
  var AllFieldsValidated = true;
  var errortoThrow = 'These field(s) are required: <br>';
  if (email == '') {
    errortoThrow += `Co-Parent's Email Address <br>`;
    AllFieldsValidated = false;
  }
  if (phone == '') {
    errortoThrow += `Co-Parent's Phone Number<br>`;
    AllFieldsValidated = false;
  }
  if (name == '') {
    errortoThrow += `Co-Parent's Name<br>`;
    AllFieldsValidated = false;
  }
  if (AllFieldsValidated == false) {
    throw errortoThrow;
  }
  return AllFieldsValidated;
}
