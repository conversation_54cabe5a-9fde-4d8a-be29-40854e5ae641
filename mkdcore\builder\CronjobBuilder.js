/*Powered By: Manaknightdigital Inc. https://manaknightdigital.com/ Year: 2021*/
/**
 * Copy builder
 * @copyright 2021 Manaknightdigital Inc.
 * @link https://manaknightdigital.com
 * @license Proprietary Software licensing
 * <AUTHOR>
 *
 */

const fs = require('fs');
const Builder = require('./Builder');

module.exports = function () {
  Builder.call(this);

  this._render_list = [];
  this._cronjobs = [];

  this.set_cronjobs = function (cronjob) {
    this._cronjobs = cronjob;
  };

  this.build = function () {
    try {
      for (const cronjob in this._cronjobs) {
        const cronjob_template = fs.readFileSync(
          `../mkdcore/source/cronjob/${cronjob}`,
          'utf8',
        );

        this._render_list[
          `../../release/cronjobs/${cronjob}`
        ] = cronjob_template;
      }

      for (const key in this._render_list) {
        const page = this._render_list[key];
        this.writeFileSyncRecursive(key, page, { mode: 0775 });
      }
      console.log('Cronjob build success');
    } catch (error) {
      console.log('Cronjob build Error', error);
    }
  };

  this.destroy = function () {};
};
